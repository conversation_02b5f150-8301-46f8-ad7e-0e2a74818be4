using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.Manager;
using PlayerRoles;
using System;
using System.Collections.Generic;
using System.Linq;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Enum;
using BlackRoseServer.Helper;
using MEC;

namespace BlackRoseServer.Display
{
    /// <summary>
    /// SCP-079专用的简化阵营显示系统
    /// 位于右上角，简洁设计，无血量条等复杂元素
    /// </summary>
    public class SCP079TeammateDisplay
    {
        private static SCP079TeammateDisplay _instance;
        public static SCP079TeammateDisplay Instance => _instance ??= new SCP079TeammateDisplay();

        private SCP079TeammateDisplay() { }

        /// <summary>
        /// 为SCP-079显示简化的SCP阵营信息
        /// </summary>
        /// <param name="scp079Player">SCP-079玩家</param>
        public void ShowSCP079TeammateInfo(Player scp079Player)
        {
            if (scp079Player == null || scp079Player.Role != RoleTypeId.Scp079)
                return;

            // 使用延迟执行来确保Hint清理完成
            Timing.CallDelayed(0.1f, () => ShowSCP079TeammateInfoDelayed(scp079Player));
        }

        /// <summary>
        /// 延迟显示SCP-079阵营信息（确保清理完成）
        /// </summary>
        /// <param name="scp079Player">SCP-079玩家</param>
        private void ShowSCP079TeammateInfoDelayed(Player scp079Player)
        {
            if (scp079Player == null || scp079Player.Role != RoleTypeId.Scp079)
                return;

            try
            {
                // 先强制清理旧的显示
                ForceCleanupSCP079Hints(scp079Player);
                RemoveSCP079TeammateInfo(scp079Player);

                // 获取所有SCP队友（排除自己）
                var teammates = GetSCPTeammates(scp079Player);
                if (!teammates.Any())
                    return;

                // 按优先级排序
                var sortedTeammates = teammates.OrderBy(t => GetRoleDisplayOrder(t.Role)).ToList();

                // 特殊处理SCP-0492数量统计
                var scp0492Count = teammates.Count(t => t.Role == RoleTypeId.Scp0492);
                var otherSCPs = teammates.Where(t => t.Role != RoleTypeId.Scp0492).ToList();

                int index = 0;
                const int maxDisplayItems = 6; // SCP-079显示更少的项目，保持简洁

                // 如果有SCP-0492，先显示数量统计
                if (scp0492Count > 0 && index < maxDisplayItems)
                {
                    ShowSCP0492Count(scp079Player, scp0492Count, index);
                    index++;
                }

                // 显示其他SCP，简化版本
                var sortedOtherSCPs = otherSCPs.OrderBy(t => GetRoleDisplayOrder(t.Role)).ToList();
                foreach (var teammate in sortedOtherSCPs)
                {
                    if (index >= maxDisplayItems) break;
                    ShowSimplifiedSCPInfo(scp079Player, teammate, index);
                    index++;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"显示SCP-079阵营信息失败: {ex.Message}");
                Logger.Debug($"ShowSCP079TeammateInfo详细错误: {ex}");
            }
        }

        /// <summary>
        /// 显示简化的单个SCP信息（无血量条）
        /// </summary>
        /// <param name="scp079Player">SCP-079玩家</param>
        /// <param name="teammate">队友信息</param>
        /// <param name="index">显示索引</param>
        private void ShowSimplifiedSCPInfo(Player scp079Player, SCP079TeammateInfo teammate, int index)
        {
            try
            {
                // 计算Y坐标：使用右上角区域，从200开始向下
                int yCoordinate = 200 + (index * 25);

                // 获取角色翻译和区域信息
                string roleTranslation = GetRoleTranslation(teammate.Role);
                string zoneInfo = GetZoneInfo(teammate.Zone);

                // 获取血量和护盾信息
                int currentHealth = (int)teammate.Health;
                int maxHealth = (int)teammate.MaxHealth;
                int totalShield = (int)(teammate.Player.ArtificialHealth + teammate.Player.HumeShield); // 总护盾值

                // 根据血量百分比确定血量颜色
                float healthPercent = (float)currentHealth / maxHealth;
                string healthColor = healthPercent > 0.6f ? "#00FF00" : // 绿色 > 60%
                                   healthPercent > 0.3f ? "#FFFF00" : // 黄色 30-60%
                                   "#FF0000"; // 红色 < 30%

                // 显示格式：SCP-项目(红色) 血量(颜色)/护盾(紫色) (区域)
                string healthText = $"<color={healthColor}>{currentHealth}</color>";
                string shieldText = totalShield > 0 ? $"/<color=#800080>{totalShield}</color>" : "";
                string scpText = $"<color=#FF0000>{roleTranslation}</color> {healthText}{shieldText} <color=#CCCCCC>({zoneInfo})</color>";

                var config = new HintConfig
                {
                    Type = HintType.Custom,
                    Text = scpText,
                    XCoordinate = 350f, // 右上角位置
                    YCoordinate = yCoordinate,
                    FontSize = 16, // 更小的字体
                    Alignment = HintAlignment.Right, // 右对齐
                    LifecycleType = HintLifecycleType.Persistent,
                    Priority = BlackRoseServer.Manager.HintPriority.Normal,
                    CustomId = $"scp079_teammate_{scp079Player.UserId}_{teammate.Role}_{index}",
                    AllowOverlap = true
                };

                HintManager.Instance.ShowHint(scp079Player, config);
            }
            catch (Exception ex)
            {
                Logger.Error($"显示简化SCP信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示SCP-0492数量统计（简化版）
        /// </summary>
        /// <param name="scp079Player">SCP-079玩家</param>
        /// <param name="count">SCP-0492数量</param>
        /// <param name="index">显示索引</param>
        private void ShowSCP0492Count(Player scp079Player, int count, int index)
        {
            try
            {
                int yCoordinate = 200 - (index * 30);

                string countText = $"<color=#8B4513>SCP-049-2</color> <color=#FFFF00>x{count}</color>";

                var config = new HintConfig
                {
                    Type = HintType.Custom,
                    Text = countText,
                    XCoordinate = 350f, // 右上角位置
                    YCoordinate = yCoordinate,
                    FontSize = 16, // 更小的字体
                    Alignment = HintAlignment.Right, // 右对齐
                    LifecycleType = HintLifecycleType.Persistent,
                    Priority = BlackRoseServer.Manager.HintPriority.Normal,
                    CustomId = $"scp079_0492_count_{scp079Player.UserId}_{index}",
                    AllowOverlap = true
                };

                HintManager.Instance.ShowHint(scp079Player, config);
            }
            catch (Exception ex)
            {
                Logger.Error($"显示SCP-0492数量失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 强制清理SCP-079的所有Hint（包括可能残留的）
        /// </summary>
        /// <param name="scp079Player">SCP-079玩家</param>
        private void ForceCleanupSCP079Hints(Player scp079Player)
        {
            if (scp079Player == null)
                return;

            try
            {
                // 强制清理Y=200-300范围内的所有Hint位置
                for (int y = 200; y <= 300; y += 10)
                {
                    HintManager.Instance.ClearPlayerPositions(scp079Player);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"强制清理SCP-079 Hint失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 移除SCP-079的阵营显示
        /// </summary>
        /// <param name="scp079Player">SCP-079玩家</param>
        public void RemoveSCP079TeammateInfo(Player scp079Player)
        {
            if (scp079Player == null)
                return;

            try
            {
                // 移除所有SCP-079相关的hint
                for (int i = 0; i < 10; i++) // 清理可能的所有索引
                {
                    // 清理队友显示
                    foreach (RoleTypeId role in Enum.GetValues(typeof(RoleTypeId)))
                    {
                        if (XHelper.IsSCP(role))
                        {
                            string customId = $"scp079_teammate_{scp079Player.UserId}_{role}_{i}";
                            HintManager.Instance.RemoveHintByCustomId(scp079Player, customId);
                        }
                    }

                    // 清理SCP-0492数量显示
                    string countId = $"scp079_0492_count_{scp079Player.UserId}_{i}";
                    HintManager.Instance.RemoveHintByCustomId(scp079Player, countId);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"清理SCP-079阵营显示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取SCP队友信息
        /// </summary>
        /// <param name="currentPlayer">当前SCP-079玩家</param>
        /// <returns>队友信息列表</returns>
        private List<SCP079TeammateInfo> GetSCPTeammates(Player currentPlayer)
        {
            var teammates = new List<SCP079TeammateInfo>();

            try
            {
                // 使用XHelper.PlayerList获取假人除外的玩家列表
                var allPlayers = XHelper.PlayerList.Where(p => p != null).ToList();

                foreach (var player in allPlayers)
                {
                    // 排除自己，只显示其他SCP
                    if (player.UserId == currentPlayer.UserId)
                        continue;

                    // 只显示存活的SCP
                    if (player.IsAlive && XHelper.IsSCP(player.Role))
                    {
                        var teammateInfo = new SCP079TeammateInfo
                        {
                            Player = player,
                            Role = player.Role,
                            Health = player.Health,
                            MaxHealth = player.MaxHealth,
                            Zone = GetPlayerZone(player)
                        };

                        teammates.Add(teammateInfo);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取SCP队友信息失败: {ex.Message}");
            }

            return teammates;
        }

        /// <summary>
        /// 获取角色显示优先级
        /// </summary>
        /// <param name="role">角色类型</param>
        /// <returns>优先级数字（越小优先级越高）</returns>
        private int GetRoleDisplayOrder(RoleTypeId role)
        {
            return role switch
            {
                RoleTypeId.Scp0492 => 1,  // SCP-049-2 最高优先级
                RoleTypeId.Scp079 => 2,   // SCP-079 第二优先级
                RoleTypeId.Scp3114 => 3,  // SCP-3114 第三优先级
                RoleTypeId.Scp049 => 4,   // SCP-049
                RoleTypeId.Scp096 => 5,   // SCP-096
                RoleTypeId.Scp106 => 6,   // SCP-106
                RoleTypeId.Scp173 => 7,   // SCP-173
                RoleTypeId.Scp939 => 8,   // SCP-939
                _ => 99 // 其他SCP
            };
        }

        /// <summary>
        /// 获取角色翻译
        /// </summary>
        /// <param name="role">角色类型</param>
        /// <returns>翻译后的角色名</returns>
        private string GetRoleTranslation(RoleTypeId role)
        {
            return role switch
            {
                RoleTypeId.Scp049 => "SCP-049",
                RoleTypeId.Scp0492 => "SCP-049-2",
                RoleTypeId.Scp079 => "SCP-079",
                RoleTypeId.Scp096 => "SCP-096",
                RoleTypeId.Scp106 => "SCP-106",
                RoleTypeId.Scp173 => "SCP-173",
                RoleTypeId.Scp3114 => "SCP-3114",
                RoleTypeId.Scp939 => "SCP-939",
                _ => role.ToString()
            };
        }

        /// <summary>
        /// 获取区域信息
        /// </summary>
        /// <param name="zone">区域类型</param>
        /// <returns>区域名称</returns>
        private string GetZoneInfo(ZoneDetector.ZoneType zone)
        {
            return zone switch
            {
                ZoneDetector.ZoneType.LightContainment => "轻收容",
                ZoneDetector.ZoneType.HeavyContainment => "重收容",
                ZoneDetector.ZoneType.Entrance => "入口区",
                ZoneDetector.ZoneType.Surface => "地表",
                _ => "未知"
            };
        }

        /// <summary>
        /// 获取玩家所在区域
        /// </summary>
        /// <param name="player">玩家</param>
        /// <returns>区域类型</returns>
        private ZoneDetector.ZoneType GetPlayerZone(Player player)
        {
            try
            {
                return ZoneDetector.GetPlayerZone(player);
            }
            catch
            {
                return ZoneDetector.ZoneType.Unknown;
            }
        }
    }

    /// <summary>
    /// SCP-079专用队友信息结构（简化版）
    /// </summary>
    public class SCP079TeammateInfo
    {
        public Player Player { get; set; }
        public RoleTypeId Role { get; set; }
        public float Health { get; set; }
        public float MaxHealth { get; set; }
        public ZoneDetector.ZoneType Zone { get; set; }
    }
}
