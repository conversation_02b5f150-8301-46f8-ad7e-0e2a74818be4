﻿using PlayerRoles;
using LabApi.Features.Wrappers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;

namespace BlackRoseServer.Helper.SCP
{
    public class DisconnectSCPHelper
    {
        public RoleTypeId RoleType;
        public float Health;
        public float AHealth;
        public Vector3 Postion;

        public string UserId;

        public DisconnectSCPHelper(Player Player)
        {
            RoleType = Player.Role;
            Health = Player.Health;
            AHealth = Player.ArtificialHealth;
            Postion = Player.Position;
            UserId = Player.UserId;
        }

        public void Reborn(Player Player)
        {
            Player.SetRole(RoleType , RoleChangeReason.RemoteAdmin);
            Player.Health = Health;
            Player.ArtificialHealth = AHealth;
            Player.Position = Postion;
            Plugin.PlayerDataService.DisconnectSCPs.TryRemove(UserId, out _);
        }
        public void FullyReborn(Player Player)
        {
            Player.SetRole(RoleType, RoleChangeReason.RemoteAdmin);
            Plugin.PlayerDataService.DisconnectSCPs.TryRemove(UserId, out _);
        }
    }
}
