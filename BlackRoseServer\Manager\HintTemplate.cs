using System;
using System.Collections.Generic;
using HintServiceMeow.Core.Enum;

namespace BlackRoseServer.Manager
{
    /// <summary>
    /// Hint模板系统
    /// </summary>
    public static class HintTemplate
    {
        /// <summary>
        /// 预定义的Hint模板
        /// </summary>
        private static readonly Dictionary<HintType, HintConfig> Templates = new Dictionary<HintType, HintConfig>
        {

            
            [HintType.LevelUp] = new HintConfig
            {
                Type = HintType.LevelUp,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.LevelUp,
                FontSize = 25,
                LineHeight = 5,
                Priority = HintPriority.High,
                LifecycleType = HintLifecycleType.Temporary,
                Duration = 15f,
                AllowOverlap = false
            },
            
            [HintType.SCP914Info] = new HintConfig
            {
                Type = HintType.SCP914Info,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.Interaction,
                FontSize = 20,
                LineHeight = 5,
                Priority = HintPriority.Normal,
                LifecycleType = HintLifecycleType.Temporary,
                Duration = 5f,
                AllowOverlap = true
            },
            
            [HintType.ElevatorInteraction] = new HintConfig
            {
                Type = HintType.ElevatorInteraction,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.Interaction,
                FontSize = 20,
                LineHeight = 5,
                Priority = HintPriority.Normal,
                LifecycleType = HintLifecycleType.Temporary,
                Duration = 3f,
                AllowOverlap = true
            },
            
            [HintType.SecurityOffDuty] = new HintConfig
            {
                Type = HintType.SecurityOffDuty,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.Notification,
                FontSize = 20,
                LineHeight = 5,
                Priority = HintPriority.Normal,
                LifecycleType = HintLifecycleType.Temporary,
                Duration = 10f,
                DelayTime = 0.5f,
                AllowOverlap = false
            },
            
            [HintType.Timer] = new HintConfig
            {
                Type = HintType.Timer,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.Timer,
                FontSize = 35,
                LineHeight = 5,
                SyncSpeed = HintSyncSpeed.Fast,
                Priority = HintPriority.High,
                LifecycleType = HintLifecycleType.AutoUpdate,
                AllowOverlap = false
            },
            
            [HintType.ExperienceBar] = new HintConfig
            {
                Type = HintType.ExperienceBar,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.ExperienceBar,
                FontSize = 20,
                LineHeight = 5,
                Priority = HintPriority.Low,
                LifecycleType = HintLifecycleType.Persistent,
                AllowOverlap = false
            },
            
            [HintType.Chat] = new HintConfig
            {
                Type = HintType.Chat,
                Alignment = HintAlignment.Left,
                YCoordinate = HintLayers.Chat,
                FontSize = 25,
                LineHeight = 5,
                Priority = HintPriority.Normal,
                LifecycleType = HintLifecycleType.Persistent,
                AllowOverlap = false
            },
            
            [HintType.Leaderboard] = new HintConfig
            {
                Type = HintType.Leaderboard,
                Alignment = HintAlignment.Right,
                YCoordinate = HintLayers.Leaderboard,
                FontSize = 25,
                LineHeight = 5,
                Priority = HintPriority.Normal,
                LifecycleType = HintLifecycleType.AutoUpdate,
                AllowOverlap = false
            },
            
            [HintType.Spectator] = new HintConfig
            {
                Type = HintType.Spectator,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.Spectator,
                FontSize = 20,
                LineHeight = 5,
                Priority = HintPriority.Normal,
                LifecycleType = HintLifecycleType.AutoUpdate,
                AllowOverlap = false
            },
            
            [HintType.Notification] = new HintConfig
            {
                Type = HintType.Notification,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.Notification,
                FontSize = 20,
                LineHeight = 5,
                Priority = HintPriority.Normal,
                LifecycleType = HintLifecycleType.Temporary,
                Duration = 5f,
                AllowOverlap = false
            },
            
            [HintType.Warning] = new HintConfig
            {
                Type = HintType.Warning,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.Notification,
                FontSize = 22,
                LineHeight = 5,
                Priority = HintPriority.High,
                LifecycleType = HintLifecycleType.Temporary,
                Duration = 8f,
                AllowOverlap = false
            },
            
            [HintType.Error] = new HintConfig
            {
                Type = HintType.Error,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.Critical,
                FontSize = 24,
                LineHeight = 5,
                Priority = HintPriority.Critical,
                LifecycleType = HintLifecycleType.Temporary,
                Duration = 10f,
                AllowOverlap = false
            },
            
            [HintType.Success] = new HintConfig
            {
                Type = HintType.Success,
                Alignment = HintAlignment.Center,
                YCoordinate = HintLayers.Notification,
                FontSize = 20,
                LineHeight = 5,
                Priority = HintPriority.Normal,
                LifecycleType = HintLifecycleType.Temporary,
                Duration = 5f,
                AllowOverlap = false
            }
        };

        /// <summary>
        /// 获取指定类型的模板配置
        /// </summary>
        /// <param name="type">Hint类型</param>
        /// <returns>模板配置的克隆</returns>
        public static HintConfig GetTemplate(HintType type)
        {
            if (Templates.TryGetValue(type, out var template))
            {
                return template.Clone();
            }
            
            // 如果没有找到模板，返回默认配置
            return HintConfig.Default;
        }



        /// <summary>
        /// 创建升级显示配置
        /// </summary>
        /// <param name="playerName">玩家名称</param>
        /// <param name="levelBefore">升级前等级</param>
        /// <param name="levelAfter">升级后等级</param>
        /// <returns>配置好的HintConfig</returns>
        public static HintConfig CreateLevelUp(string playerName, int levelBefore, int levelAfter)
        {
            var config = GetTemplate(HintType.LevelUp);
            config.Text = $"<color=#FFD700><size=30><b>恭喜升级！</b></size></color>\n<color=#FFFF00>{playerName}</color>\n<color=#FFFFFF>{levelBefore} → {levelAfter}</color>";
            return config;
        }

        /// <summary>
        /// 创建保安下班提示配置
        /// </summary>
        /// <returns>配置好的HintConfig</returns>
        public static HintConfig CreateSecurityOffDuty()
        {
            var config = GetTemplate(HintType.SecurityOffDuty);
            config.Text = "<color=#00FF00>下班成功！</color>\n<color=#FFFF00>请在5秒内清空背包格数到你逃离前所拥有的物品数量</color>";
            return config;
        }

        /// <summary>
        /// 创建通知配置
        /// </summary>
        /// <param name="message">通知消息</param>
        /// <param name="color">颜色（可选）</param>
        /// <returns>配置好的HintConfig</returns>
        public static HintConfig CreateNotification(string message, string color = "#FFFFFF")
        {
            var config = GetTemplate(HintType.Notification);
            config.Text = $"<color={color}>{message}</color>";
            return config;
        }

        /// <summary>
        /// 创建警告配置
        /// </summary>
        /// <param name="message">警告消息</param>
        /// <returns>配置好的HintConfig</returns>
        public static HintConfig CreateWarning(string message)
        {
            var config = GetTemplate(HintType.Warning);
            config.Text = $"<color=#FFA500><b>⚠️ 警告</b></color>\n<color=#FFFF00>{message}</color>";
            return config;
        }

        /// <summary>
        /// 创建错误配置
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>配置好的HintConfig</returns>
        public static HintConfig CreateError(string message)
        {
            var config = GetTemplate(HintType.Error);
            config.Text = $"<color=#FF0000><b>❌ 错误</b></color>\n<color=#FFFFFF>{message}</color>";
            return config;
        }

        /// <summary>
        /// 创建成功配置
        /// </summary>
        /// <param name="message">成功消息</param>
        /// <returns>配置好的HintConfig</returns>
        public static HintConfig CreateSuccess(string message)
        {
            var config = GetTemplate(HintType.Success);
            config.Text = $"<color=#00FF00><b>✅ 成功</b></color>\n<color=#FFFFFF>{message}</color>";
            return config;
        }
    }
}
