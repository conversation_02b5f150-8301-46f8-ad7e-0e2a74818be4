using System;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.Manager;
using BlackRoseServer.Helper;
using PlayerRoles;

namespace BlackRoseServer.Display
{
    /// <summary>
    /// SCP阵营队友显示系统
    /// </summary>
    public class SCPTeammateDisplay
    {
        /// <summary>
        /// SCP队友信息
        /// </summary>
        public class SCPTeammateInfo
        {
            public Player Player { get; set; }
            public RoleTypeId Role { get; set; }
            public float Health { get; set; }
            public float MaxHealth { get; set; }
            public float Shield { get; set; }
            public float MaxShield { get; set; }
            public ZoneDetector.ZoneType Zone { get; set; }
            public bool IsAlive { get; set; }
        }

        /// <summary>
        /// 显示SCP队友信息
        /// </summary>
        /// <param name="player">当前SCP玩家</param>
        public void ShowSCPTeammateInfo(Player player)
        {
            if (player == null || !player.IsSCP)
                return;

            try
            {
                // 先移除旧的显示，避免冲突
                RemoveSCPTeammateInfo(player);

                // 获取所有SCP队友
                var teammates = GetSCPTeammates(player);
                if (!teammates.Any())
                    return;

                // 按照优先级排序所有SCP，包括SCP-0492
                var allSCPs = teammates.OrderBy(t => GetRoleDisplayOrder(t.Role)).ToList();

                // 特殊处理：统计SCP-0492数量
                var scp0492Count = teammates.Count(t => t.Role == RoleTypeId.Scp0492);
                var otherSCPs = teammates.Where(t => t.Role != RoleTypeId.Scp0492).ToList();

                int index = 0;
                const int maxDisplayItems = 8; // 最多显示8个项目

                // 如果有SCP-0492，先显示数量统计（优先级最高）
                if (scp0492Count > 0 && index < maxDisplayItems)
                {
                    ShowSCP0492CountHint(player, scp0492Count, index);
                    index++;
                }

                // 显示其他SCP，按优先级排序
                var sortedOtherSCPs = otherSCPs.OrderBy(t => GetRoleDisplayOrder(t.Role)).ToList();
                foreach (var teammate in sortedOtherSCPs)
                {
                    if (index >= maxDisplayItems) break;
                    ShowIndividualSCPHint(player, teammate, index);
                    index++;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"显示SCP队友信息失败: {ex.Message}");
                Logger.Debug($"ShowSCPTeammateInfo详细错误: {ex}");
            }
        }

        /// <summary>
        /// 显示单个SCP的独立Hint
        /// </summary>
        /// <param name="viewer">观看者</param>
        /// <param name="teammate">队友信息</param>
        /// <param name="index">显示索引</param>
        private void ShowIndividualSCPHint(Player viewer, SCPTeammateInfo teammate, int index)
        {
            try
            {
                // 构建单个SCP的显示文本
                string scpText = BuildIndividualSCPText(teammate);

                // 计算Y坐标：基础920，每个SCP向下偏移60像素（修复显示顺序）
                int yCoordinate = 920 - (index * 60);

                var config = new HintConfig
                {
                    Type = HintType.Custom,
                    Text = scpText,
                    XCoordinate = -350f, // 设置负的X坐标，让文本更靠左
                    YCoordinate = yCoordinate, // 每个SCP独立的Y坐标
                    FontSize = 25, // 适中的字体大小
                    Alignment = HintServiceMeow.Core.Enum.HintAlignment.Left, // 最左边对齐
                    LifecycleType = HintLifecycleType.Persistent,
                    Priority = HintPriority.Normal,
                    CustomId = $"scp_individual_{viewer.UserId}_{teammate.Role}_{index}", // 每个SCP独立的ID
                    AllowOverlap = true // 允许重叠
                };

                // 显示单个SCP信息
                HintManager.Instance.ShowHint(viewer, config);
            }
            catch (Exception ex)
            {
                Logger.Error($"显示单个SCP Hint失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示SCP-0492数量统计的独立Hint（简化显示）
        /// </summary>
        /// <param name="viewer">观看者</param>
        /// <param name="count">SCP-0492数量</param>
        /// <param name="index">显示索引</param>
        private void ShowSCP0492CountHint(Player viewer, int count, int index)
        {
            try
            {
                // 简化显示：角色名称 + 数量
                string countText = $"👤<color=#FF0000>SCP-049-2</color> <color=#FFFF00>x{count}</color>";

                // 计算Y坐标：基础920，每个SCP向上偏移60像素
                int yCoordinate = 920 - (index * 60);

                var config = new HintConfig
                {
                    Type = HintType.Custom,
                    Text = countText,
                    XCoordinate = -350f, // 设置负的X坐标，让文本更靠左
                    YCoordinate = yCoordinate, // 每个SCP独立的Y坐标
                    FontSize = 25, // 适中的字体大小
                    Alignment = HintServiceMeow.Core.Enum.HintAlignment.Left, // 最左边对齐
                    LifecycleType = HintLifecycleType.Persistent,
                    Priority = HintPriority.Normal,
                    CustomId = $"scp_0492_count_{viewer.UserId}_{index}", // SCP-0492数量的独立ID
                    AllowOverlap = true // 允许重叠
                };

                // 显示SCP-0492数量信息
                HintManager.Instance.ShowHint(viewer, config);
            }
            catch (Exception ex)
            {
                Logger.Error($"显示SCP-0492数量Hint失败: {ex.Message}");
            }
        }



        /// <summary>
        /// 构建单个SCP的显示文本
        /// </summary>
        /// <param name="teammate">队友信息</param>
        /// <returns>单个SCP的显示文本</returns>
        private string BuildIndividualSCPText(SCPTeammateInfo teammate)
        {
            try
            {
                // 获取角色颜色和名称
                var roleColor = GetRoleColor(teammate.Role);
                var roleName = GetRoleDisplayName(teammate.Role);
                var zoneInfo = ZoneDetector.GetZoneName(teammate.Zone);

                if (teammate.Role == RoleTypeId.Scp079)
                {
                    // SCP-079特殊显示：角色名(区域) + 电量 + 等级
                    var energyPercent = GetSCP079Energy(teammate.Player);
                    var tier = GetSCP079Level(teammate.Player);
                    var energyColor = GetHealthColor(energyPercent); // 复用血量颜色方法

                    return $"👤<color=#FF0000>{roleName}</color>\n✚<color={energyColor}>电量: {energyPercent:F0}% | 等级: {tier}</color>";
                }
                else
                {
                    // 其他SCP显示：角色名(区域) 护盾：数值 + 血量条 + 百分比 (血量)
                    var healthPercent = teammate.MaxHealth > 0 ? (teammate.Health / teammate.MaxHealth) * 100 : 0;
                    var healthBar = BuildHealthBar(healthPercent);
                    var healthColor = GetHealthColor(healthPercent);

                    // 获取护盾信息
                    var shieldValue = GetPlayerShield(teammate.Player);
                    var shieldInfo = shieldValue > 0 ? $" <color=#9966FF>护盾：{shieldValue:F0}</color>" : "";

                    return $"👤<color=#FF0000>{roleName}</color> <color=#D3D3D3>({zoneInfo})</color>{shieldInfo}\n✚{healthBar} <color={healthColor}>{healthPercent:F0}% ({teammate.Health:F0})</color>";
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"构建单个SCP文本失败: {ex.Message}");
                return $"<color=#FF0000>SCP信息错误</color>";
            }
        }

        /// <summary>
        /// 构建SCP队友信息文本
        /// </summary>
        /// <param name="currentPlayer">当前SCP玩家</param>
        /// <returns>队友信息文本</returns>
        private string BuildSCPTeammateInfoText(Player currentPlayer)
        {
            try
            {
                var infoLines = new List<string>();
                var teammates = GetSCPTeammates(currentPlayer);

                if (teammates.Count == 0)
                {
                    return "<color=#D3D3D3>无其他SCP</color>";
                }

                // 移除"SCP阵营"标题，直接显示SCP信息

                // 按优先级排序：SCP-079最上方，然后按角色编号排序
                var sortedTeammates = teammates.OrderBy(t => GetSCPDisplayPriority(t.Role)).ToList();

                foreach (var teammate in sortedTeammates)
                {
                    if (teammate.IsAlive)
                    {
                        var scpInfo = BuildDetailedSCPInfo(teammate);
                        infoLines.Add(scpInfo);
                    }
                }

                return string.Join("\n", infoLines);
            }
            catch (Exception ex)
            {
                Logger.Error($"构建SCP队友信息文本失败: {ex.Message}");
                return "<color=#FF0000>队友信息获取失败</color>";
            }
        }

        /// <summary>
        /// 获取SCP显示优先级（数字越小优先级越高）
        /// </summary>
        private int GetSCPDisplayPriority(RoleTypeId role)
        {
            return role switch
            {
                RoleTypeId.Scp079 => 0, // SCP-079最上方
                RoleTypeId.Scp173 => 1,
                RoleTypeId.Scp106 => 2,
                RoleTypeId.Scp096 => 3,
                RoleTypeId.Scp049 => 4,
                RoleTypeId.Scp939 => 5,
                RoleTypeId.Scp0492 => 6,
                _ => 99
            };
        }

        /// <summary>
        /// 构建详细的SCP信息显示
        /// </summary>
        /// <param name="teammate">队友信息</param>
        /// <returns>详细的SCP信息字符串</returns>
        private string BuildDetailedSCPInfo(SCPTeammateInfo teammate)
        {
            try
            {
                var roleName = GetRoleDisplayName(teammate.Role);
                var roleColor = GetRoleColor(teammate.Role);
                var zoneInfo = ZoneDetector.GetColoredZoneName(teammate.Zone);

                if (teammate.Role == RoleTypeId.Scp079)
                {
                    // SCP-079特殊显示：电量和等级，不显示位置
                    var energy = GetSCP079Energy(teammate.Player);
                    var level = GetSCP079Level(teammate.Player);
                    return $"<color=#FF0000>{roleName}</color>\n电量:<color=#00FFFF>{energy:F0}%</color> 等级:<color=#FFD700>{level}</color>";
                }
                else
                {
                    // 其他SCP显示：角色名(区域) + 血量条 + 数值
                    var healthPercent = teammate.MaxHealth > 0 ? (teammate.Health / teammate.MaxHealth) * 100 : 0;
                    var healthBar = BuildHealthBar(healthPercent);
                    var healthColor = GetHealthColor(healthPercent);

                    var line1 = $"<color=#FF0000>{roleName}</color> <color=#D3D3D3>({zoneInfo})</color>";
                    var line2 = $"{healthBar} <color={healthColor}>{healthPercent:F0}% ({teammate.Health:F0})</color>";

                    // 如果有护盾，添加护盾信息
                    if (teammate.Shield > 0)
                    {
                        line2 += $"/<color=#9966CC>{teammate.Shield:F0}</color>";
                    }

                    return $"{line1}\n{line2}";
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"构建详细SCP信息失败: {ex.Message}");
                return "<color=#FF0000>信息错误</color>";
            }
        }

        /// <summary>
        /// 构建血量条显示
        /// </summary>
        /// <param name="healthPercent">血量百分比</param>
        /// <returns>血量条字符串</returns>
        private string BuildHealthBar(float healthPercent)
        {
            const int barLength = 8; // 8个方块（增加3个格子）
            int filledBlocks = (int)Math.Round(healthPercent / 12.5f); // 每12.5%一个方块
            filledBlocks = Math.Max(0, Math.Min(barLength, filledBlocks));

            var healthColor = GetHealthColor(healthPercent);
            var filledBar = new string('■', filledBlocks);
            var emptyBar = new string('□', barLength - filledBlocks);

            return $"<color={healthColor}>[{filledBar}</color><color=#666666>{emptyBar}]</color>";
        }



        /// <summary>
        /// 获取SCP队友列表
        /// </summary>
        /// <param name="currentPlayer">当前玩家</param>
        /// <returns>队友信息列表</returns>
        private List<SCPTeammateInfo> GetSCPTeammates(Player currentPlayer)
        {
            var teammates = new List<SCPTeammateInfo>();

            try
            {
                // 使用XHelper.PlayerList获取假人除外的玩家列表
                var allPlayers = XHelper.PlayerList.Where(p => p != null && p.IsReady).ToList();
                // Logger.Debug($"检查SCP队友 - 当前玩家: {currentPlayer.Nickname}, 总玩家数: {allPlayers.Count}");

                foreach (var player in allPlayers)
                {
                    if (player == null)
                        continue;

                    // Logger.Debug($"检查玩家: {player.Nickname}, 角色: {player.Role}, 是否SCP: {player.IsSCP}, 是否存活: {player.IsAlive}");

                    if (!player.IsSCP || !player.IsAlive)
                        continue;

                    var teammateInfo = new SCPTeammateInfo
                    {
                        Player = player,
                        Role = player.Role,
                        Health = player.Health,
                        MaxHealth = player.MaxHealth,
                        Shield = GetPlayerShield(player),
                        MaxShield = GetPlayerMaxShield(player),
                        Zone = ConvertToZoneType(player.Zone), // 使用LabAPI的Zone属性
                        IsAlive = player.IsAlive
                    };

                    teammates.Add(teammateInfo);
                    // Logger.Debug($"添加SCP队友: {player.Nickname} ({player.Role})");
                }

                // Logger.Debug($"找到 {teammates.Count} 个SCP队友");
            }
            catch (Exception ex)
            {
                Logger.Error($"获取SCP队友列表失败: {ex.Message}");
                Logger.Debug($"GetSCPTeammates详细错误: {ex}");
            }

            return teammates;
        }

        /// <summary>
        /// 获取角色显示信息
        /// </summary>
        /// <param name="teammate">队友信息</param>
        /// <returns>显示信息字符串</returns>
        private string GetRoleDisplayInfo(SCPTeammateInfo teammate)
        {
            try
            {
                var roleName = GetRoleDisplayName(teammate.Role);
                var roleColor = GetRoleColor(teammate.Role);
                var zoneInfo = ZoneDetector.GetColoredZoneName(teammate.Zone);

                // 根据SCP类型显示不同信息
                if (teammate.Role == RoleTypeId.Scp079)
                {
                    // SCP-079显示电量和等级，不显示位置
                    var energy = GetSCP079Energy(teammate.Player);
                    var level = GetSCP079Level(teammate.Player);
                    return $"<color=#FF0000>[{roleName}]</color> 电量：<color=#00FFFF>{energy:F0}%</color> 等级：<color=#FFD700>{level}</color>";
                }
                else
                {
                    // 其他SCP显示血量/护盾和位置
                    var healthInfo = GetHealthDisplayInfo(teammate);
                    return $"<color=#FF0000>[{roleName}]</color> {healthInfo} <color=#D3D3D3>({zoneInfo})</color>";
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取角色显示信息失败: {ex.Message}");
                return "<color=#FF0000>信息错误</color>";
            }
        }

        /// <summary>
        /// 获取血量显示信息
        /// </summary>
        /// <param name="teammate">队友信息</param>
        /// <returns>血量显示字符串</returns>
        private string GetHealthDisplayInfo(SCPTeammateInfo teammate)
        {
            try
            {
                var healthPercent = teammate.MaxHealth > 0 ? (teammate.Health / teammate.MaxHealth) * 100 : 0;
                var healthColor = GetHealthColor(healthPercent);

                // 显示格式：血量/护盾
                string healthText = $"<color={healthColor}>{teammate.Health:F0}</color>";

                // 如果有护盾，显示为 血量/护盾 格式（护盾固定紫色）
                if (teammate.Shield > 0)
                {
                    healthText += $"/<color=#9966CC>{teammate.Shield:F0}</color>";
                }

                return healthText;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取血量显示信息失败: {ex.Message}");
                return "<color=#FF0000>HP错误</color>";
            }
        }

        /// <summary>
        /// 获取玩家护盾值
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>护盾值</returns>
        private float GetPlayerShield(Player player)
        {
            try
            {
                if (player == null)
                {
                    return 0f;
                }

                // 使用LabAPI的护盾属性
                // 对于SCP，主要使用HumeShield（Hume护盾）
                // 对于人类，使用ArtificialHealth（人工血量/AHP）
                if (player.Role.ToString().StartsWith("Scp"))
                {
                    // SCP使用Hume护盾
                    return player.HumeShield;
                }
                else
                {
                    // 人类使用人工血量
                    return player.ArtificialHealth;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家护盾失败 - 玩家: {player?.Nickname ?? "null"}, 角色: {player?.Role ?? RoleTypeId.None}, 错误: {ex.Message}");
                return 0f;
            }
        }

        /// <summary>
        /// 获取玩家最大护盾值
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>最大护盾值</returns>
        private float GetPlayerMaxShield(Player player)
        {
            try
            {
                if (player == null)
                {
                    return 0f;
                }

                // 使用LabAPI的最大护盾属性
                if (player.Role.ToString().StartsWith("Scp"))
                {
                    // SCP使用最大Hume护盾
                    return player.MaxHumeShield;
                }
                else
                {
                    // 人类使用最大人工血量
                    return player.MaxArtificialHealth;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家最大护盾失败: {ex.Message}");
                return 0f;
            }
        }

        /// <summary>
        /// 获取角色显示名称
        /// </summary>
        /// <param name="role">角色类型</param>
        /// <returns>显示名称</returns>
        private string GetRoleDisplayName(RoleTypeId role)
        {
            return role switch
            {
                RoleTypeId.Scp173 => "SCP-173",
                RoleTypeId.Scp106 => "SCP-106",
                RoleTypeId.Scp049 => "SCP-049",
                RoleTypeId.Scp0492 => "SCP-049-2",
                RoleTypeId.Scp096 => "SCP-096",
                RoleTypeId.Scp939 => "SCP-939",
                RoleTypeId.Scp079 => "SCP-079",
                _ => role.ToString()
            };
        }

        /// <summary>
        /// 获取角色颜色
        /// </summary>
        /// <param name="role">角色类型</param>
        /// <returns>颜色代码</returns>
        private string GetRoleColor(RoleTypeId role)
        {
            return role switch
            {
                RoleTypeId.Scp173 => "#8B4513", // 棕色
                RoleTypeId.Scp106 => "#2F4F2F", // 深绿色
                RoleTypeId.Scp049 => "#000000", // 黑色
                RoleTypeId.Scp0492 => "#8B4513", // 棕色
                RoleTypeId.Scp096 => "#F5DEB3", // 米色
                RoleTypeId.Scp939 => "#DC143C", // 深红色
                RoleTypeId.Scp079 => "#FF6600", // 橙色
                _ => "#FFFFFF"
            };
        }

        /// <summary>
        /// 获取血量颜色
        /// </summary>
        /// <param name="healthPercent">血量百分比</param>
        /// <returns>颜色代码</returns>
        private string GetHealthColor(float healthPercent)
        {
            if (healthPercent > 75f)
                return "#00FF00"; // 绿色
            else if (healthPercent > 50f)
                return "#FFFF00"; // 黄色
            else if (healthPercent > 25f)
                return "#FFA500"; // 橙色
            else
                return "#FF0000"; // 红色
        }

        /// <summary>
        /// 获取护盾颜色
        /// </summary>
        /// <param name="shieldPercent">护盾百分比</param>
        /// <returns>颜色代码</returns>
        private string GetShieldColor(float shieldPercent)
        {
            if (shieldPercent > 75f)
                return "#00BFFF"; // 深天蓝色
            else if (shieldPercent > 50f)
                return "#87CEEB"; // 天蓝色
            else if (shieldPercent > 25f)
                return "#B0C4DE"; // 浅钢蓝色
            else
                return "#778899"; // 浅石板灰色
        }

        /// <summary>
        /// 获取角色显示顺序
        /// </summary>
        /// <param name="role">角色类型</param>
        /// <returns>显示顺序</returns>
        private int GetRoleDisplayOrder(RoleTypeId role)
        {
            return role switch
            {
                RoleTypeId.Scp0492 => 1, // SCP-049-2最优先
                RoleTypeId.Scp3114 => 2, // SCP-3114第二
                RoleTypeId.Scp173 => 3, // SCP-173第三
                RoleTypeId.Scp049 => 4, // SCP-049第四
                RoleTypeId.Scp106 => 5, // SCP-106第五
                RoleTypeId.Scp079 => 6, // SCP-079第六
                RoleTypeId.Scp096 => 7, // SCP-096第七
                RoleTypeId.Scp939 => 8, // SCP-939第八
                _ => 999
            };
        }

        /// <summary>
        /// 获取SCP-079电量
        /// </summary>
        /// <param name="player">SCP-079玩家</param>
        /// <returns>电量百分比</returns>
        private float GetSCP079Energy(Player player)
        {
            try
            {
                if (player?.Role != RoleTypeId.Scp079) return 0f;

                // 获取SCP-079的电量信息
                if (player.ReferenceHub?.roleManager?.CurrentRole is PlayerRoles.PlayableScps.Scp079.Scp079Role scp079Role)
                {
                    var currentEnergy = scp079Role.SubroutineModule.TryGetSubroutine<PlayerRoles.PlayableScps.Scp079.Scp079AuxManager>(out var auxManager)
                        ? auxManager.CurrentAux
                        : 0f;
                    var maxEnergy = auxManager?.MaxAux ?? 100f;

                    return (currentEnergy / maxEnergy) * 100f;
                }

                return 0f;
            }
            catch (Exception ex)
            {
                Logger.Debug($"获取SCP-079电量失败: {ex.Message}");
                return 0f;
            }
        }

        /// <summary>
        /// 获取SCP-079等级
        /// </summary>
        /// <param name="player">SCP-079玩家</param>
        /// <returns>访问等级</returns>
        private int GetSCP079Level(Player player)
        {
            try
            {
                if (player?.Role != RoleTypeId.Scp079) return 0;

                // 获取SCP-079的等级信息
                if (player.ReferenceHub?.roleManager?.CurrentRole is PlayerRoles.PlayableScps.Scp079.Scp079Role scp079Role)
                {
                    if (scp079Role.SubroutineModule.TryGetSubroutine<PlayerRoles.PlayableScps.Scp079.Scp079TierManager>(out var tierManager))
                    {
                        return tierManager.AccessTierLevel;
                    }
                }

                return 1;
            }
            catch (Exception ex)
            {
                Logger.Debug($"获取SCP-079等级失败: {ex.Message}");
                return 1;
            }
        }

        /// <summary>
        /// 转换LabAPI的FacilityZone到自定义的ZoneType
        /// </summary>
        /// <param name="facilityZone">LabAPI的区域类型</param>
        /// <returns>自定义区域类型</returns>
        private ZoneDetector.ZoneType ConvertToZoneType(MapGeneration.FacilityZone facilityZone)
        {
            switch (facilityZone)
            {
                case MapGeneration.FacilityZone.LightContainment:
                    return ZoneDetector.ZoneType.LightContainment;
                case MapGeneration.FacilityZone.HeavyContainment:
                    return ZoneDetector.ZoneType.HeavyContainment;
                case MapGeneration.FacilityZone.Entrance:
                    return ZoneDetector.ZoneType.Entrance;
                case MapGeneration.FacilityZone.Surface:
                    return ZoneDetector.ZoneType.Surface;
                case MapGeneration.FacilityZone.Other:
                    return ZoneDetector.ZoneType.Unknown; // Other映射到Unknown
                default:
                    return ZoneDetector.ZoneType.Unknown;
            }
        }

        /// <summary>
        /// 移除SCP队友信息显示
        /// </summary>
        /// <param name="player">玩家对象</param>
        public void RemoveSCPTeammateInfo(Player player)
        {
            if (player == null)
                return;

            try
            {
                // 移除所有独立的SCP Hint
                // 由于我们限制最多8个项目，尝试移除可能的所有索引
                for (int i = 0; i < 8; i++) // 最多8个项目
                {
                    foreach (RoleTypeId role in Enum.GetValues(typeof(RoleTypeId)))
                    {
                        if (role.ToString().StartsWith("Scp"))
                        {
                            var customId = $"scp_individual_{player.UserId}_{role}_{i}";
                            HintManager.Instance.RemoveHintByCustomId(player, customId);
                        }
                    }

                    // 移除SCP-0492数量Hint
                    var countCustomId = $"scp_0492_count_{player.UserId}_{i}";
                    HintManager.Instance.RemoveHintByCustomId(player, countCustomId);
                }

                // 也移除旧的格式，以防万一
                var oldCustomId = $"scp_teammate_{player.UserId}";
                HintManager.Instance.RemoveHintByCustomId(player, oldCustomId);
            }
            catch (Exception ex)
            {
                Logger.Error($"移除SCP队友信息失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 计算SCP显示位置，避免重叠
        /// </summary>
        /// <param name="player">当前SCP玩家</param>
        /// <returns>Y坐标</returns>
        private int CalculateSCPDisplayPosition(Player player)
        {
            try
            {
                // 基于角色类型分配固定位置，确保位置稳定
                int roleOrder = GetRoleDisplayOrder(player.Role);

                // 基础Y坐标900，每个角色向上偏移80像素（Y减少）
                int baseY = 900;
                int offsetPerRole = 80;

                // 直接基于角色顺序计算位置，不依赖动态列表
                return baseY - ((roleOrder - 1) * offsetPerRole); // 向上增长，Y坐标减少
            }
            catch (Exception ex)
            {
                Logger.Debug($"计算SCP显示位置失败: {ex.Message}");
                return 900; // 默认位置
            }
        }
    }
}
