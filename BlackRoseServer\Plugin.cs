﻿using BlackRoseServer.Helper.Chat;
using BlackRoseServer.Helper;
using BlackRoseServer.Helper.Players;
using BlackRoseServer.API.Features.Pool;
using BlackRoseServer.Display;
using MEC;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using BlackRoseServer.Helper.Music;
using AudioApi.Dummies;

using PlayerStatsSystem;
using HarmonyLib;
using HintServiceMeow.UI.Extension;
using HintServiceMeow.Core.Extension;
using PlayerRoles;
using System.Reflection;
using MapGeneration;
using InventorySystem.Items;
using InventorySystem;
using BlackRoseServer.Helper.SCP;
using BlackRoseServer.API;
using System.Collections.Concurrent;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using Logger = LabApi.Features.Console.Logger;
using LabApi.Events.Arguments.PlayerEvents;
using LabApi.Events.Arguments.ServerEvents;
using LabApi.Events.Arguments.Scp914Events;
using LabApi.Events.Arguments.Scp079Events;
using LabApi.Events.Arguments.Scp049Events;
using LabApi.Loader.Features.Plugins;
using HintServiceMeow.Core.Models.Hints;
using LabApi.Loader.Features.Plugins.Enums;
using GameCore;
using InventorySystem.Items.Firearms.Attachments;
using UnityEngine;

namespace BlackRoseServer
{
    public sealed class Plugin : LabApi.Loader.Features.Plugins.Plugin<Config>
    {
        public Plugin()
        {
            BlackRoseServer.Config.Instance = this.Config;
        }
        public Harmony Harmony { get; private set; }

        private readonly EventManagerService _eventManager = new();

        public static class PlayerDataService
        {
            /// <summary>
            /// float[0]击杀次数 float[1]死亡次数 float[2]伤害总值 非常牛逼的设计方法Made by me
            /// </summary>
            public static readonly ConcurrentDictionary<string, float[]> PlayerKillsDeaths = new();
            public static readonly ConcurrentDictionary<string, DisconnectSCPHelper> DisconnectSCPs = new();
            public static readonly ConcurrentDictionary<string, List<Player>> WaitingSCPs = new();

            /// <summary>
            /// 记录玩家升级信息 [升级前等级, 升级后等级]
            /// </summary>
            public static readonly ConcurrentDictionary<string, int[]> PlayerLevelUps = new();

            public static void Reset()
            {
                PlayerKillsDeaths.Clear();
                DisconnectSCPs.Clear();
                WaitingSCPs.Clear();
                PlayerLevelUps.Clear();
            }

            public static void AddOrUpdateKillData(string userId, float kills = 0, float deaths = 0, float damage = 0)
            {
                PlayerKillsDeaths.AddOrUpdate(
                    userId,
                    new float[] { kills, deaths, damage },
                    (key, oldValue) => new float[] 
                    { 
                        oldValue[0] + kills, 
                        oldValue[1] + deaths, 
                        oldValue[2] + damage 
                    }
                );
            }
        }

        public static Dictionary<string, float[]> PlayerKillsDeaths => PlayerDataService.PlayerKillsDeaths.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        public static Dictionary<string, DisconnectSCPHelper> DisconnectSCPs => PlayerDataService.DisconnectSCPs.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        public static Dictionary<string, List<Player>> WaitingSCPs => PlayerDataService.WaitingSCPs.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        public static Dictionary<string, int[]> PlayerLevelUps => PlayerDataService.PlayerLevelUps.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

        public override string Name => "BlackRoseServer";

        public override string Description => "SCPSL Plugins";

        public override string Author => "X小左-开朗的火山河123-to0c";

        public override System.Version Version => new(1,0,0);

        public override System.Version RequiredApiVersion => new(1,0,0);

        private readonly CoroutineService _coroutineManager = new();

        public override LoadPriority Priority { get; } = LoadPriority.High;

        public override void Enable()
        {
            // 确保Config.Instance正确设置
            BlackRoseServer.Config.Instance = this.Config;
            Logger.Debug($"Config.Instance已设置: {BlackRoseServer.Config.Instance != null}");

            _eventManager.RegisterEvents(this);
            ConfigureHarmony();
            InitializeServices();
        }
        public override void Disable()
        {
            _eventManager.UnregisterEvents(this);
        }

        private void ConfigureHarmony()
        {
            Harmony = new Harmony("cn.xlittleleft.plugin");
            Harmony.PatchAll();
        }

        private void InitializeServices()
        {
            try
            {
                // Startup.SetupDependencies();
                MusicHelper.Instance.Init();

                // 验证音频配置
                ValidateAudioConfig();

                if (MusicHelper.Instance.IsAudioAvailable)
                {
                    Logger.Info("服务初始化完成 - 音频功能可用");
                }
                else
                {
                    Logger.Info("服务初始化完成 - 音频功能不可用，其他功能正常");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"服务初始化失败: {ex.Message}");
                Logger.Debug($"服务初始化详细错误: {ex}");
            }
        }

        /// <summary>
        /// 验证音频配置
        /// </summary>
        private void ValidateAudioConfig()
        {
            try
            {
                if (!Config.EnableAudioEffects)
                {
                    Logger.Info("音效功能已在配置中禁用");
                    return;
                }

                // 验证音量设置
                if (Config.AudioVolume < 0 || Config.AudioVolume > 100)
                {
                    Logger.Warn($"音效音量配置无效 ({Config.AudioVolume})，将使用默认值 80");
                    Config.AudioVolume = 80f;
                }

                // 验证击杀音效路径
                if (Config.EnableKillSound && string.IsNullOrWhiteSpace(Config.KillSoundPath))
                {
                    Logger.Warn("击杀音效已启用但文件路径为空，击杀音效将被禁用");
                    Config.EnableKillSound = false;
                }

                // 验证获取经验音效路径
                if (Config.EnableExperienceGainSound && string.IsNullOrWhiteSpace(Config.ExperienceGainSoundPath))
                {
                    Logger.Warn("获取经验音效已启用但文件路径为空，获取经验音效将被禁用");
                    Config.EnableExperienceGainSound = false;
                }

                Logger.Info($"音频配置验证完成 - 击杀音效: {(Config.EnableKillSound ? "启用" : "禁用")}, 获取经验音效: {(Config.EnableExperienceGainSound ? "启用" : "禁用")}, 音量: {Config.AudioVolume}");
            }
            catch (Exception ex)
            {
                Logger.Error($"音频配置验证失败: {ex.Message}");
                Logger.Debug($"ValidateAudioConfig详细错误: {ex}");
            }
        }

        void HandleRoundStart(RoundStartingEventArgs ev)
        {
            ApplyFriendlyFireSettings();

            // 重置当局得分数据
            PlayerDataService.Reset();
            ExperienceSystemHelper.Instance.CleanupRoundData();

            // 启动数据缓存系统
            BlackRoseServer.API.PlayerDataCache.Instance.StartRound();

            // 清理过期的称号切换冷却记录
            BlackRoseServer.Commands.ConsoleCommand.ToggleBadgeCommand.CleanupExpiredCooldowns();

            // 根据配置启动功能协程
            if (Config.Instance?.EnableInfiniteAmmo == true)
            {
                _coroutineManager.StartCoroutine(XHelper.InAmmo());
                Logger.Info("无限子弹功能已启用");
            }
            else
            {
                Logger.Info("无限子弹功能已禁用");
            }

            if (Config.Instance?.EnableGuardOffDuty == true)
            {
                _coroutineManager.StartCoroutine(XHelper.Guard());
                Logger.Info("保安下班功能已启用");
            }
            else
            {
                Logger.Info("保安下班功能已禁用");
            }

            // 启动出生保护系统
            BlackRoseServer.Manager.SpawnProtectionManager.Instance.StartProtectionSystem();

            // 延迟初始化计时器显示系统，确保Config完全加载
            Timing.CallDelayed(2f, () =>
            {
                try
                {
                    if (Config.Instance != null)
                    {
                        TimerDisplayHelper.Instance.Initialize();
                        Logger.Debug("计时器显示系统延迟初始化成功");
                    }
                    else
                    {
                        Logger.Error("延迟2秒后Config.Instance仍为null，计时器系统初始化失败");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"延迟初始化计时器显示系统失败: {ex.Message}");
                }
            });

            _coroutineManager.StartDelayedCoroutine(360f, () =>
            {
                _coroutineManager.ServerHandler = _coroutineManager.StartCoroutine(XHelper.ServerHandle());
            });
        }

        private void ApplyFriendlyFireSettings()
        {
            if (Config.Instance?.EnableFriendlyFire == true)
            {
                Server.FriendlyFire = false;
                typeof(AttackerDamageHandler).GetMethod("RefreshConfigs", BindingFlags.Static | BindingFlags.NonPublic)?.Invoke(null, null);
            }
        }

        void HandlePlayerDamage(PlayerHurtingEventArgs ev)
        {
            // 检查出生保护
            if (BlackRoseServer.Manager.SpawnProtectionManager.Instance.IsProtected(ev.Player))
            {
                ev.IsAllowed = false;
                Logger.Debug($"玩家 {ev.Player.Nickname} 受到出生保护，伤害已阻止");
                return;
            }

            // 如果攻击者有出生保护，移除保护
            if (ev.Attacker != null && BlackRoseServer.Manager.SpawnProtectionManager.Instance.IsProtected(ev.Attacker))
            {
                BlackRoseServer.Manager.SpawnProtectionManager.Instance.RemoveProtection(ev.Attacker, "攻击他人");
            }

            var damageProcessor = new DamageProcessor(ev);
            damageProcessor.ProcessDamage();
        }

        void HandlePlayerDying(PlayerDyingEventArgs ev)
        {
            var deathProcessor = new DeathProcessor(ev);
            deathProcessor.ProcessDeath();

            // 处理经验系统
            ExperienceSystemHelper.Instance.ProcessPlayerDying(ev);
        }

        void HandlePlayerSpawn(PlayerSpawningEventArgs ev)
        {
            var spawnProcessor = new SpawnProcessor(ev);
            spawnProcessor.ProcessSpawn();

            // 添加出生保护
            if (ev.Player != null && ev.Player.Role != RoleTypeId.Spectator)
            {
                Timing.CallDelayed(0.5f, () =>
                {
                    BlackRoseServer.Manager.SpawnProtectionManager.Instance.AddProtection(ev.Player);
                });
            }
        }

        public void HandleWaitingForPlayers()
        {
            if (Config.Instance?.EnableFriendlyFire == true)
            {
                typeof(AttackerDamageHandler).GetMethod("RefreshConfigs", BindingFlags.Static | BindingFlags.NonPublic)?.Invoke(null, null);
                Server.FriendlyFire = false;
            }
        }

        void HandlePlayerEscape(PlayerEscapingEventArgs ev)
        {
            var escapeProcessor = new EscapeProcessor(ev);
            escapeProcessor.ProcessEscape();

            // 处理经验系统
            ExperienceSystemHelper.Instance.ProcessPlayerEscaping(ev);
        }

        void HandleScp914Activation(Scp914ActivatingEventArgs ev)
        {
            var scp914Processor = new Scp914Processor(ev);
            scp914Processor.ProcessActivation();
        }

        void HandleRoundEnd(RoundEndedEventArgs ev)
        {
            // 先处理MVP和排行榜（在缓存系统结束前）
            try
            {
                var roundEndProcessor = new RoundEndProcessor();
                roundEndProcessor.ProcessRoundEnd();
            }
            catch (Exception ex)
            {
                Logger.Error($"回合结束处理失败: {ex.Message}");
            }

            // 然后批量写入缓存的玩家数据
            try
            {
                BlackRoseServer.API.PlayerDataCache.Instance.EndRound();
                Logger.Info("回合结束时批量写入玩家数据完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"回合结束时批量写入玩家数据失败: {ex.Message}");
            }

            // 清理出生保护
            try
            {
                BlackRoseServer.Manager.SpawnProtectionManager.Instance.StopProtectionSystem();
            }
            catch (Exception ex)
            {
                Logger.Error($"停止出生保护系统失败: {ex.Message}");
            }

            // 停止所有显示系统，避免在回合结束处理时出现冲突
            try
            {
                TimerDisplayHelper.Instance.Cleanup();
                Logger.Debug("回合结束时清理计时器显示系统完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"回合结束时清理计时器显示系统失败: {ex.Message}");
            }

            // 停止RightBottomDisplayManager协程，防止在回合结束处理时卡死
            try
            {
                RightBottomDisplayManager.DisposeInstance();
                Logger.Debug("回合结束时清理右下角显示系统完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"回合结束时清理右下角显示系统失败: {ex.Message}");
            }

            // 强制清理所有显示系统，防止位置冲突和卡死
            try
            {
                // 清理经验显示系统
                SimpleExperienceDisplay.DisposeInstance();
                Logger.Debug("回合结束时清理经验显示系统完成");

                // 清理顶部UI显示系统
                BlackRoseServer.Display.TopUIDisplay.DisposeInstance();
                Logger.Debug("回合结束时清理顶部UI显示系统完成");

                // 🚨 移除危险的Player.List遍历，直接清理HintManager
                // 在回合结束时访问Player.List可能导致卡死
                try
                {
                    Manager.HintManager.Instance.ForceCleanupAllPositions();
                }
                catch (Exception hintEx)
                {
                    Logger.Debug($"强制清理Hint位置失败: {hintEx.Message}");
                }
                Logger.Debug("回合结束时强制清理所有Hint位置完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"回合结束时强制清理显示系统失败: {ex.Message}");
            }

            _coroutineManager.KillCoroutine(_coroutineManager.ServerHandler);
        }

        public void HandlePlayerJoin(PlayerJoinedEventArgs ev)
        {
            var joinProcessor = new PlayerJoinProcessor(ev);
            joinProcessor.ProcessJoin();
        }

        void HandlePlayerLeft(PlayerLeftEventArgs ev)
        {
            var leftProcessor = new PlayerLeftProcessor(ev);
            leftProcessor.ProcessLeft();

            // 清理经验系统玩家数据
            if (ev.Player != null && !string.IsNullOrEmpty(ev.Player.UserId))
            {
                ExperienceSystemHelper.Instance.CleanupPlayerData(ev.Player.UserId);
                TimerDisplayHelper.Instance.RemovePlayer(ev.Player);
            }
        }

        void HandleRoundRestart()
        {
            var restartProcessor = new RoundRestartProcessor();
            restartProcessor.ProcessRestart();

            // 清理经验系统数据
            ExperienceSystemHelper.Instance.CleanupRoundData();
        }

        void HandleDropAmmo(PlayerDroppingAmmoEventArgs ev)
        {
            ev.IsAllowed = false;
        }

        void HandleElevatorInteraction(PlayerInteractedElevatorEventArgs ev)
        {
            var elevatorProcessor = new ElevatorInteractionProcessor(ev);
            elevatorProcessor.ProcessInteraction();
        }

        // 经验系统事件处理方法
        void HandlePlayerHurt(PlayerHurtEventArgs ev)
        {
            ExperienceSystemHelper.Instance.ProcessPlayerHurt(ev);
        }

        void HandlePlayerUsedItem(PlayerUsedItemEventArgs ev)
        {
            ExperienceSystemHelper.Instance.ProcessPlayerUsedItem(ev);
        }

        void HandlePlayerActivatedGenerator(PlayerActivatedGeneratorEventArgs ev)
        {
            ExperienceSystemHelper.Instance.ProcessPlayerActivatedGenerator(ev);
        }

        void HandleScp079ChangedCamera(Scp079ChangedCameraEventArgs ev)
        {
            ExperienceSystemHelper.Instance.ProcessScp079ChangedCamera(ev);
        }

        void HandleScp079GainedExperience(Scp079GainedExperienceEventArgs ev)
        {
            ExperienceSystemHelper.Instance.ProcessScp079GainedExperience(ev);
        }

        void HandleScp079UsedTesla(Scp079UsedTeslaEventArgs ev)
        {
            ExperienceSystemHelper.Instance.ProcessScp079UsedTesla(ev);
        }

        void HandleScp049ResurrectedBody(Scp049ResurrectedBodyEventArgs ev)
        {
            ExperienceSystemHelper.Instance.ProcessScp049ResurrectedBody(ev);
        }



        // 计时器显示事件处理方法
        void HandlePlayerChangingRole(PlayerChangingRoleEventArgs ev)
        {
            TimerDisplayHelper.Instance.HandleRoleChange(ev.Player, ev.NewRole);

            // 处理SCP阵营显示
            try
            {
                var oldRoleType = ev.OldRole?.RoleTypeId ?? RoleTypeId.None;
                BlackRoseServer.Display.RightBottomDisplayManager.Instance.OnPlayerRoleChanged(ev.Player, oldRoleType, ev.NewRole);
            }
            catch (Exception ex)
            {
                Logger.Error($"处理SCP显示角色变更失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 播放击杀音效
        /// </summary>
        /// <param name="player">玩家</param>
        private static void PlayKillSound(Player player)
        {
            try
            {
                if (player == null || player.DoNotTrack) return;

                // 检查配置实例是否可用
                if (BlackRoseServer.Config.Instance == null)
                {
                    Logger.Debug("配置实例不可用，跳过击杀音效播放");
                    return;
                }

                // 检查配置是否启用音效功能
                if (!BlackRoseServer.Config.Instance.EnableAudioEffects || !BlackRoseServer.Config.Instance.EnableKillSound)
                {
                    return; // 配置禁用时直接返回，不记录日志
                }

                // 检查MusicHelper实例是否可用
                if (MusicHelper.Instance == null)
                {
                    Logger.Debug("MusicHelper实例不可用，跳过击杀音效播放");
                    return;
                }

                if (!MusicHelper.Instance.IsAudioAvailable)
                {
                    Logger.Debug($"音频功能不可用，跳过为玩家 {player.Nickname} 播放击杀音效");
                    return;
                }

                // 验证音频文件路径
                if (string.IsNullOrWhiteSpace(BlackRoseServer.Config.Instance.KillSoundPath))
                {
                    Logger.Warn($"击杀音效文件路径未配置，跳过播放");
                    return;
                }

                // 验证音频文件是否存在
                if (!System.IO.File.Exists(BlackRoseServer.Config.Instance.KillSoundPath))
                {
                    Logger.Error($"击杀音效文件不存在: {BlackRoseServer.Config.Instance.KillSoundPath}");
                    return;
                }

                // 播放击杀音效 - 使用AudioApi，简单可靠
                try
                {
                    // 使用AudioApi的VoiceDummy.PlayToPlayer扩展方法
                    // 注意：AudioApi会自动添加.ogg扩展名，所以只传递不含扩展名的文件名
                    string audioPath = System.IO.Path.GetDirectoryName(BlackRoseServer.Config.Instance.KillSoundPath);
                    string audioFileName = System.IO.Path.GetFileNameWithoutExtension(BlackRoseServer.Config.Instance.KillSoundPath);

                    Logger.Debug($"AudioApi播放参数 - 路径: {audioPath}, 文件名: {audioFileName}");
                    Logger.Debug($"玩家信息 - ID: {player.PlayerId}, 连接状态: {player.IsReady}, 角色: {player.Role}");

                    // 检查文件是否真的存在
                    string fullPath = System.IO.Path.Combine(audioPath, audioFileName + ".ogg");
                    Logger.Debug($"检查完整路径: {fullPath}, 文件存在: {System.IO.File.Exists(fullPath)}");

                    // 尝试使用VoiceDummy的静态方法直接播放
                    VoiceDummy.PlayToPlayer(
                        player.ReferenceHub,
                        player.PlayerId, // 使用玩家ID作为假人ID
                        audioPath, // 路径
                        audioFileName, // 文件名（不含扩展名，AudioApi会自动添加.ogg）
                        BlackRoseServer.Config.Instance.AudioVolume,
                        false // 不循环
                    );

                    Logger.Debug($"为玩家 {player.Nickname} 播放击杀音效: {BlackRoseServer.Config.Instance.KillSoundPath}");
                }
                catch (Exception audioEx)
                {
                    Logger.Error($"AudioApi播放击杀音效失败: {audioEx.Message}");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"播放击杀音效失败 - 玩家: {player?.Nickname ?? "Unknown"}, 错误: {ex.Message}");
                Logger.Debug($"PlayKillSound详细错误: {ex}");
            }
        }

        /// <summary>
        /// 截断玩家名称，超过12个字符的部分用..替换
        /// </summary>
        /// <param name="playerName">玩家名称</param>
        /// <returns>截断后的名称</returns>
        private static string TruncatePlayerName(string playerName)
        {
            if (string.IsNullOrEmpty(playerName))
                return string.Empty;

            if (playerName.Length <= 12)
                return playerName;

            return playerName.Substring(0, 12) + "..";
        }

        /// <summary>
        /// 播放获取经验音效
        /// </summary>
        /// <param name="player">玩家</param>
        public static void PlayExperienceGainSound(Player player)
        {
            try
            {
                if (player == null || player.DoNotTrack) return;

                // 检查配置实例是否可用
                if (BlackRoseServer.Config.Instance == null)
                {
                    Logger.Debug("配置实例不可用，跳过获取经验音效播放");
                    return;
                }

                // 检查配置是否启用音效功能
                if (!BlackRoseServer.Config.Instance.EnableAudioEffects || !BlackRoseServer.Config.Instance.EnableExperienceGainSound)
                {
                    return; // 配置禁用时直接返回，不记录日志
                }

                // 检查MusicHelper实例是否可用
                if (MusicHelper.Instance == null)
                {
                    Logger.Debug("MusicHelper实例不可用，跳过获取经验音效播放");
                    return;
                }

                if (!MusicHelper.Instance.IsAudioAvailable)
                {
                    Logger.Debug($"音频功能不可用，跳过为玩家 {player.Nickname} 播放获取经验音效");
                    return;
                }

                // 验证音频文件路径
                if (string.IsNullOrWhiteSpace(BlackRoseServer.Config.Instance.ExperienceGainSoundPath))
                {
                    Logger.Warn($"获取经验音效文件路径未配置，跳过播放");
                    return;
                }

                // 播放获取经验音效 - 使用MusicHelper替代AudioApi
                try
                {
                    // 使用MusicHelper播放音效，避免AudioApi版本依赖问题
                    var audioPlayer = MusicHelper.Instance.Play(
                        BlackRoseServer.Config.Instance.ExperienceGainSoundPath,
                        $"ExperienceGainSound_{player.UserId}",
                        new MusicHelper.TrackEvent(),
                        player, // source
                        new Player[] { player }, // extraPlay (只对当前玩家播放)
                        false, // isSole
                        BlackRoseServer.Config.Instance.AudioVolume, // volume
                        false // isLoop
                    );

                    if (audioPlayer != null)
                    {
                        Logger.Debug($"为玩家 {player.Nickname} 播放获取经验音效: {BlackRoseServer.Config.Instance.ExperienceGainSoundPath}");
                    }
                    else
                    {
                        Logger.Debug($"获取经验音效播放器创建失败 - 玩家: {player.Nickname}");
                    }
                }
                catch (Exception audioEx)
                {
                    Logger.Debug($"MusicHelper播放获取经验音效失败: {audioEx.Message}");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"播放获取经验音效失败 - 玩家: {player?.Nickname ?? "Unknown"}, 错误: {ex.Message}");
                Logger.Debug($"PlayExperienceGainSound详细错误: {ex}");
            }
        }

        /// <summary>
        /// 记录玩家升级信息
        /// </summary>
        /// <param name="player">玩家</param>
        /// <param name="levelBefore">升级前等级</param>
        /// <param name="levelAfter">升级后等级</param>
        private static void RecordPlayerLevelUp(Player player, int levelBefore, int levelAfter)
        {
            if (player == null) return;

            PlayerDataService.PlayerLevelUps.AddOrUpdate(
                player.UserId,
                new int[] { levelBefore, levelAfter },
                (key, oldValue) => new int[] { levelBefore, levelAfter }
            );
        }

        /// <summary>
        /// 安全显示提示信息，使用控制台消息方案
        /// </summary>
        private static void SafeShowHint(Player player, string message)
        {
            try
            {
                if (player == null) return;

                // 直接使用控制台消息方案
                player.SendConsoleMessage($"[提示] {message}", "yellow");
            }
            catch (Exception ex)
            {
                Logger.Error($"SafeShowHint失败 - 玩家: {player?.Nickname ?? "Unknown"}, 消息: {message}, 错误: {ex.Message}");
                Logger.Debug($"SafeShowHint详细错误: {ex}");
            }
        }



        private class CoroutineService
        {
            public CoroutineHandle ServerHandler { get; set; }

            public CoroutineHandle StartCoroutine(IEnumerator<float> coroutine)
            {
                return Timing.RunCoroutine(coroutine);
            }

            public void StartDelayedCoroutine(float delay, Action action)
            {
                Timing.CallDelayed(delay, action);
            }

            public void KillCoroutine(CoroutineHandle handle)
            {
                Timing.KillCoroutines(handle);
            }
        }

        private class EventManagerService
        {
            public void RegisterEvents(Plugin target)
            {
                LabApi.Events.Handlers.PlayerEvents.DroppingAmmo += target.HandleDropAmmo;
                LabApi.Events.Handlers.PlayerEvents.Hurting += target.HandlePlayerDamage;
                LabApi.Events.Handlers.PlayerEvents.Dying += target.HandlePlayerDying;
                LabApi.Events.Handlers.PlayerEvents.Spawning += target.HandlePlayerSpawn;
                LabApi.Events.Handlers.ServerEvents.WaitingForPlayers += target.HandleWaitingForPlayers;
                LabApi.Events.Handlers.PlayerEvents.Escaping += target.HandlePlayerEscape;
                LabApi.Events.Handlers.Scp914Events.Activating += target.HandleScp914Activation;
                LabApi.Events.Handlers.ServerEvents.RoundStarting += target.HandleRoundStart;
                LabApi.Events.Handlers.ServerEvents.RoundEnded += target.HandleRoundEnd;
                LabApi.Events.Handlers.PlayerEvents.Joined += target.HandlePlayerJoin;
                LabApi.Events.Handlers.PlayerEvents.Left += target.HandlePlayerLeft;
                LabApi.Events.Handlers.ServerEvents.RoundRestarted += target.HandleRoundRestart;
                LabApi.Events.Handlers.PlayerEvents.InteractedElevator += target.HandleElevatorInteraction;

                // 新增经验系统事件
                LabApi.Events.Handlers.PlayerEvents.Hurt += target.HandlePlayerHurt;
                LabApi.Events.Handlers.PlayerEvents.UsedItem += target.HandlePlayerUsedItem;
                LabApi.Events.Handlers.PlayerEvents.ActivatedGenerator += target.HandlePlayerActivatedGenerator;
                LabApi.Events.Handlers.Scp079Events.ChangedCamera += target.HandleScp079ChangedCamera;
                LabApi.Events.Handlers.Scp079Events.GainedExperience += target.HandleScp079GainedExperience;
                LabApi.Events.Handlers.Scp079Events.UsedTesla += target.HandleScp079UsedTesla;
                LabApi.Events.Handlers.Scp049Events.ResurrectedBody += target.HandleScp049ResurrectedBody;



                // 计时器显示事件
                LabApi.Events.Handlers.PlayerEvents.ChangingRole += target.HandlePlayerChangingRole;
            }
            public void UnregisterEvents(Plugin target)
            {
                LabApi.Events.Handlers.PlayerEvents.DroppingAmmo -= target.HandleDropAmmo;
                LabApi.Events.Handlers.PlayerEvents.Hurting -= target.HandlePlayerDamage;
                LabApi.Events.Handlers.PlayerEvents.Dying -= target.HandlePlayerDying;
                LabApi.Events.Handlers.PlayerEvents.Spawning -= target.HandlePlayerSpawn;
                LabApi.Events.Handlers.ServerEvents.WaitingForPlayers -= target.HandleWaitingForPlayers;
                LabApi.Events.Handlers.PlayerEvents.Escaping -= target.HandlePlayerEscape;
                LabApi.Events.Handlers.Scp914Events.Activating -= target.HandleScp914Activation;
                LabApi.Events.Handlers.ServerEvents.RoundStarting -= target.HandleRoundStart;
                LabApi.Events.Handlers.ServerEvents.RoundEnded -= target.HandleRoundEnd;
                LabApi.Events.Handlers.PlayerEvents.Joined -= target.HandlePlayerJoin;
                LabApi.Events.Handlers.PlayerEvents.Left -= target.HandlePlayerLeft;
                LabApi.Events.Handlers.ServerEvents.RoundRestarted -= target.HandleRoundRestart;
                LabApi.Events.Handlers.PlayerEvents.InteractedElevator -= target.HandleElevatorInteraction;

                // 移除经验系统事件
                LabApi.Events.Handlers.PlayerEvents.Hurt -= target.HandlePlayerHurt;
                LabApi.Events.Handlers.PlayerEvents.UsedItem -= target.HandlePlayerUsedItem;
                LabApi.Events.Handlers.PlayerEvents.ActivatedGenerator -= target.HandlePlayerActivatedGenerator;
                LabApi.Events.Handlers.Scp079Events.ChangedCamera -= target.HandleScp079ChangedCamera;
                LabApi.Events.Handlers.Scp079Events.GainedExperience -= target.HandleScp079GainedExperience;
                LabApi.Events.Handlers.Scp079Events.UsedTesla -= target.HandleScp079UsedTesla;
                LabApi.Events.Handlers.Scp049Events.ResurrectedBody -= target.HandleScp049ResurrectedBody;



                // 移除计时器显示事件
                LabApi.Events.Handlers.PlayerEvents.ChangingRole -= target.HandlePlayerChangingRole;
            }
        }

        private class DamageProcessor
        {
            private readonly PlayerHurtingEventArgs _event;

            public DamageProcessor(PlayerHurtingEventArgs ev)
            {
                _event = ev;
            }

            public void ProcessDamage()
            {
                var Target = _event.Player;
                var Attacker = _event.Attacker;
                var DamageHandler = _event.DamageHandler;

                if (Attacker != null && Target != null && DamageHandler is StandardDamageHandler handler)
                {
                    if (Attacker != Target && !Attacker.IsSameTeam(Target))
                    {
                        PlayerDataService.AddOrUpdateKillData(Attacker.UserId, 0, 0, handler.Damage);
                    }
                }
            }
        }

        private class DeathProcessor
        {
            private readonly PlayerDyingEventArgs _event;

            public DeathProcessor(PlayerDyingEventArgs ev)
            {
                _event = ev;
            }

            public void ProcessDeath()
            {
                var Target = _event.Player;
                var Attacker = _event.Attacker;
                var DamageHandler = _event.DamageHandler;

                if (Target is not null)
                {
                    ProcessTargetDeath(Target, Attacker, DamageHandler);
                }

                // 旧的经验系统已被ExperienceSystemHelper替代
                // ProcessExperienceSystem(Target, Attacker, DamageHandler);
            }

            private void ProcessTargetDeath(Player Target, Player Attacker, DamageHandlerBase DamageHandler)
            {
                if (Target.ReferenceHub.IsSCP(false))
                {
                    ProcessSCPDeath(Target, Attacker, DamageHandler);
                }
                else if (Attacker is not null && Target != Attacker)
                {
                    PlayerDataService.AddOrUpdateKillData(Attacker.UserId, 1, 0, 0);
                }

                PlayerDataService.AddOrUpdateKillData(Target.UserId, 0, 1, 0);

                if (Attacker is not null && Target != Attacker)
                {
                    Timing.CallDelayed(0.1f, () =>
                    {
                        Attacker.SendHitMarker(3f);

                        // 播放击杀音效
                        Plugin.PlayKillSound(Attacker);
                    });
                }
            }

            private void ProcessSCPDeath(Player Target, Player Attacker, DamageHandlerBase DamageHandler)
            {
                if (DamageHandler is UniversalDamageHandler universal && universal.TranslationId == DeathTranslations.Crushed.Id && Round.Duration.TotalSeconds <= 150)
                {
                    string UserId = Target.UserId;

                    PlayerDataService.DisconnectSCPs.TryAdd(UserId, new DisconnectSCPHelper(Target));
                    PlayerDataService.WaitingSCPs.TryAdd(UserId, new List<Player>());

                    // 优化SCP补位广播显示
                    string roleTranslation = ChatHelper.Instance.GetRoleTranslations()[Target.Role];
                    string broadcastMessage = $"<color=#FF6B6B><size=25>🔄 SCP补位系统</size></color>\n" +
                                            $"<color=#FFD700>{roleTranslation}</color> <color=#CCCCCC>疑似自杀</color>\n" +
                                            $"<color=#87CEEB>输入</color> <color=#00FF00>.scp</color> <color=#87CEEB>申请补位</color>";

                    Server.SendBroadcast(broadcastMessage, 15, Broadcast.BroadcastFlags.Normal, true);

                    Timing.CallDelayed(15f, () =>
                    {
                        if (PlayerDataService.DisconnectSCPs.TryGetValue(UserId, out var NewSCP) && 
                            PlayerDataService.WaitingSCPs.TryGetValue(UserId, out var waitingList) && 
                            waitingList.Count > 0)
                        {
                            Player TargetPlayer = waitingList.RandomItem();
                            if (NewSCP != null && TargetPlayer != null)
                            {
                                NewSCP.FullyReborn(TargetPlayer);
                                PlayerDataService.WaitingSCPs.TryRemove(UserId, out _);
                                if (PlayerDataService.DisconnectSCPs.IsEmpty)
                                {
                                    PlayerDataService.WaitingSCPs.Clear();
                                }
                            }
                        }
                    });
                }
                else if (Attacker is not null)
                {
                    Server.SendBroadcast($"<b><size=40><color=red>{ChatHelper.Instance.GetRoleTranslations()[Target.Role]}</color>已被<color={Attacker.ReferenceHub.roleManager.CurrentRole.RoleColor.ToHex()}>{Attacker.Nickname}</color>重新收容</size></b>", 6, Broadcast.BroadcastFlags.Normal);
                    
                    PlayerDataService.AddOrUpdateKillData(Attacker.UserId, 5, 0, 0);
                }
            }

            private void ProcessExperienceSystem(Player Target, Player Attacker, DamageHandlerBase DamageHandler)
            {
                if (Target == null || Attacker == null || (Attacker != null && Attacker.DoNotTrack))
                {
                    return;
                }

                if (DamageHandler is UniversalDamageHandler universalDamageHandler && universalDamageHandler.TranslationId == DeathTranslations.PocketDecay.Id)
                {
                    var SCP106 = XHelper.PlayerList.Where(x => x.Role is RoleTypeId.Scp106).FirstOrDefault();

                    if (SCP106 != null)
                    {
                        SCP106.AddXP(3);
                        SafeShowHint(SCP106, "击杀人类获得3经验");
                    }
                }

                if (Target.IsSCP && Target.Role is not RoleTypeId.Scp0492)
                {
                    ProcessExperienceGain(Attacker, 15, "击杀SCP获得15经验");
                }
                else
                {
                    ProcessExperienceGain(Attacker, 3, "击杀人类获得3经验");
                }
            }

            // PlayKillSound和PlayLevelUpSound方法已移到Plugin类作为静态方法

            /// <summary>
            /// 处理经验获得，包括升级检测和音效播放
            /// </summary>
            /// <param name="player">玩家</param>
            /// <param name="xpAmount">经验数量</param>
            /// <param name="message">提示消息</param>
            private void ProcessExperienceGain(Player player, int xpAmount, string message)
            {
                try
                {
                    if (player == null || player.DoNotTrack) return;

                    // 获取升级前的等级
                    int levelBefore = player.GetLVL();

                    // 添加经验
                    player.AddXP(xpAmount);

                    // 获取升级后的等级
                    int levelAfter = player.GetLVL();

                    // 检查是否升级
                    bool isLevelUp = levelAfter > levelBefore;

                    // 播放获取经验音效（每次获得经验都播放）
                    Plugin.PlayExperienceGainSound(player);

                    if (isLevelUp)
                    {
                        // 记录升级信息，延迟到结算时显示
                        RecordPlayerLevelUp(player, levelBefore, levelAfter);
                        Logger.Debug($"玩家 {player.Nickname} 升级到 {levelAfter} 级，将在结算时显示");
                    }
                    else
                    {
                        // 显示普通经验进度条
                        Helper.Players.ExperienceHelper.Instance.ShowExperienceProgress(player, xpAmount, false);

                        SafeShowHint(player, message);
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"处理经验获得失败 - 玩家: {player?.Nickname ?? "Unknown"}, 经验: {xpAmount}, 错误: {ex.Message}");
                    Logger.Debug($"ProcessExperienceGain详细错误: {ex}");
                }
            }
        }

        private class SpawnProcessor
        {
            private readonly PlayerSpawningEventArgs _event;

            public SpawnProcessor(PlayerSpawningEventArgs ev)
            {
                _event = ev;
            }

            public void ProcessSpawn()
            {
                var Player = _event.Player;

                if (Player is null) return;

                Timing.CallDelayed(1f, () =>
                {
                    if (Player.Role is RoleTypeId.ClassD)
                    {
                        Player.AddItem(ItemType.KeycardJanitor, ItemAddReason.AdminCommand);
                        Player.AddItem(ItemType.Painkillers, ItemAddReason.AdminCommand);
                    }
                    if (Player.Role.GetTeam() is Team.SCPs)
                    {
                        Timing.RunCoroutine(XHelper.PositionCheckerCoroutine(Player));
                    }
                });

                if (Player.IsSCP)
                {
                    Timing.CallDelayed(0.5f, () =>
                    {
                        if (XHelper.healthDict.TryGetValue(Player.Role, out var health))
                        {
                            Player.Health = health;
                        }
                    });
                }
            }
        }

        private class EscapeProcessor
        {
            private readonly PlayerEscapingEventArgs _event;

            public EscapeProcessor(PlayerEscapingEventArgs ev)
            {
                _event = ev;
            }

            public void ProcessEscape()
            {
                var Player = _event.Player;
                var NewRole = _event.NewRole;

                if (Player is null) return;

                if (Player.Role is RoleTypeId.ClassD && NewRole.GetTeam() is Team.FoundationForces)
                {
                    Timing.CallDelayed(1f, () =>
                    {
                        for (int i = 0; i < Player.ReferenceHub.inventory.UserInventory.Items.Count; i++)
                        {
                            KeyValuePair<ushort, ItemBase> keyValuePair = Player.ReferenceHub.inventory.UserInventory.Items.ElementAt(i);
                            if (keyValuePair.Value.ItemTypeId == ItemType.GunCrossvec)
                            {
                                Player.ReferenceHub.inventory.ServerRemoveItem(keyValuePair.Key, keyValuePair.Value.PickupDropModel);
                                break;
                            }
                        }
                        Player.AddItem(ItemType.GunE11SR);
                        Player.AddAmmo(ItemType.Ammo556x45, 120);
                    });
                }
            }
        }

        private class Scp914Processor
        {
            private readonly Scp914ActivatingEventArgs _event;

            public Scp914Processor(Scp914ActivatingEventArgs ev)
            {
                _event = ev;
            }

            public void ProcessActivation()
            {
                var Player = _event.Player;
                var Setting = _event.KnobSetting;

                if (Player is null) return;

                string message = $"<color=#FFA500><size=25>[SCP-914启动]</size></color>\n<color=#FFFFFF>{XHelper.Scp914KnobSettingTranslate[Setting]}</color>\n<color=#808080>启动者: {Player.Nickname}</color>";

                // 获取附近5米范围内的玩家
                var nearbyPlayers = XHelper.PlayerList.Where(p => p != null &&
                    Vector3.Distance(p.Position, Player.Position) <= 5f).ToList();

                foreach (var nearbyPlayer in nearbyPlayers)
                {
                    var scp914Hint = new Hint
                    {
                        Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center,
                        YCoordinate = 800, // 屏幕下方显示
                        FontSize = 20,
                        LineHeight = 5,
                        Text = message
                    };

                    nearbyPlayer.AddHint(scp914Hint);

                    // 5秒后移除显示
                    Timing.CallDelayed(5f, () =>
                    {
                        nearbyPlayer.RemoveHint(scp914Hint);
                    });
                }

                Logger.Debug($"SCP-914启动信息已显示 - 启动者: {Player.Nickname}, 设置: {Setting}, 附近玩家数: {nearbyPlayers.Count}");
            }
        }

        private class RoundEndProcessor
        {
            public void ProcessRoundEnd()
            {
                try
                {
                    // 处理MVP计算和显示
                    ProcessMVPAndLeaderboards();

                    // 显示回合结束排行榜
                    RoundHelper.Instance.OnRoundEnd();

                    if (Config.Instance?.EnableFriendlyFire == true)
                    {
                        try
                        {
                            Server.FriendlyFire = true;
                            Logger.Debug("回合结束时启用友伤");

                            // 移除危险的反射调用，避免可能的卡死
                            // typeof(AttackerDamageHandler).GetMethod("RefreshConfigs", BindingFlags.Static | BindingFlags.NonPublic)?.Invoke(null, null);
                        }
                        catch (Exception ffEx)
                        {
                            Logger.Error($"启用友伤失败: {ffEx.Message}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"RoundEndProcessor处理失败: {ex.Message}");
                    Logger.Debug($"RoundEndProcessor详细错误: {ex}");
                }
            }

            private void ProcessMVPAndLeaderboards()
            {
                try
                {
                    Logger.Debug("开始处理MVP和排行榜...");
                    var playerData = Plugin.PlayerKillsDeaths;
                    Logger.Debug($"PlayerKillsDeaths数据: {playerData?.Count ?? 0} 个玩家");

                    if (playerData == null || !playerData.Any())
                    {
                        Logger.Warn("没有玩家数据，跳过MVP处理");
                        return;
                    }

                    // 打印所有玩家数据用于调试（避免大量Player.Get调用）
                    Logger.Debug($"玩家数据总数: {playerData.Count}");
                    foreach (var kvp in playerData.Take(5)) // 只打印前5个玩家的数据
                    {
                        Logger.Debug($"玩家数据: {kvp.Key} - 击杀:{kvp.Value[0]}, 死亡:{kvp.Value[1]}, 伤害:{kvp.Value[2]}");
                    }

                    // 计算MVP（基于当局得分：击杀*3 + 伤害/100）
                    var mvpEntry = playerData
                        .Where(kvp => kvp.Value[0] > 0 || kvp.Value[2] > 0) // 有击杀或伤害
                        .Select(kvp => new {
                            Key = kvp.Key,
                            Value = kvp.Value,
                            Score = kvp.Value[0] * 3 + kvp.Value[2] / 100 // 当局得分
                        })
                        .OrderByDescending(x => x.Score)
                        .FirstOrDefault();

                    if (mvpEntry != null && !string.IsNullOrEmpty(mvpEntry.Key))
                    {
                        Player mvpPlayer = null;
                        try
                        {
                            mvpPlayer = Player.Get(userId: mvpEntry.Key);
                        }
                        catch (Exception ex)
                        {
                            Logger.Debug($"获取MVP玩家失败: {ex.Message}");
                        }
                        if (mvpPlayer != null && !mvpPlayer.DoNotTrack)
                        {
                            // 获取升级前的等级
                            int levelBefore = mvpPlayer.GetLVL();

                            // 给MVP玩家+30经验
                            mvpPlayer.AddXP(30);

                            // 获取升级后的等级
                            int levelAfter = mvpPlayer.GetLVL();

                            // 检查是否升级
                            bool isLevelUp = levelAfter > levelBefore;

                            // 显示经验进度条
                            Helper.Players.ExperienceHelper.Instance.ShowExperienceProgress(mvpPlayer, 30, isLevelUp);

                            if (isLevelUp)
                            {
                                // 播放获取经验音效（添加异常处理避免AudioApi依赖问题）
                                try
                                {
                                    Plugin.PlayExperienceGainSound(mvpPlayer);
                                }
                                catch (Exception audioEx)
                                {
                                    Logger.Debug($"播放MVP升级音效失败: {audioEx.Message}");
                                }
                                Logger.Debug($"MVP玩家 {mvpPlayer.Nickname} 升级到 {levelAfter} 级");
                            }

                            // 显示MVP信息
                            ShowMVPDisplay(mvpPlayer, mvpEntry.Score);

                            Logger.Debug($"MVP玩家: {mvpPlayer.Nickname}, 当局得分: {mvpEntry.Score:F2}, 已获得30经验");
                        }
                    }

                    // 显示升级玩家信息
                    ShowLevelUpPlayers();

                    // 移除回合结算排行榜显示，避免与实时排行榜重复
                }
                catch (Exception ex)
                {
                    Logger.Error($"处理MVP和排行榜失败: {ex.Message}");
                    Logger.Debug($"ProcessMVPAndLeaderboards详细错误: {ex}");
                }
            }

            private void ShowMVPDisplay(Player mvpPlayer, double score)
            {
                try
                {
                    // 为所有玩家显示极致美观的MVP信息
                    string displayName = TruncatePlayerName(mvpPlayer.Nickname);
                    int roundScore = ExperienceSystemHelper.Instance.GetPlayerRoundExperience(mvpPlayer);

                    // 创建极致美观的MVP显示
                    string mvpMessage = $"<align=center>" +
                        $"<color=#FFD700><size=35><b>★ MVP ★</b></size></color>\n" +
                        $"<color=#FFFF00><size=28><b>{displayName}</b></size></color>\n" +
                        $"<color=#FFFFFF><size=22>━━━━━━━━━━━━━━━━━━━━</size></color>\n" +
                        $"<color=#FFA500><size=24>🏆 当局得分: <b>{score:F2}</b></size></color>\n" +
                        $"<color=#00FF00><size=24>⚡ 回合经验: <b>+{roundScore}</b></size></color>\n" +
                        $"<color=#FFFFFF><size=22>━━━━━━━━━━━━━━━━━━━━</size></color>" +
                        $"</align>";

                    foreach (var player in XHelper.PlayerList.Where(p => p != null))
                    {
                        var mvpHint = new Hint
                        {
                            Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center,
                            YCoordinate = 120, // 稍微调高一点，确保在友伤显示上方
                            FontSize = 30, // 增大字体
                            LineHeight = 8, // 增加行间距
                            Text = mvpMessage
                        };

                        player.AddHint(mvpHint);

                        // 20秒后移除MVP显示（延长显示时间）
                        Timing.CallDelayed(20f, () =>
                        {
                            try
                            {
                                player.RemoveHint(mvpHint);
                            }
                            catch (Exception ex)
                            {
                                Logger.Debug($"移除MVP显示失败: {ex.Message}");
                            }
                        });
                    }

                    Logger.Info($"🏆 MVP显示已展示 - 玩家: {displayName}, 当局得分: {score:F2}, 回合经验: +{roundScore}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"显示MVP信息失败: {ex.Message}");
                    Logger.Debug($"ShowMVPDisplay详细错误: {ex}");
                }
            }

            private void ShowLevelUpPlayers()
            {
                try
                {
                    var levelUpData = Plugin.PlayerLevelUps;
                    if (levelUpData == null || !levelUpData.Any())
                    {
                        Logger.Debug("没有玩家升级数据");
                        return;
                    }

                    foreach (var levelUpEntry in levelUpData)
                    {
                        Player player = null;
                        try
                        {
                            player = Player.Get(userId: levelUpEntry.Key);
                        }
                        catch (Exception ex)
                        {
                            Logger.Debug($"获取升级玩家失败: {ex.Message}");
                            continue;
                        }

                        if (player != null && !player.DoNotTrack)
                        {
                            int levelBefore = levelUpEntry.Value[0];
                            int levelAfter = levelUpEntry.Value[1];

                            // 显示升级信息
                            ShowPlayerLevelUpDisplay(player, levelBefore, levelAfter);

                            Logger.Debug($"结算显示升级信息 - 玩家: {player.Nickname}, {levelBefore} -> {levelAfter}");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"显示升级玩家信息失败: {ex.Message}");
                    Logger.Debug($"ShowLevelUpPlayers详细错误: {ex}");
                }
            }

            private void ShowPlayerLevelUpDisplay(Player player, int levelBefore, int levelAfter)
            {
                try
                {
                    // 为升级玩家显示升级信息
                    string displayName = TruncatePlayerName(player.Nickname);
                    string levelUpMessage = $"<color=#FFD700><size=30><b>恭喜升级！</b></size></color>\n<color=#FFFF00>{displayName}</color>\n<color=#FFFFFF>{levelBefore} → {levelAfter}</color>";

                    var levelUpHint = new Hint
                    {
                        Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center,
                        YCoordinate = 180, // 在MVP下方显示（MVP在Y=150）
                        FontSize = 25,
                        LineHeight = 5,
                        Text = levelUpMessage
                    };

                    player.AddHint(levelUpHint);

                    // 15秒后移除升级显示
                    Timing.CallDelayed(15f, () =>
                    {
                        player.RemoveHint(levelUpHint);
                    });

                    // 显示升级经验进度条，15秒显示时长
                    Helper.Players.ExperienceHelper.Instance.ShowExperienceProgress(player, 0, true, 15f);
                }
                catch (Exception ex)
                {
                    Logger.Error($"显示玩家升级信息失败 - 玩家: {player?.Nickname ?? "Unknown"}, 错误: {ex.Message}");
                    Logger.Debug($"ShowPlayerLevelUpDisplay详细错误: {ex}");
                }
            }

            // ShowRoundEndLeaderboards方法已移除，避免与实时排行榜重复

            private string GetRankColor(int rank)
            {
                return rank switch
                {
                    1 => "#FFD700", // 金色
                    2 => "#C0C0C0", // 银色
                    3 => "#CD7F32", // 铜色
                    _ => "white"
                };
            }
        }
        private class PlayerJoinProcessor
        {
            private readonly PlayerJoinedEventArgs _event;

            public PlayerJoinProcessor(PlayerJoinedEventArgs ev)
            {
                _event = ev;
            }

            public void ProcessJoin()
            {
                var Player = _event.Player;

                if (Player == null || string.IsNullOrEmpty(Player.UserId)) return;
                XHelper.PlayerList.Add(Player);
                XHelper.SpecialPlayerList.Add(Player);

                // 初始化玩家数据条目
                PlayerDataService.AddOrUpdateKillData(Player.UserId, 0, 0, 0);

                ChatHelper.Instance.InitForPlayer(Player);
                SpectatorHelper.Instance.InitForPlayer(Player);
                RoundHelper.Instance.InitForPlayer(Player);
                Helper.Players.ExperienceHelper.Instance.InitForPlayer(Player);
                TimerDisplayHelper.Instance.InitForPlayer(Player);

                // 初始化顶部UI显示
                BlackRoseServer.Display.TopUIDisplay.Instance.ShowTopUI(Player);

                // 延迟处理玩家数据（包括DNT玩家）
                Timing.CallDelayed(1f, () =>
                {
                    try
                    {
                        Logger.Info($"开始处理玩家数据 - 玩家: {Player.Nickname}");
                        ProcessPlayerData(Player);
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"ProcessPlayerData异常 - 玩家: {Player.Nickname}, 错误: {ex.Message}");
                    }
                });

                if (Player.DoNotTrack)
                {
                    Player.SendConsoleMessage($"[警告] 当您启用DNT时我们只可以给您提供最基础的功能，为了您的正常游戏体验，请关闭DNT", "white");

                    // 显示DNT警告Hint（屏幕中心，10秒）
                    var dntWarningConfig = new BlackRoseServer.Manager.HintConfig
                    {
                        Type = BlackRoseServer.Manager.HintType.Custom,
                        Text = "<color=#FF6B6B><size=28><b>[警告]</b></size></color>\n" +
                               "<color=#FFFFFF>您启用了DNT，这意味着我们不会收集您的</color>\n" +
                               "<color=#FFFFFF>除了为了服务器安全以外的信息</color>\n" +
                               "<color=#FFFF00>为了您的良好游戏体验，请关闭DNT</color>",
                        YCoordinate = 400, // 屏幕中心
                        FontSize = 24,
                        Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center,
                        Duration = 10f, // 10秒
                        LifecycleType = BlackRoseServer.Manager.HintLifecycleType.Temporary,
                        Priority = BlackRoseServer.Manager.HintPriority.High,
                        CustomId = $"dnt_warning_{Player.UserId}",
                        AllowOverlap = true
                    };

                    BlackRoseServer.Manager.HintManager.Instance.ShowHint(Player, dntWarningConfig);

                    return; // DNT玩家跳过后续处理
                }

                Player.AddHint(new Hint() { Text = "" , YCoordinate = 160});

                // 处理VIP进服提示
                Timing.CallDelayed(0.5f, () =>
                {
                    ProcessVIPJoinMessage(Player);
                });
            }

            private void ProcessVIPJoinMessage(Player player)
            {
                try
                {
                    string vipMessage = player.GetJoinedInfo();
                    if (!string.IsNullOrEmpty(vipMessage))
                    {
                        // VIP进服广播显示
                        Server.SendBroadcast(vipMessage, 8, Broadcast.BroadcastFlags.Normal, true);

                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"处理VIP进服消息失败 - 玩家: {player?.Nickname ?? "Unknown"}, 错误: {ex.Message}");

                }
            }

            private void ProcessPlayerData(Player Player)
            {
                try
                {


                    if (!Player.CheckPlayer())
                    {
                        Player.SavePlayer();

                    }

                    // 数据库昵称操作（如果需要）
                    Player.SetNickName(Player.Nickname);

                    // 直接设置DisplayName来触发SetNickPatch，这样才能显示等级
                    Player.DisplayName = Player.Nickname;


                    ProcessPlayerGroup(Player);
                    ProcessPlayerBadge(Player);


                }
                catch (Exception ex)
                {
                    Logger.Error($"ProcessPlayerData内部异常 - 玩家: {Player.Nickname}, 错误: {ex.Message}");

                }
            }

            private void ProcessPlayerGroup(Player Player)
            {


                if (Player == null)
                {
                    Logger.Error("ProcessPlayerGroup: Player为null");
                    return;
                }

                try
                {


                    // 从数据库获取权限组名称
                    string permissionName = Player.GetPermissionName();



                    if (!string.IsNullOrEmpty(permissionName))
                    {
                        // 获取服务器权限组配置
                        var group = ServerStatic.PermissionsHandler.GetGroup(permissionName);
                        if (group != null)
                        {
                            Player.UserGroup = group;
                            Logger.Info($"玩家 {Player.Nickname} 成功应用权限组: {permissionName}");
                        }
                        else
                        {
                            var availableGroups = ServerStatic.PermissionsHandler.Groups.Keys.ToArray();
                            Logger.Error($"权限组 '{permissionName}' 不存在! 玩家: {Player.Nickname}");
                            Logger.Error($"可用权限组: [{string.Join(", ", availableGroups)}]");
                            Logger.Error($"请检查 config_remoteadmin.txt 文件中是否定义了 '{permissionName}' 权限组");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"处理玩家权限组失败 - 玩家: {Player.Nickname}, 错误: {ex.Message}");
                    Logger.Error($"异常堆栈: {ex.StackTrace}");
                }


            }

            private void ProcessPlayerBadge(Player Player)
            {
                if (Player == null) return;

                try
                {
                    // 首先停止所有现有的称号协程
                    StopPlayerBadgeCoroutines(Player);

                    // 使用新的GetVisibleBadge方法，自动排除隐藏的称号
                    var (badge, color) = Player.GetVisibleBadge();

                    if (!string.IsNullOrEmpty(badge))
                    {
                        Logger.Debug($"{Player.Nickname} | {badge} | {color} | 可见称号");

                        // 检查是否有有效的称号（通过TryGetBadge获取过期时间）
                        if (Player.TryGetBadge(out _, out _, out DateTime time) && time > DateTime.Now)
                        {
                            if (color != "colorful")
                            {
                                if (!string.IsNullOrEmpty(badge))
                                {
                                    if (badge.Contains(","))
                                    {
                                        string[] badges = badge.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                                        Timing.RunCoroutine(XHelper.BadgeHandle(Player, badges).CancelWith(Player.GameObject));
                                    }
                                    else
                                    {
                                        Player.GroupName = badge;
                                    }
                                }
                                Player.GroupColor = color;
                            }
                            else
                            {
                                if (badge.Contains(","))
                                {
                                    string[] badges = badge.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                                    Timing.RunCoroutine(XHelper.BadgeHandle(Player, badges).CancelWith(Player.GameObject));
                                }
                                else
                                {
                                    Player.GroupName = badge;
                                }
                                Timing.RunCoroutine(XHelper.RainbowBadge(Player).CancelWith(Player.GameObject));
                            }
                        }
                    }
                    else
                    {
                        // 称号被隐藏或没有称号，清空显示
                        Player.GroupName = "";
                        Player.GroupColor = "default";

                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"处理玩家称号失败 - 玩家: {Player.Nickname}, 错误: {ex.Message}");
                }
            }

            /// <summary>
            /// 停止玩家的所有称号协程
            /// </summary>
            private void StopPlayerBadgeCoroutines(Player player)
            {
                try
                {
                    if (player?.GameObject == null) return;

                    // 停止所有与该玩家GameObject关联的协程
                    // 这会停止BadgeHandle和RainbowBadge协程
                    Timing.KillCoroutines(player.GameObject);


                }
                catch (Exception ex)
                {
                    Logger.Error($"停止玩家称号协程失败: {ex.Message}");
                }
            }
        }

        private class PlayerLeftProcessor
        {
            private readonly PlayerLeftEventArgs _event;

            public PlayerLeftProcessor(PlayerLeftEventArgs ev)
            {
                _event = ev;
            }

            public void ProcessLeft()
            {
                var Player = _event.Player;

                if (Player == null || string.IsNullOrEmpty(Player.UserId)) return;

                XHelper.PlayerList.Remove(Player);
                XHelper.SpecialPlayerList.Remove(Player);

                // 清理顶部UI显示
                BlackRoseServer.Display.TopUIDisplay.Instance.RemoveTopUI(Player);
            }
        }

        private class RoundRestartProcessor
        {
            public void ProcessRestart()
            {
                XHelper.PlayerList.Clear();
                XHelper.SpecialPlayerList.Clear();

                PlayerDataService.Reset();
                RoundHelper.Instance.Reset();
                Helper.Players.ExperienceHelper.Instance.Reset();
                TimerDisplayHelper.Instance.Cleanup();

                // 清理右下角显示系统
                try
                {
                    RightBottomDisplayManager.DisposeInstance();
                    Logger.Debug("回合重启时清理右下角显示系统完成");
                }
                catch (Exception ex)
                {
                    Logger.Error($"回合重启时清理右下角显示系统失败: {ex.Message}");
                }
            }
        }

        private class ElevatorInteractionProcessor
        {
            private readonly PlayerInteractedElevatorEventArgs _event;

            public ElevatorInteractionProcessor(PlayerInteractedElevatorEventArgs ev)
            {
                _event = ev;
            }

            public void ProcessInteraction()
            {
                try
                {
                    var player = _event.Player;
                    var elevator = _event.Elevator;

                    if (player == null || elevator == null) return;

                    string displayName = TruncatePlayerName(player.Nickname);
                    string interactionMessage = $"<color=#00FFFF><size=25>[电梯交互]</size></color>\n<color=#FFFFFF>{displayName}</color>";

                    // 获取附近5米范围内的玩家
                    var nearbyPlayers = XHelper.PlayerList.Where(p => p != null &&
                        Vector3.Distance(p.Position, player.Position) <= 5f).ToList();

                    foreach (var nearbyPlayer in nearbyPlayers)
                    {
                        var elevatorHint = new Hint
                        {
                            Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center,
                            YCoordinate = 800,
                            FontSize = 20,
                            LineHeight = 5,
                            Text = interactionMessage
                        };

                        nearbyPlayer.AddHint(elevatorHint);

                        // 3秒后移除显示
                        Timing.CallDelayed(3f, () =>
                        {
                            nearbyPlayer.RemoveHint(elevatorHint);
                        });
                    }

                    Logger.Debug($"电梯交互UI显示 - 交互者: {player.Nickname}, 楼层: {elevator.CurrentDestinationLevel}, 附近玩家数: {nearbyPlayers.Count}");
                }
                catch (Exception ex)
                {
                    Logger.Error($"处理电梯交互失败: {ex.Message}");
                    Logger.Debug($"ElevatorInteractionProcessor详细错误: {ex}");
                }
            }
        }
    }
}
