using System;
using System.Collections.Generic;
using UnityEngine;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.Helper;
using Logger = LabApi.Features.Console.Logger;

namespace BlackRoseServer.Display
{
    /// <summary>
    /// 位置区域检测系统
    /// </summary>
    public static class ZoneDetector
    {
        /// <summary>
        /// 区域类型枚举
        /// </summary>
        public enum ZoneType
        {
            /// <summary>
            /// 地表区域
            /// </summary>
            Surface,
            
            /// <summary>
            /// 轻收容区
            /// </summary>
            LightContainment,
            
            /// <summary>
            /// 重收容区
            /// </summary>
            HeavyContainment,
            
            /// <summary>
            /// 入口区域
            /// </summary>
            Entrance,
            
            /// <summary>
            /// 未知区域
            /// </summary>
            Unknown
        }

        /// <summary>
        /// 区域名称映射
        /// </summary>
        private static readonly Dictionary<ZoneType, string> ZoneNames = new Dictionary<ZoneType, string>
        {
            [ZoneType.Surface] = "地表",
            [ZoneType.LightContainment] = "轻收",
            [ZoneType.HeavyContainment] = "重收",
            [ZoneType.Entrance] = "入口",
            [ZoneType.Unknown] = "未知"
        };

        /// <summary>
        /// 区域颜色映射
        /// </summary>
        private static readonly Dictionary<ZoneType, string> ZoneColors = new Dictionary<ZoneType, string>
        {
            [ZoneType.Surface] = "#87CEEB",        // 天蓝色
            [ZoneType.LightContainment] = "#90EE90", // 浅绿色
            [ZoneType.HeavyContainment] = "#FFB6C1", // 浅粉色
            [ZoneType.Entrance] = "#F0E68C",       // 卡其色
            [ZoneType.Unknown] = "#D3D3D3"         // 浅灰色
        };

        /// <summary>
        /// 获取玩家当前所在区域
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>区域类型</returns>
        public static ZoneType GetPlayerZone(Player player)
        {
            if (player == null || !player.IsAlive)
                return ZoneType.Unknown;

            try
            {
                var position = player.Position;
                return GetZoneByPosition(position);
            }
            catch (Exception ex)
            {
                Logger.Error($"获取玩家区域失败: {ex.Message}");
                return ZoneType.Unknown;
            }
        }

        /// <summary>
        /// 根据位置坐标判断区域
        /// </summary>
        /// <param name="position">位置坐标</param>
        /// <returns>区域类型</returns>
        public static ZoneType GetZoneByPosition(Vector3 position)
        {
            try
            {
                // 地表区域判断 (Y坐标通常较高)
                if (position.y > 900f)
                {
                    return ZoneType.Surface;
                }

                // 根据X和Z坐标判断区域
                // 这些数值可能需要根据实际地图调整
                
                // 轻收容区 (通常在地图的一侧)
                if (position.x > 0f && position.z < 0f)
                {
                    return ZoneType.LightContainment;
                }
                
                // 重收容区 (通常在地图的另一侧)
                if (position.x < 0f && position.z < 0f)
                {
                    return ZoneType.HeavyContainment;
                }
                
                // 入口区域 (通常在中央或特定位置)
                if (position.z > 0f)
                {
                    return ZoneType.Entrance;
                }

                return ZoneType.Unknown;
            }
            catch (Exception ex)
            {
                Logger.Error($"根据位置判断区域失败: {ex.Message}");
                return ZoneType.Unknown;
            }
        }

        /// <summary>
        /// 获取区域的显示名称
        /// </summary>
        /// <param name="zone">区域类型</param>
        /// <returns>显示名称</returns>
        public static string GetZoneName(ZoneType zone)
        {
            return ZoneNames.TryGetValue(zone, out var name) ? name : "未知";
        }

        /// <summary>
        /// 获取区域的显示颜色
        /// </summary>
        /// <param name="zone">区域类型</param>
        /// <returns>颜色代码</returns>
        public static string GetZoneColor(ZoneType zone)
        {
            return ZoneColors.TryGetValue(zone, out var color) ? color : "#FFFFFF";
        }

        /// <summary>
        /// 获取带颜色的区域名称
        /// </summary>
        /// <param name="zone">区域类型</param>
        /// <returns>带颜色标签的区域名称</returns>
        public static string GetColoredZoneName(ZoneType zone)
        {
            var name = GetZoneName(zone);
            var color = GetZoneColor(zone);
            return $"<color={color}>{name}</color>";
        }

        /// <summary>
        /// 获取玩家的带颜色区域名称
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>带颜色标签的区域名称</returns>
        public static string GetPlayerColoredZone(Player player)
        {
            var zone = GetPlayerZone(player);
            return GetColoredZoneName(zone);
        }

        /// <summary>
        /// 检查两个玩家是否在同一区域
        /// </summary>
        /// <param name="player1">玩家1</param>
        /// <param name="player2">玩家2</param>
        /// <returns>是否在同一区域</returns>
        public static bool AreInSameZone(Player player1, Player player2)
        {
            if (player1 == null || player2 == null)
                return false;

            var zone1 = GetPlayerZone(player1);
            var zone2 = GetPlayerZone(player2);
            
            return zone1 == zone2 && zone1 != ZoneType.Unknown;
        }

        /// <summary>
        /// 获取区域内的所有玩家
        /// </summary>
        /// <param name="zone">区域类型</param>
        /// <returns>该区域内的玩家列表</returns>
        public static List<Player> GetPlayersInZone(ZoneType zone)
        {
            var playersInZone = new List<Player>();
            
            try
            {
                foreach (var player in XHelper.PlayerList)
                {
                    if (player != null && player.IsAlive && GetPlayerZone(player) == zone)
                    {
                        playersInZone.Add(player);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取区域内玩家失败: {ex.Message}");
            }

            return playersInZone;
        }

        /// <summary>
        /// 获取所有区域的玩家分布统计
        /// </summary>
        /// <returns>区域玩家分布字典</returns>
        public static Dictionary<ZoneType, int> GetZonePlayerCounts()
        {
            var zoneCounts = new Dictionary<ZoneType, int>();
            
            // 初始化所有区域计数为0
            foreach (ZoneType zone in Enum.GetValues(typeof(ZoneType)))
            {
                zoneCounts[zone] = 0;
            }

            try
            {
                foreach (var player in XHelper.PlayerList)
                {
                    if (player != null && player.IsAlive)
                    {
                        var zone = GetPlayerZone(player);
                        zoneCounts[zone]++;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取区域玩家统计失败: {ex.Message}");
            }

            return zoneCounts;
        }

        /// <summary>
        /// 获取区域距离（简单的欧几里得距离）
        /// </summary>
        /// <param name="zone1">区域1</param>
        /// <param name="zone2">区域2</param>
        /// <returns>区域间的相对距离等级</returns>
        public static int GetZoneDistance(ZoneType zone1, ZoneType zone2)
        {
            if (zone1 == zone2)
                return 0;

            // 定义区域间的相对距离
            var zoneDistances = new Dictionary<(ZoneType, ZoneType), int>
            {
                // 相邻区域距离为1
                [(ZoneType.Entrance, ZoneType.LightContainment)] = 1,
                [(ZoneType.Entrance, ZoneType.HeavyContainment)] = 1,
                [(ZoneType.Entrance, ZoneType.Surface)] = 2,
                [(ZoneType.LightContainment, ZoneType.HeavyContainment)] = 2,
                [(ZoneType.LightContainment, ZoneType.Surface)] = 3,
                [(ZoneType.HeavyContainment, ZoneType.Surface)] = 3,
            };

            // 尝试正向和反向查找
            if (zoneDistances.TryGetValue((zone1, zone2), out var distance))
                return distance;
            if (zoneDistances.TryGetValue((zone2, zone1), out distance))
                return distance;

            // 未知区域或无法计算的距离
            return 999;
        }

        /// <summary>
        /// 获取调试信息
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>调试信息字符串</returns>
        public static string GetDebugInfo(Player player)
        {
            if (player == null)
                return "玩家为null";

            try
            {
                var position = player.Position;
                var zone = GetPlayerZone(player);
                var zoneName = GetZoneName(zone);
                
                return $"玩家: {player.Nickname}\n" +
                       $"位置: ({position.x:F1}, {position.y:F1}, {position.z:F1})\n" +
                       $"区域: {zoneName} ({zone})";
            }
            catch (Exception ex)
            {
                return $"获取调试信息失败: {ex.Message}";
            }
        }
    }
}
