using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using MEC;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Extension;

namespace BlackRoseServer.Manager
{
    /// <summary>
    /// Hint生命周期管理器
    /// </summary>
    public class HintLifecycleManager : IDisposable
    {
        /// <summary>
        /// Hint生命周期信息
        /// </summary>
        public class HintLifecycleInfo
        {
            public Hint HintObject { get; set; }
            public HintConfig Config { get; set; }
            public Player Player { get; set; }
            public DateTime CreatedTime { get; set; }
            public DateTime? ExpiryTime { get; set; }
            public string Id { get; set; }
            public bool IsActive { get; set; } = true;
        }

        private readonly ConcurrentDictionary<string, HintLifecycleInfo> _activeHints;
        private readonly ConcurrentDictionary<Player, HashSet<string>> _playerHints;
        private readonly object _syncLock = new object();
        private CoroutineHandle _cleanupCoroutine;
        private bool _disposed = false;
        private volatile bool _isProcessingCleanup = false; // 防止重入

        /// <summary>
        /// 构造函数
        /// </summary>
        public HintLifecycleManager()
        {
            _activeHints = new ConcurrentDictionary<string, HintLifecycleInfo>();
            _playerHints = new ConcurrentDictionary<Player, HashSet<string>>();
            StartCleanupCoroutine();
        }

        /// <summary>
        /// 注册Hint到生命周期管理
        /// </summary>
        /// <param name="hint">Hint对象</param>
        /// <param name="config">配置信息</param>
        /// <param name="player">目标玩家</param>
        /// <returns>Hint的唯一ID</returns>
        public string RegisterHint(Hint hint, HintConfig config, Player player)
        {
            if (hint == null || config == null || player == null)
            {
                Logger.Error("RegisterHint: 参数不能为null");
                return string.Empty;
            }

            try
            {
                string hintId = GenerateHintId(config, player);
                
                var lifecycleInfo = new HintLifecycleInfo
                {
                    HintObject = hint,
                    Config = config,
                    Player = player,
                    CreatedTime = DateTime.Now,
                    Id = hintId
                };

                // 设置过期时间
                if (config.LifecycleType == HintLifecycleType.Temporary && config.Duration > 0)
                {
                    lifecycleInfo.ExpiryTime = DateTime.Now.AddSeconds(config.Duration);
                }

                // 注册到活跃Hint列表
                _activeHints.TryAdd(hintId, lifecycleInfo);

                // 注册到玩家Hint列表（使用更短的锁时间）
                try
                {
                    lock (_syncLock)
                    {
                        if (!_playerHints.ContainsKey(player))
                        {
                            _playerHints[player] = new HashSet<string>();
                        }
                        _playerHints[player].Add(hintId);
                    }
                }
                catch (Exception lockEx)
                {
                    Logger.Debug($"注册玩家Hint列表时锁异常: {lockEx.Message}");
                }

                // Logger.Debug($"Hint已注册到生命周期管理 - ID: {hintId}, 玩家: {player.Nickname}, 类型: {config.Type}");
                return hintId;
            }
            catch (Exception ex)
            {
                Logger.Error($"注册Hint到生命周期管理失败: {ex.Message}");
                Logger.Debug($"RegisterHint详细错误: {ex}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 移除指定的Hint
        /// </summary>
        /// <param name="hintId">Hint ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveHint(string hintId)
        {
            if (string.IsNullOrEmpty(hintId))
                return false;

            try
            {
                if (_activeHints.TryRemove(hintId, out var lifecycleInfo))
                {
                    // 从玩家的Hint列表中移除
                    lock (_syncLock)
                    {
                        if (_playerHints.TryGetValue(lifecycleInfo.Player, out var playerHintSet))
                        {
                            playerHintSet.Remove(hintId);
                            if (playerHintSet.Count == 0)
                            {
                                _playerHints.TryRemove(lifecycleInfo.Player, out _);
                            }
                        }
                    }

                    // 从玩家界面移除Hint
                    try
                    {
                        if (lifecycleInfo.Player?.ReferenceHub != null)
                        {
                            lifecycleInfo.Player.RemoveHint(lifecycleInfo.HintObject);
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.Error($"从玩家界面移除Hint失败: {ex.Message}");
                    }

                    lifecycleInfo.IsActive = false;
                    // Logger.Debug($"Hint已从生命周期管理中移除 - ID: {hintId}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"移除Hint失败: {ex.Message}");
                Logger.Debug($"RemoveHint详细错误: {ex}");
            }

            return false;
        }

        /// <summary>
        /// 移除玩家的所有Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>移除的Hint数量</returns>
        public int RemovePlayerHints(Player player)
        {
            if (player == null)
                return 0;

            try
            {
                int removedCount = 0;
                lock (_syncLock)
                {
                    if (_playerHints.TryRemove(player, out var playerHintSet))
                    {
                        foreach (var hintId in playerHintSet.ToList())
                        {
                            if (RemoveHint(hintId))
                            {
                                removedCount++;
                            }
                        }
                    }
                }

                // Logger.Debug($"已移除玩家 {player.Nickname} 的 {removedCount} 个Hint");
                return removedCount;
            }
            catch (Exception ex)
            {
                Logger.Error($"移除玩家Hint失败: {ex.Message}");
                Logger.Debug($"RemovePlayerHints详细错误: {ex}");
                return 0;
            }
        }

        /// <summary>
        /// 移除指定类型的Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="hintType">Hint类型</param>
        /// <returns>移除的Hint数量</returns>
        public int RemoveHintsByType(Player player, HintType hintType)
        {
            if (player == null)
                return 0;

            try
            {
                int removedCount = 0;
                var hintsToRemove = new List<string>();

                lock (_syncLock)
                {
                    if (_playerHints.TryGetValue(player, out var playerHintSet))
                    {
                        foreach (var hintId in playerHintSet)
                        {
                            if (_activeHints.TryGetValue(hintId, out var lifecycleInfo) &&
                                lifecycleInfo.Config.Type == hintType)
                            {
                                hintsToRemove.Add(hintId);
                            }
                        }
                    }
                }

                foreach (var hintId in hintsToRemove)
                {
                    if (RemoveHint(hintId))
                    {
                        removedCount++;
                    }
                }

                // Logger.Debug($"已移除玩家 {player.Nickname} 的 {removedCount} 个 {hintType} 类型Hint");
                return removedCount;
            }
            catch (Exception ex)
            {
                Logger.Error($"移除指定类型Hint失败: {ex.Message}");
                Logger.Debug($"RemoveHintsByType详细错误: {ex}");
                return 0;
            }
        }

        /// <summary>
        /// 获取玩家的活跃Hint数量
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>活跃Hint数量</returns>
        public int GetPlayerHintCount(Player player)
        {
            if (player == null)
                return 0;

            lock (_syncLock)
            {
                return _playerHints.TryGetValue(player, out var playerHintSet) ? playerHintSet.Count : 0;
            }
        }

        /// <summary>
        /// 检查玩家是否有指定类型的Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="hintType">Hint类型</param>
        /// <returns>是否存在</returns>
        public bool HasHintType(Player player, HintType hintType)
        {
            if (player == null)
                return false;

            lock (_syncLock)
            {
                if (_playerHints.TryGetValue(player, out var playerHintSet))
                {
                    return playerHintSet.Any(hintId =>
                        _activeHints.TryGetValue(hintId, out var lifecycleInfo) &&
                        lifecycleInfo.Config.Type == hintType);
                }
            }

            return false;
        }

        /// <summary>
        /// 生成Hint唯一ID
        /// </summary>
        private string GenerateHintId(HintConfig config, Player player)
        {
            if (!string.IsNullOrEmpty(config.CustomId))
            {
                return $"{player.UserId}_{config.CustomId}";
            }

            return $"{player.UserId}_{config.Type}_{DateTime.Now.Ticks}";
        }

        /// <summary>
        /// 启动清理协程
        /// </summary>
        private void StartCleanupCoroutine()
        {
            _cleanupCoroutine = Timing.RunCoroutine(CleanupExpiredHints());
        }

        /// <summary>
        /// 清理过期Hint的协程
        /// </summary>
        private IEnumerator<float> CleanupExpiredHints()
        {
            while (!_disposed)
            {
                float waitTime = 1f; // 默认等待时间
                bool hasError = false;

                // 防止重入
                if (_isProcessingCleanup)
                {
                    Logger.Debug("清理协程正在处理中，跳过本次清理");
                    yield return Timing.WaitForSeconds(waitTime);
                    continue;
                }

                try
                {
                    _isProcessingCleanup = true;

                    var now = DateTime.Now;
                    var expiredHints = new List<string>();

                    // 查找过期的Hint，使用ToList()避免集合修改异常
                    var activeHintsList = _activeHints.ToList();
                    foreach (var kvp in activeHintsList)
                    {
                        try
                        {
                            var lifecycleInfo = kvp.Value;
                            if (lifecycleInfo.ExpiryTime.HasValue && now >= lifecycleInfo.ExpiryTime.Value)
                            {
                                expiredHints.Add(kvp.Key);
                            }
                        }
                        catch (Exception hintEx)
                        {
                            Logger.Debug($"检查Hint过期时间失败 - ID: {kvp.Key}, 错误: {hintEx.Message}");
                        }
                    }

                    // 移除过期的Hint
                    foreach (var hintId in expiredHints)
                    {
                        try
                        {
                            RemoveHint(hintId);
                        }
                        catch (Exception removeEx)
                        {
                            Logger.Debug($"移除过期Hint失败 - ID: {hintId}, 错误: {removeEx.Message}");
                        }
                    }

                    if (expiredHints.Count > 0)
                    {
                        // Logger.Debug($"清理了 {expiredHints.Count} 个过期的Hint");
                    }
                }
                catch (Exception ex)
                {
                    Logger.Error($"清理过期Hint失败: {ex.Message}");
                    // 如果出现严重错误，设置更长的等待时间
                    waitTime = 5f;
                    hasError = true;
                }
                finally
                {
                    _isProcessingCleanup = false; // 确保标志被重置
                }

                // yield必须在try-catch块外面
                yield return Timing.WaitForSeconds(waitTime);

                if (_disposed) break; // 检查是否已释放
            }
        }

        /// <summary>
        /// 根据CustomId查找Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="customId">自定义ID</param>
        /// <returns>Hint ID，如果未找到返回null</returns>
        public string FindHintByCustomId(Player player, string customId)
        {
            if (player == null || string.IsNullOrEmpty(customId))
                return null;

            try
            {
                lock (_syncLock)
                {
                    if (!_playerHints.TryGetValue(player, out var playerHints))
                        return null;

                    foreach (var hintId in playerHints)
                    {
                        if (_activeHints.TryGetValue(hintId, out var hintInfo) &&
                            hintInfo.Config.CustomId == customId)
                        {
                            return hintId;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"查找CustomId Hint失败: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 更新Hint文本
        /// </summary>
        /// <param name="hintId">Hint ID</param>
        /// <param name="newText">新文本</param>
        /// <returns>是否更新成功</returns>
        public bool UpdateHintText(string hintId, string newText)
        {
            if (string.IsNullOrEmpty(hintId) || string.IsNullOrEmpty(newText))
                return false;

            try
            {
                lock (_syncLock)
                {
                    if (_activeHints.TryGetValue(hintId, out var hintInfo))
                    {
                        hintInfo.HintObject.Text = newText;
                        hintInfo.Config.Text = newText;
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"更新Hint文本失败: {ex.Message}");
            }

            return false;
        }

        /// <summary>
        /// 强制清理所有Hint（回合结束时使用）
        /// </summary>
        public void ForceCleanupAll()
        {
            try
            {
                Logger.Debug("开始强制清理所有Hint...");

                // 停止清理协程
                if (_cleanupCoroutine.IsRunning)
                {
                    Timing.KillCoroutines(_cleanupCoroutine);
                    Logger.Debug("已停止清理协程");
                }

                // 直接清理所有数据，不调用RemoveHint避免复杂的清理逻辑
                var hintCount = _activeHints.Count;
                _activeHints.Clear();
                _playerHints.Clear();

                Logger.Debug($"强制清理完成，清理了 {hintCount} 个Hint");
            }
            catch (Exception ex)
            {
                Logger.Error($"强制清理所有Hint失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            try
            {
                // 停止清理协程
                if (_cleanupCoroutine.IsRunning)
                {
                    Timing.KillCoroutines(_cleanupCoroutine);
                }

                // 清理所有活跃的Hint
                foreach (var kvp in _activeHints.ToList())
                {
                    RemoveHint(kvp.Key);
                }

                _activeHints.Clear();
                _playerHints.Clear();

                Logger.Debug("HintLifecycleManager已释放");
            }
            catch (Exception ex)
            {
                Logger.Error($"释放HintLifecycleManager失败: {ex.Message}");
            }
        }
    }
}
