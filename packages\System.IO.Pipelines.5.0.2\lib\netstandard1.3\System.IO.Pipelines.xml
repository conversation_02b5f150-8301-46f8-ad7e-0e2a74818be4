<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.IO.Pipelines</name>
    </assembly>
    <members>
        <member name="P:System.IO.Pipelines.BufferSegment.Start">
            <summary>
            The Start represents the offset into AvailableMemory where the range of "active" bytes begins. At the point when the block is leased
            the Start is guaranteed to be equal to 0. The value of Start may be assigned anywhere between 0 and
            AvailableMemory.Length, and must be equal to or less than End.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.BufferSegment.End">
            <summary>
            The End represents the offset into AvailableMemory where the range of "active" bytes ends. At the point when the block is leased
            the End is guaranteed to be equal to Start. The value of Start may be assigned anywhere between 0 and
            <PERSON>uff<PERSON>.Length, and must be equal to or less than End.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.BufferSegment.NextSegment">
            <summary>
            Reference to the next block of data when the overall "active" bytes spans multiple blocks. At the point when the block is
            leased Next is guaranteed to be null. Start, End, and Next are used together in order to create a linked-list of discontiguous
            working memory. The "active" memory is grown when bytes are copied in, End is increased, and Next is assigned. The "active"
            memory is shrunk when bytes are consumed, Start is increased, and blocks are returned to the pool.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.BufferSegment.ReadOnly">
            <summary>
            If true, data should not be written into the backing block after the End offset. Data between start and end should never be modified
            since this would break cloning.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.BufferSegment.WritableBytes">
            <summary>
            The amount of writable bytes in this segment. It is the amount of bytes between Length and End
            </summary>
        </member>
        <member name="T:System.IO.Pipelines.FlushResult">
            <summary>
            Result returned by <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)"/> call
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.FlushResult.#ctor(System.Boolean,System.Boolean)">
            <summary>
            Creates a new instance of <see cref="T:System.IO.Pipelines.FlushResult"/> setting <see cref="P:System.IO.Pipelines.FlushResult.IsCanceled"/> and <see cref="P:System.IO.Pipelines.FlushResult.IsCompleted"/> flags
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.FlushResult.IsCanceled">
            <summary>
            True if the current <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)"/> operation was canceled, otherwise false.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.FlushResult.IsCompleted">
            <summary>
            True if the <see cref="T:System.IO.Pipelines.PipeWriter"/> is complete otherwise false
            </summary>
        </member>
        <member name="T:System.IO.Pipelines.IDuplexPipe">
            <summary>
            Defines a class that provides a duplex pipe from which data can be read from and written to.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.IDuplexPipe.Input">
            <summary>
            Gets the <see cref="T:System.IO.Pipelines.PipeReader"/> half of the duplex pipe.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.IDuplexPipe.Output">
            <summary>
            Gets the <see cref="T:System.IO.Pipelines.PipeWriter"/> half of the duplex pipe.
            </summary>
        </member>
        <member name="T:System.IO.Pipelines.Pipe">
            <summary>
            Default <see cref="T:System.IO.Pipelines.PipeWriter"/> and <see cref="T:System.IO.Pipelines.PipeReader"/> implementation.
            </summary>
            <summary>
            Default <see cref="T:System.IO.Pipelines.PipeWriter"/> and <see cref="T:System.IO.Pipelines.PipeReader"/> implementation.
            </summary>
            <summary>
            Default <see cref="T:System.IO.Pipelines.PipeWriter"/> and <see cref="T:System.IO.Pipelines.PipeReader"/> implementation.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.Pipe.#ctor">
            <summary>
            Initializes the <see cref="T:System.IO.Pipelines.Pipe"/> using <see cref="P:System.IO.Pipelines.PipeOptions.Default"/> as options.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.Pipe.#ctor(System.IO.Pipelines.PipeOptions)">
            <summary>
            Initializes the <see cref="T:System.IO.Pipelines.Pipe"/> with the specified <see cref="T:System.IO.Pipelines.PipeOptions"/>.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.Pipe.Reader">
            <summary>
            Gets the <see cref="T:System.IO.Pipelines.PipeReader"/> for this pipe.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.Pipe.Writer">
            <summary>
            Gets the <see cref="T:System.IO.Pipelines.PipeWriter"/> for this pipe.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.Pipe.Reset">
            <summary>
            Resets the pipe
            </summary>
        </member>
        <member name="T:System.IO.Pipelines.PipeOptions">
            <summary>
            Represents a set of <see cref="T:System.IO.Pipelines.Pipe"/> options
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.Default">
            <summary>
            Default instance of <see cref="T:System.IO.Pipelines.PipeOptions"/>
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.PipeOptions.#ctor(System.Buffers.MemoryPool{System.Byte},System.IO.Pipelines.PipeScheduler,System.IO.Pipelines.PipeScheduler,System.Int64,System.Int64,System.Int32,System.Boolean)">
            <summary>
            Creates a new instance of <see cref="T:System.IO.Pipelines.PipeOptions"/>
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.UseSynchronizationContext">
            <summary>
            Gets a value that determines if asynchronous callbacks should be executed on the <see cref="T:System.Threading.SynchronizationContext" /> they were captured on.
            This takes precedence over the schedulers specified in <see cref="P:System.IO.Pipelines.PipeOptions.ReaderScheduler"/> and <see cref="P:System.IO.Pipelines.PipeOptions.WriterScheduler"/>.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.PauseWriterThreshold">
            <summary>
            Gets amount of bytes in <see cref="T:System.IO.Pipelines.Pipe"/> when <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)"/> starts blocking
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.ResumeWriterThreshold">
            <summary>
            Gets amount of bytes in <see cref="T:System.IO.Pipelines.Pipe"/> when <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)"/> stops blocking
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.MinimumSegmentSize">
            <summary>
            Gets minimum size of segment requested from <see cref="P:System.IO.Pipelines.PipeOptions.Pool"/>
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.WriterScheduler">
            <summary>
            Gets the <see cref="T:System.IO.Pipelines.PipeScheduler"/> used to execute <see cref="T:System.IO.Pipelines.PipeWriter"/> callbacks
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.ReaderScheduler">
            <summary>
            Gets the <see cref="T:System.IO.Pipelines.PipeScheduler"/> used to execute <see cref="T:System.IO.Pipelines.PipeReader"/> callbacks
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeOptions.Pool">
            <summary>
            Gets the <see cref="T:System.Buffers.MemoryPool`1"/> instances used for buffer management
            </summary>
        </member>
        <member name="T:System.IO.Pipelines.PipeReader">
            <summary>
            Defines a class that provides access to a read side of pipe.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.TryRead(System.IO.Pipelines.ReadResult@)">
            <summary>
            Attempt to synchronously read data the <see cref="T:System.IO.Pipelines.PipeReader"/>.
            </summary>
            <param name="result">The <see cref="T:System.IO.Pipelines.ReadResult"/></param>
            <returns>True if data was available, or if the call was canceled or the writer was completed.</returns>
            <remarks>If the pipe returns false, there's no need to call <see cref="M:System.IO.Pipelines.PipeReader.AdvanceTo(System.SequencePosition,System.SequencePosition)"/>.</remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads a sequence of bytes from the current <see cref="T:System.IO.Pipelines.PipeReader"/>.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.ValueTask`1"/> representing the asynchronous read operation.</returns>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.AdvanceTo(System.SequencePosition)">
            <summary>
            Moves forward the pipeline's read cursor to after the consumed data.
            </summary>
            <param name="consumed">Marks the extent of the data that has been successfully processed.</param>
            <remarks>
            The memory for the consumed data will be released and no longer available.
            The examined data communicates to the pipeline when it should signal more data is available.
            </remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.AdvanceTo(System.SequencePosition,System.SequencePosition)">
            <summary>
            Moves forward the pipeline's read cursor to after the consumed data.
            </summary>
            <param name="consumed">Marks the extent of the data that has been successfully processed.</param>
            <param name="examined">Marks the extent of the data that has been read and examined.</param>
            <remarks>
            The memory for the consumed data will be released and no longer available.
            The examined data communicates to the pipeline when it should signal more data is available.
            </remarks>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.CancelPendingRead">
            <summary>
            Cancel to currently pending or if none is pending next call to <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)"/>, without completing the <see cref="T:System.IO.Pipelines.PipeReader"/>.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.Complete(System.Exception)">
            <summary>
            Signal to the producer that the consumer is done reading.
            </summary>
            <param name="exception">Optional <see cref="T:System.Exception"/> indicating a failure that's causing the pipeline to complete.</param>
        </member>
        <member name="M:System.IO.Pipelines.PipeReader.OnWriterCompleted(System.Action{System.Exception,System.Object},System.Object)">
            <summary>
            Cancel the pending <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)"/> operation. If there is none, cancels next <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)"/> operation, without completing the <see cref="T:System.IO.Pipelines.PipeWriter"/>.
            </summary>
        </member>
        <member name="T:System.IO.Pipelines.PipeScheduler">
            <summary>
            Abstraction for running <see cref="T:System.IO.Pipelines.PipeReader"/> and <see cref="T:System.IO.Pipelines.PipeWriter"/> callbacks and continuations
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeScheduler.ThreadPool">
            <summary>
            The <see cref="T:System.IO.Pipelines.PipeScheduler"/> implementation that queues callbacks to thread pool
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.PipeScheduler.Inline">
            <summary>
            The <see cref="T:System.IO.Pipelines.PipeScheduler"/> implementation that runs callbacks inline
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.PipeScheduler.Schedule(System.Action{System.Object},System.Object)">
            <summary>
            Requests <paramref name="action"/> to be run on scheduler with <paramref name="state"/> being passed in
            </summary>
        </member>
        <member name="T:System.IO.Pipelines.PipeWriter">
            <summary>
            Defines a class that provides a pipeline to which data can be written.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.Complete(System.Exception)">
            <summary>
            Marks the <see cref="T:System.IO.Pipelines.PipeWriter"/> as being complete, meaning no more items will be written to it.
            </summary>
            <param name="exception">Optional <see cref="T:System.Exception"/> indicating a failure that's causing the pipeline to complete.</param>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.CancelPendingFlush">
            <summary>
            Cancel the pending <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)"/> operation. If there is none, cancels next <see cref="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)"/> operation, without completing the <see cref="T:System.IO.Pipelines.PipeWriter"/>.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.OnReaderCompleted(System.Action{System.Exception,System.Object},System.Object)">
            <summary>
            Registers a callback that gets executed when the <see cref="T:System.IO.Pipelines.PipeReader"/> side of the pipe is completed
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.FlushAsync(System.Threading.CancellationToken)">
            <summary>
            Makes bytes written available to <see cref="T:System.IO.Pipelines.PipeReader"/> and runs <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)"/> continuation.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.Advance(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.GetMemory(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.GetSpan(System.Int32)">
            <inheritdoc />
        </member>
        <member name="M:System.IO.Pipelines.PipeWriter.WriteAsync(System.ReadOnlyMemory{System.Byte},System.Threading.CancellationToken)">
            <summary>
            Writes <paramref name="source"/> to the pipe and makes data accessible to <see cref="T:System.IO.Pipelines.PipeReader"/>
            </summary>
        </member>
        <member name="T:System.IO.Pipelines.ReadResult">
            <summary>
            The result of a <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)"/> call.
            </summary>
        </member>
        <member name="M:System.IO.Pipelines.ReadResult.#ctor(System.Buffers.ReadOnlySequence{System.Byte},System.Boolean,System.Boolean)">
            <summary>
            Creates a new instance of <see cref="T:System.IO.Pipelines.ReadResult"/> setting <see cref="P:System.IO.Pipelines.ReadResult.IsCanceled"/> and <see cref="P:System.IO.Pipelines.ReadResult.IsCompleted"/> flags
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.ReadResult.Buffer">
            <summary>
            The <see cref="T:System.Buffers.ReadOnlySequence`1"/> that was read
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.ReadResult.IsCanceled">
            <summary>
            True if the current <see cref="M:System.IO.Pipelines.PipeReader.ReadAsync(System.Threading.CancellationToken)"/> operation was canceled, otherwise false.
            </summary>
        </member>
        <member name="P:System.IO.Pipelines.ReadResult.IsCompleted">
            <summary>
            True if the <see cref="T:System.IO.Pipelines.PipeReader"/> is complete
            </summary>
        </member>
    </members>
</doc>
