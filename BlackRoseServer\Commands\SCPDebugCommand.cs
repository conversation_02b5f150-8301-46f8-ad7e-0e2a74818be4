using System;
using System.Linq;
using CommandSystem;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.Display;
using BlackRoseServer.Helper;
using BlackRoseServer.Helper.Players;
using PlayerRoles;

namespace BlackRoseServer.Commands
{
    /// <summary>
    /// SCP显示调试命令
    /// </summary>
    [CommandHandler(typeof(RemoteAdminCommandHandler))]
    public class SCPDebugCommand : ICommand
    {
        public string Command => "scpdebug";
        public string[] Aliases => new[] { "scpd" };
        public string Description => "调试SCP显示系统";

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            try
            {
                if (arguments.Count == 0)
                {
                    response = GetUsage();
                    return false;
                }

                var subCommand = arguments.At(0).ToLower();

                switch (subCommand)
                {
                    case "check":
                        return CheckSCPDisplay(out response);
                    
                    case "force":
                        return ForceUpdateSCPDisplay(out response);
                    
                    case "test":
                        return TestSCPTeammates(out response);
                    
                    case "leaderboard":
                        return TestLeaderboard(out response);
                    
                    case "players":
                        return ShowPlayerInfo(out response);
                    
                    default:
                        response = GetUsage();
                        return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"SCP调试命令执行失败: {ex.Message}");
                response = $"命令执行失败: {ex.Message}";
                return false;
            }
        }

        private string GetUsage()
        {
            return "用法: scpdebug <子命令>\n" +
                   "子命令:\n" +
                   "  check - 检查SCP显示系统状态\n" +
                   "  force - 强制更新所有SCP显示\n" +
                   "  test - 测试SCP队友检测\n" +
                   "  leaderboard - 测试回合结束排行榜\n" +
                   "  players - 显示玩家信息";
        }

        private bool CheckSCPDisplay(out string response)
        {
            try
            {
                SCPDisplayDebugger.DebugSCPDisplay();
                response = "SCP显示系统检查完成，请查看服务器日志获取详细信息";
                return true;
            }
            catch (Exception ex)
            {
                response = $"检查SCP显示失败: {ex.Message}";
                return false;
            }
        }

        private bool ForceUpdateSCPDisplay(out string response)
        {
            try
            {
                SCPDisplayDebugger.ForceUpdateAllSCPDisplays();
                response = "强制更新SCP显示完成";
                return true;
            }
            catch (Exception ex)
            {
                response = $"强制更新SCP显示失败: {ex.Message}";
                return false;
            }
        }

        private bool TestSCPTeammates(out string response)
        {
            try
            {
                var allPlayers = Player.List.Where(p => p != null && p.IsReady).ToList();
                var scpPlayers = allPlayers.Where(p => p.IsSCP).ToList();
                
                response = $"玩家统计:\n";
                response += $"总玩家数: {allPlayers.Count}\n";
                response += $"SCP玩家数: {scpPlayers.Count}\n\n";
                
                if (scpPlayers.Count == 0)
                {
                    response += "当前没有SCP玩家";
                    return true;
                }
                
                response += "SCP玩家列表:\n";
                foreach (var player in scpPlayers)
                {
                    response += $"- {player.Nickname} ({player.Role}) - 存活: {player.IsAlive}\n";
                    
                    // 测试队友检测
                    var display = new SCPTeammateDisplay();
                    try
                    {
                        display.ShowSCPTeammateInfo(player);
                        response += $"  ✅ 队友显示更新成功\n";
                    }
                    catch (Exception ex)
                    {
                        response += $"  ❌ 队友显示更新失败: {ex.Message}\n";
                    }
                }
                
                return true;
            }
            catch (Exception ex)
            {
                response = $"测试SCP队友失败: {ex.Message}";
                return false;
            }
        }

        private bool TestLeaderboard(out string response)
        {
            try
            {
                Logger.Info("手动触发回合结束排行榜测试...");
                RoundHelper.Instance.OnRoundEnd();
                response = "回合结束排行榜测试完成，请查看服务器日志和游戏内广播";
                return true;
            }
            catch (Exception ex)
            {
                response = $"测试回合结束排行榜失败: {ex.Message}";
                return false;
            }
        }

        private bool ShowPlayerInfo(out string response)
        {
            try
            {
                var playerData = Plugin.PlayerKillsDeaths;
                var allPlayers = XHelper.PlayerList.Where(p => p != null && p.IsReady).ToList();
                var xhelperPlayers = XHelper.PlayerList.ToList();
                
                response = $"玩家信息统计:\n";
                response += $"XHelper.PlayerList玩家数: {allPlayers.Count}\n";
                response += $"XHelper.PlayerList玩家数: {xhelperPlayers.Count}\n";
                response += $"PlayerKillsDeaths数据数: {playerData?.Count ?? 0}\n\n";

                response += "XHelper.PlayerList中的玩家:\n";
                foreach (var player in allPlayers.Take(10)) // 只显示前10个
                {
                    response += $"- {player.Nickname} ({player.Role}) - 存活: {player.IsAlive}, SCP: {player.IsSCP}\n";
                }
                
                if (allPlayers.Count > 10)
                {
                    response += $"... 还有 {allPlayers.Count - 10} 个玩家\n";
                }
                
                return true;
            }
            catch (Exception ex)
            {
                response = $"显示玩家信息失败: {ex.Message}";
                return false;
            }
        }
    }
}
