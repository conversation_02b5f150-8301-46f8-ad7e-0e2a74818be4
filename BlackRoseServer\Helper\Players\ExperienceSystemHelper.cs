using LabApi.Features.Wrappers;
using LabApi.Events.Arguments.PlayerEvents;
using LabApi.Events.Arguments.Scp079Events;
using LabApi.Events.Arguments.Scp049Events;
using PlayerRoles;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using InventorySystem.Items;
using PlayerStatsSystem;
using BlackRoseServer.API;
using BlackRoseServer.Display;
using LabApi.Features.Console;
using Logger = LabApi.Features.Console.Logger;

namespace BlackRoseServer.Helper.Players
{
    /// <summary>
    /// 经验系统核心助手类
    /// </summary>
    public class ExperienceSystemHelper
    {
        private static ExperienceSystemHelper _instance;
        public static ExperienceSystemHelper Instance => _instance ??= new ExperienceSystemHelper();

        // 伤害阈值跟踪
        private readonly ConcurrentDictionary<string, float> _humanDamageAccumulator = new();
        private readonly ConcurrentDictionary<string, float> _scpDamageAccumulator = new();
        
        // 首次激活跟踪
        private readonly ConcurrentDictionary<string, HashSet<string>> _playerFirstActivations = new();

        // 玩家当局经验记录（每回合重置）
        private readonly ConcurrentDictionary<string, int> _playerRoundExperience = new();

        // 经验常量
        private const float HUMAN_DAMAGE_THRESHOLD = 250f;
        private const float SCP_DAMAGE_THRESHOLD = 65f;
        private const float SCP173_NECK_DAMAGE = 100f;

        // 经验值常量
        private const int HUMAN_DAMAGE_XP = 1;
        private const int HUMAN_KILL_HUMAN_XP = 3;
        private const int HUMAN_KILL_SCP_XP = 15;
        private const int SCP_DAMAGE_XP = 1;
        private const int SCP_KILL_HUMAN_XP = 2;
        private const int SCP173_KILL_XP = 3;
        private const int ESCAPE_XP = 20;
        private const int SCP_ITEM_USE_XP = 3;
        private const int FIRST_GENERATOR_XP = 3;

        private const int SCP049_RESURRECT_XP = 2;

        /// <summary>
        /// 处理玩家受伤事件，计算伤害阈值经验
        /// </summary>
        public void ProcessPlayerHurt(PlayerHurtEventArgs ev)
        {
            if (ev.Attacker == null || ev.Player == null || ev.Attacker == ev.Player)
                return;

            var attackerId = ev.Attacker.UserId;
            var damage = 0f;

            // 从DamageHandler获取伤害值
            if (ev.DamageHandler is StandardDamageHandler standardHandler)
            {
                damage = standardHandler.Damage;
            }

            // 特殊处理SCP173扭脖
            if (ev.Attacker.Role == RoleTypeId.Scp173 && IsNeckSnapDamage(ev.DamageHandler))
            {
                damage = SCP173_NECK_DAMAGE;
            }

            if (ev.Attacker.IsSCP)
            {
                ProcessScpDamageExperience(attackerId, damage);
            }
            else
            {
                ProcessHumanDamageExperience(attackerId, damage);
            }
        }

        /// <summary>
        /// 处理人类伤害经验
        /// </summary>
        private void ProcessHumanDamageExperience(string playerId, float damage)
        {
            var currentDamage = _humanDamageAccumulator.GetOrAdd(playerId, 0f);
            currentDamage += damage;

            var xpGained = 0;
            while (currentDamage >= HUMAN_DAMAGE_THRESHOLD)
            {
                xpGained += HUMAN_DAMAGE_XP;
                currentDamage -= HUMAN_DAMAGE_THRESHOLD;
            }

            _humanDamageAccumulator[playerId] = currentDamage;

            if (xpGained > 0)
            {
                var player = Player.Get(userId: playerId);
                if (player != null)
                {
                    GiveExperience(player, xpGained, $"造成伤害获得{xpGained}经验");
                }
            }
        }

        /// <summary>
        /// 处理SCP伤害经验
        /// </summary>
        private void ProcessScpDamageExperience(string playerId, float damage)
        {
            var currentDamage = _scpDamageAccumulator.GetOrAdd(playerId, 0f);
            currentDamage += damage;

            var xpGained = 0;
            while (currentDamage >= SCP_DAMAGE_THRESHOLD)
            {
                xpGained += SCP_DAMAGE_XP;
                currentDamage -= SCP_DAMAGE_THRESHOLD;
            }

            _scpDamageAccumulator[playerId] = currentDamage;

            if (xpGained > 0)
            {
                var player = Player.Get(userId: playerId);
                if (player != null)
                {
                    GiveExperience(player, xpGained, $"造成伤害获得{xpGained}经验");
                }
            }
        }

        /// <summary>
        /// 判断是否为SCP173扭脖伤害
        /// </summary>
        private bool IsNeckSnapDamage(object damageHandler)
        {
            // 根据伤害处理器类型判断是否为扭脖
            if (damageHandler == null) return false;

            var typeName = damageHandler.GetType().Name;
            // 检查是否为SCP173相关的伤害类型
            return typeName.Contains("Scp173") ||
                   typeName.Contains("NeckSnap") ||
                   typeName.Contains("Snap") ||
                   (damageHandler is StandardDamageHandler standardHandler && standardHandler.Damage <= 0);
        }

        /// <summary>
        /// 处理玩家死亡事件，计算击杀经验
        /// </summary>
        public void ProcessPlayerDying(PlayerDyingEventArgs ev)
        {
            if (ev.Attacker == null || ev.Player == null || ev.Attacker == ev.Player)
                return;

            var xpAmount = CalculateKillExperience(ev.Attacker, ev.Player, ev.DamageHandler);
            if (xpAmount > 0)
            {
                var message = GetKillMessage(ev.Attacker, ev.Player, xpAmount);
                GiveExperience(ev.Attacker, xpAmount, message);
            }

            // SCP079经验已简化，不再需要复杂的协助击杀检查
        }

        /// <summary>
        /// 计算击杀经验
        /// </summary>
        private int CalculateKillExperience(Player attacker, Player target, object damageHandler)
        {
            // SCP173特殊击杀经验
            if (attacker.Role == RoleTypeId.Scp173)
            {
                // damageHandler可用于进一步区分击杀类型，目前统一给予SCP173击杀经验
                return SCP173_KILL_XP;
            }

            // 人类击杀
            if (!attacker.IsSCP)
            {
                if (target.IsSCP && target.Role != RoleTypeId.Scp0492)
                {
                    return HUMAN_KILL_SCP_XP; // 击杀SCP
                }
                else
                {
                    return HUMAN_KILL_HUMAN_XP; // 击杀人类/小僵尸
                }
            }
            // SCP击杀
            else
            {
                return SCP_KILL_HUMAN_XP; // SCP击杀人类
            }
        }

        /// <summary>
        /// 获取击杀消息
        /// </summary>
        private string GetKillMessage(Player attacker, Player target, int xp)
        {
            if (attacker.Role == RoleTypeId.Scp173)
            {
                return $"SCP173击杀获得{xp}经验";
            }

            if (!attacker.IsSCP)
            {
                if (target.IsSCP && target.Role != RoleTypeId.Scp0492)
                {
                    return $"击杀SCP获得{xp}经验";
                }
                else
                {
                    return $"击杀获得{xp}经验";
                }
            }
            else
            {
                return $"击杀获得{xp}经验";
            }
        }

        /// <summary>
        /// 给予玩家经验
        /// </summary>
        private void GiveExperience(Player player, int amount, string message)
        {
            try
            {
                // DNT玩家不增加经验
                if (player.DoNotTrack)
                {
                    Logger.Debug($"玩家 {player.Nickname} 处于DNT状态，跳过经验增加");
                    return;
                }

                int levelBefore = player.GetLVL();
                player.AddXP(amount);
                int levelAfter = player.GetLVL();

                bool isLevelUp = levelAfter > levelBefore;

                // 记录当局经验
                _playerRoundExperience.AddOrUpdate(player.UserId, amount, (key, oldValue) => oldValue + amount);

                // 播放获取经验音效（每次获得经验都播放）
                Plugin.PlayExperienceGainSound(player);

                // 如果升级了，记录升级信息
                if (isLevelUp)
                {
                    Logger.Debug($"玩家 {player.Nickname} 升级到 {levelAfter} 级");
                }

                // 使用统一的经验显示系统，带原因显示
                if (!string.IsNullOrEmpty(message))
                {
                    RightBottomDisplayManager.Instance.ShowExperienceGainWithMessage(player, amount, message);
                }
                else
                {
                    RightBottomDisplayManager.Instance.ShowExperienceGain(player, amount, isLevelUp);
                }

                // 记录经验获得日志
                Logger.Debug($"玩家 {player.Nickname} {message}");
            }
            catch (System.Exception ex)
            {
                Logger.Error($"给予经验时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取玩家当局经验值
        /// </summary>
        public int GetPlayerRoundExperience(Player player)
        {
            if (player == null) return 0;
            return _playerRoundExperience.TryGetValue(player.UserId, out var experience) ? experience : 0;
        }

        /// <summary>
        /// 获取所有玩家的当局经验值（用于排行榜，避免Player.Get调用）
        /// </summary>
        public Dictionary<string, int> GetAllPlayerRoundExperience()
        {
            return new Dictionary<string, int>(_playerRoundExperience);
        }





        /// <summary>
        /// 处理玩家逃脱事件
        /// </summary>
        public void ProcessPlayerEscaping(PlayerEscapingEventArgs ev)
        {
            // 只有在逃脱成功且是合法逃脱角色时才给经验
            if (ev.IsAllowed && IsValidEscapeRole(ev.Player.Role, ev.NewRole))
            {
                // 特殊检查：如果是保安在撤离区域内转换为NTF，可能是下班而不是逃脱
                if (ev.Player.Role == RoleTypeId.FacilityGuard && ev.NewRole == RoleTypeId.NtfSpecialist)
                {
                    // 检查是否在保安下班区域内
                    if (IsInGuardOffDutyArea(ev.Player.Position))
                    {
                        Logger.Debug($"保安 {ev.Player.Nickname} 在下班区域内转换角色，不给逃脱经验");
                        return;
                    }
                }

                GiveExperience(ev.Player, ESCAPE_XP, $"成功逃脱获得{ESCAPE_XP}经验");
            }
        }

        /// <summary>
        /// 检查位置是否在保安下班区域内
        /// </summary>
        private bool IsInGuardOffDutyArea(Vector3 position)
        {
            return position.x <= 150 && position.x >= 115 &&
                   position.y <= 300 && position.y >= 280 &&
                   position.z <= 32 && position.z >= 10;
        }

        /// <summary>
        /// 检查是否为合法的逃脱角色转换
        /// </summary>
        private bool IsValidEscapeRole(RoleTypeId currentRole, RoleTypeId newRole)
        {
            // D级人员逃脱变成混沌
            if (currentRole == RoleTypeId.ClassD && newRole == RoleTypeId.ChaosConscript)
                return true;

            // 科学家逃脱变成NTF
            if (currentRole == RoleTypeId.Scientist && newRole == RoleTypeId.NtfSpecialist)
                return true;

            // 保安逃脱变成NTF
            if (currentRole == RoleTypeId.FacilityGuard && newRole == RoleTypeId.NtfSpecialist)
                return true;

            // 其他情况都不算合法逃脱
            return false;
        }

        /// <summary>
        /// 处理玩家使用物品事件
        /// </summary>
        public void ProcessPlayerUsedItem(PlayerUsedItemEventArgs ev)
        {
            // 检查是否为SCP物品
            if (IsScpItem(ev.UsableItem.Base.ItemTypeId))
            {
                GiveExperience(ev.Player, SCP_ITEM_USE_XP, $"使用SCP物品获得{SCP_ITEM_USE_XP}经验");
            }
        }

        /// <summary>
        /// 判断是否为SCP物品
        /// </summary>
        private bool IsScpItem(ItemType itemType)
        {
            return itemType == ItemType.SCP018 ||
                   itemType == ItemType.SCP207 ||
                   itemType == ItemType.SCP244a ||
                   itemType == ItemType.SCP244b ||
                   itemType == ItemType.SCP268 ||
                   itemType == ItemType.SCP330 ||
                   itemType == ItemType.SCP500 ||
                   itemType == ItemType.SCP1576 ||
                   itemType == ItemType.SCP1853 ||
                   itemType == ItemType.SCP2176;
        }

        /// <summary>
        /// 处理玩家激活发电机事件
        /// </summary>
        public void ProcessPlayerActivatedGenerator(PlayerActivatedGeneratorEventArgs ev)
        {
            var playerId = ev.Player.UserId;
            var generatorId = ev.Generator.GetHashCode().ToString();

            // 获取或创建玩家的首次激活记录
            var playerActivations = _playerFirstActivations.GetOrAdd(playerId, _ => []);

            // 检查是否为首次激活此发电机
            if (playerActivations.Add(generatorId))
            {
                GiveExperience(ev.Player, FIRST_GENERATOR_XP, $"首次激活发电机获得{FIRST_GENERATOR_XP}经验");
            }
        }

        /// <summary>
        /// 处理SCP049复活事件
        /// </summary>
        public void ProcessScp049ResurrectedBody(Scp049ResurrectedBodyEventArgs ev)
        {
            GiveExperience(ev.Player, SCP049_RESURRECT_XP, $"复活小僵尸获得{SCP049_RESURRECT_XP}经验");
        }

        /// <summary>
        /// 处理SCP079切换摄像头事件
        /// </summary>
        public void ProcessScp079ChangedCamera(Scp079ChangedCameraEventArgs ev)
        {
            // SCP079经验已简化，不再需要跟踪摄像头位置
        }

        /// <summary>
        /// 处理SCP079获得经验事件
        /// </summary>
        public void ProcessScp079GainedExperience(Scp079GainedExperienceEventArgs ev)
        {
            // 简化逻辑：SCP079收到任何经验都给1经验的回合分数
            GiveExperience(ev.Player, 1, "SCP079获得1经验");
        }

        /// <summary>
        /// 处理SCP079使用电网事件
        /// </summary>
        public void ProcessScp079UsedTesla(Scp079UsedTeslaEventArgs ev)
        {
            // 电网击杀经验会在玩家死亡事件中处理
            // 这里只是标记电网被使用
            // 暂时不需要处理，保留方法以备将来扩展
            _ = ev; // 避免未使用参数警告
        }

        /// <summary>
        /// 清理玩家数据（玩家离开时调用）
        /// </summary>
        public void CleanupPlayerData(string userId)
        {
            _humanDamageAccumulator.TryRemove(userId, out _);
            _scpDamageAccumulator.TryRemove(userId, out _);
            _playerFirstActivations.TryRemove(userId, out _);
            _playerRoundExperience.TryRemove(userId, out _);
        }

        /// <summary>
        /// 清理回合数据（新回合开始时调用）
        /// </summary>
        public void CleanupRoundData()
        {
            _humanDamageAccumulator.Clear();
            _scpDamageAccumulator.Clear();
            _playerFirstActivations.Clear();
            _playerRoundExperience.Clear();
        }
    }
}
