using System;
using LabApi.Features.Console;
using BlackRoseServer.API;
using BlackRoseServer.Manager;
using BlackRoseServer.Display;

namespace BlackRoseServer
{
    /// <summary>
    /// 启动时依赖项初始化类
    /// </summary>
    public static class Startup
    {
        /// <summary>
        /// 设置和验证系统依赖项
        /// </summary>
        public static void SetupDependencies()
        {
            try
            {
                Logger.Info("正在初始化BlackRoseServer依赖项...");
                
                // 验证配置
                ValidateConfiguration();
                
                // 初始化数据库
                if (Config.Instance.EnableDatabaseFeatures)
                {
                    InitializeMySQLDatabase();
                }

                // 初始化HintManager
                InitializeHintManager();

                // 初始化右下角显示管理器
                InitializeRightBottomDisplayManager();
                
                Logger.Info("依赖项初始化完成");
            }
            catch (Exception ex)
            {
                Logger.Error($"依赖项初始化失败: {ex.Message}");
                Logger.Debug($"详细错误信息: {ex}");
            }
        }
        
        /// <summary>
        /// 验证配置有效性
        /// </summary>
        private static void ValidateConfiguration()
        {
            if (Config.Instance == null)
            {
                throw new InvalidOperationException("配置实例未初始化");
            }
            
            if (string.IsNullOrEmpty(Config.Instance.DatabaseConnectionString))
            {
                Logger.Warn("MySQL数据库连接字符串未配置，数据库功能将被禁用");
            }
        }
        
        /// <summary>
        /// 初始化MySQL数据库
        /// </summary>
        private static void InitializeMySQLDatabase()
        {
            try
            {
                // 通过访问MySQLService.Instance来初始化数据库
                var database = MySQLService.Instance;
                Logger.Info("MySQL数据库初始化成功");

                // 验证数据库是否可用
                if (database.IsDatabaseEnabled())
                {
                    Logger.Info("数据库功能已启用并可用");
                }
                else
                {
                    Logger.Warn("数据库功能已禁用");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"MySQL数据库初始化失败: {ex.Message}");
                Logger.Debug($"详细错误信息: {ex}");
                throw;
            }
        }

        /// <summary>
        /// 初始化HintManager
        /// </summary>
        private static void InitializeHintManager()
        {
            try
            {
                // 通过访问HintManager.Instance来初始化管理器
                var hintManager = HintManager.Instance;
                Logger.Info("HintManager初始化成功");

                // 运行基本功能测试（可选）
                if (Config.Instance?.EnableDatabaseFeatures == true)
                {
                    HintManagerTest.RunBasicTests();
                    DisplaySystemTest.RunBasicTests();

                    // 延迟5秒后运行SCP显示调试（等待玩家加入）
                    MEC.Timing.CallDelayed(5f, () =>
                    {
                        SCPDisplayDebugger.DebugSCPDisplay();
                    });
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"HintManager初始化失败: {ex.Message}");
                Logger.Debug($"详细错误信息: {ex}");
                // HintManager初始化失败不应该阻止插件启动
            }
        }

        /// <summary>
        /// 初始化右下角显示管理器
        /// </summary>
        private static void InitializeRightBottomDisplayManager()
        {
            try
            {
                // 通过访问RightBottomDisplayManager.Instance来初始化管理器
                var displayManager = RightBottomDisplayManager.Instance;
                Logger.Info("RightBottomDisplayManager初始化成功");
            }
            catch (Exception ex)
            {
                Logger.Error($"RightBottomDisplayManager初始化失败: {ex.Message}");
                Logger.Debug($"详细错误信息: {ex}");
                // 显示管理器初始化失败不应该阻止插件启动
            }
        }
    }
}
