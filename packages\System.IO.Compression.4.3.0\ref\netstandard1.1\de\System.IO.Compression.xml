﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Compression</name>
  </assembly>
  <members>
    <member name="T:System.IO.Compression.CompressionLevel">
      <summary>Gibt Werte an, die angeben, ob ein Komprimierungsvorgang die Geschwindigkeit oder den Komprimierungsumfang hervorhebt.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.Fastest">
      <summary>Der Komprimierungsvorgang soll so schnell wie möglich beendet werden, auch wenn die resultierende Datei nicht optimal komprimiert wird.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.NoCompression">
      <summary>Bei der Datei sollte keine Komprimierung erfolgen.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.Optimal">
      <summary>Der Komprimierungsvorgang soll optimal ausgeführt werden, auch wenn der Vorgang eine längere Zeit in Anspruch nimmt.</summary>
    </member>
    <member name="T:System.IO.Compression.CompressionMode">
      <summary> Gibt an, ob der zugrunde liegende Stream komprimiert oder dekomprimiert werden soll.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionMode.Compress">
      <summary>Komprimiert den zugrunde liegenden Stream.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionMode.Decompress">
      <summary>Dekomprimiert den zugrunde liegenden Stream.</summary>
    </member>
    <member name="T:System.IO.Compression.DeflateStream">
      <summary>Stellt Methoden und Eigenschaften zum Komprimieren und Dekomprimieren von Streams mithilfe des Deflate-Algorithmus bereit.</summary>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.DeflateStream" />-Klasse mithilfe des angegebenen Stream- und Komprimierungsgrads.</summary>
      <param name="stream">Der zu komprimierende Stream.</param>
      <param name="compressionLevel">Einer der Enumerationswerte, der angibt, ob die Geschwindigkeit oder Komprimierungsleistungsfähigkeit priorisiert wird, wenn der Stream komprimiert wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Der Stream unterstützt keine Schreibvorgänge wie die Komprimierung.(Die <see cref="P:System.IO.Stream.CanWrite" />-Eigenschaft für das Streamobjekt ist false).</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.DeflateStream" />-Klasse unter Verwendung des angegebenen Streams und der Komprimierungsebene und lässt den Stream optional geöffnet.</summary>
      <param name="stream">Der zu komprimierende Stream.</param>
      <param name="compressionLevel">Einer der Enumerationswerte, der angibt, ob die Geschwindigkeit oder Komprimierungsleistungsfähigkeit priorisiert wird, wenn der Stream komprimiert wird.</param>
      <param name="leaveOpen">true, um das Streamobjekt offen zu lassen, nachdem das <see cref="T:System.IO.Compression.DeflateStream" />-Objekt freigegeben wurde, andernfalls false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Der Stream unterstützt keine Schreibvorgänge wie die Komprimierung.(Die <see cref="P:System.IO.Stream.CanWrite" />-Eigenschaft für das Streamobjekt ist false).</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.DeflateStream" />-Klasse mithilfe des angegebenen Stream- und Komprimierungsmodus.</summary>
      <param name="stream">Der zu komprimierende oder zu dekomprimierende Stream.</param>
      <param name="mode">Einer der Enumerationswerte, der angibt, ob der Stream gelesen oder geschrieben wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> ist kein gültiger <see cref="T:System.IO.Compression.CompressionMode" />-Wert.- oder - <see cref="T:System.IO.Compression.CompressionMode" /> ist <see cref="F:System.IO.Compression.CompressionMode.Compress" />  und <see cref="P:System.IO.Stream.CanWrite" /> ist false.- oder - <see cref="T:System.IO.Compression.CompressionMode" /> ist <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  und <see cref="P:System.IO.Stream.CanRead" /> ist false.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.DeflateStream" />-Klasse unter Verwendung des angegebenen Streams und des Komprimierungsmodus und lässt den Stream optional geöffnet.</summary>
      <param name="stream">Der zu komprimierende oder zu dekomprimierende Stream.</param>
      <param name="mode">Einer der Enumerationswerte, der angibt, ob der Stream gelesen oder geschrieben wird.</param>
      <param name="leaveOpen">true, um den Stream offen zu lassen, nachdem das <see cref="T:System.IO.Compression.DeflateStream" />-Objekt freigegeben wurde, andernfalls false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> ist kein gültiger <see cref="T:System.IO.Compression.CompressionMode" />-Wert.- oder - <see cref="T:System.IO.Compression.CompressionMode" /> ist <see cref="F:System.IO.Compression.CompressionMode.Compress" />  und <see cref="P:System.IO.Stream.CanWrite" /> ist false.- oder - <see cref="T:System.IO.Compression.CompressionMode" /> ist <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  und <see cref="P:System.IO.Stream.CanRead" /> ist false.</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.BaseStream">
      <summary>Ruft einen Verweis auf den zugrunde liegenden Stream ab.</summary>
      <returns>Ein Streamobjekt, das den zugrunde liegenden Stream darstellt.</returns>
      <exception cref="T:System.ObjectDisposedException">Der zugrunde liegende Stream ist geschlossen.</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanRead">
      <summary>Ruft einen Wert ab, der angibt, ob der Stream Lesevorgänge unterstützt, während eine Datei dekomprimiert wird.</summary>
      <returns>true, wenn der <see cref="T:System.IO.Compression.CompressionMode" />-Wert Decompress ist und der zugrunde liegende Stream geöffnet ist und Lesevorgänge unterstützt, andernfalls false.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanSeek">
      <summary>Ruft einen Wert, der angibt, ob der Stream Suchvorgänge unterstützt.</summary>
      <returns>false in allen Fällen.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanWrite">
      <summary>Ruft einen Wert, der angibt, ob der Stream Schreibvorgänge unterstützt.</summary>
      <returns>true, wenn der <see cref="T:System.IO.Compression.CompressionMode" />-Wert gleich Compress ist und der zugrunde liegende Stream Schreibvorgänge unterstützt und nicht geschlossen ist, andernfalls false.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.Compression.DeflateStream" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Flush">
      <summary>Die aktuelle Implementierung dieser Methode hat keine Funktion.</summary>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.Length">
      <summary>Diese Eigenschaft wird nicht unterstützt und löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>Ein Long-Wert.</returns>
      <exception cref="T:System.NotSupportedException">Diese Eigenschaft wird für diesen Stream nicht unterstützt.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.Position">
      <summary>Diese Eigenschaft wird nicht unterstützt und löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>Ein Long-Wert.</returns>
      <exception cref="T:System.NotSupportedException">Diese Eigenschaft wird für diesen Stream nicht unterstützt.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest eine Anzahl von dekomprimierten Bytes in das angegebene Bytearray.</summary>
      <returns>Die Anzahl von Bytes, die in das Bytearray gelesen wurden.</returns>
      <param name="array">Das Array zum Speichern von dekomprimierten Bytes.</param>
      <param name="offset">Das Byteoffset in <paramref name="array" />, an dem die gelesenen Bytes platziert werden.</param>
      <param name="count">Die maximale Anzahl der zu lesenden dekomprimierten Bytes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.IO.Compression.CompressionMode" />-Wert war Compress, als das Objekt erstellt wurde.- oder - Der zugrunde liegende Stream unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist kleiner als 0.- oder - Die <paramref name="array" />-Länge minus des Indexausgangspunkts ist kleiner als <paramref name="count" />.</exception>
      <exception cref="T:System.IO.InvalidDataException">Die Daten haben ein ungültiges Format.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Dieser Vorgang wird nicht unterstützt und löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>Ein Long-Wert.</returns>
      <param name="offset">Die Position im Stream.</param>
      <param name="origin">Einer der <see cref="T:System.IO.SeekOrigin" />-Werte.</param>
      <exception cref="T:System.NotSupportedException">Diese Eigenschaft wird für diesen Stream nicht unterstützt.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.SetLength(System.Int64)">
      <summary>Dieser Vorgang wird nicht unterstützt und löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <param name="value">Die Länge des Streams.</param>
      <exception cref="T:System.NotSupportedException">Diese Eigenschaft wird für diesen Stream nicht unterstützt.</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Schreibt komprimierte Bytes aus dem angegebenen Bytearray in den zugrunde liegenden Stream.</summary>
      <param name="array">Der Puffer, der die zu komprimierenden Daten enthält.</param>
      <param name="offset">Das Byteoffset in <paramref name="array" />, aus dem die Bytes gelesen werden.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Bytes.</param>
    </member>
    <member name="T:System.IO.Compression.GZipStream">
      <summary>Stellt Methoden und Eigenschaften bereit, die zum Komprimieren und Dekomprimieren von Streams verwendet werden.</summary>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.GZipStream" />-Klasse mithilfe des angegebenen Stream- und Komprimierungsgrads.</summary>
      <param name="stream">Der Stream, in den die komprimierten Daten geschrieben werden.</param>
      <param name="compressionLevel">Einer der Enumerationswerte, der angibt, ob die Geschwindigkeit oder Komprimierungsleistungsfähigkeit priorisiert wird, wenn der Stream komprimiert wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Der Stream unterstützt keine Schreibvorgänge wie die Komprimierung.(Die <see cref="P:System.IO.Stream.CanWrite" />-Eigenschaft für das Streamobjekt ist false).</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.GZipStream" />-Klasse unter Verwendung des angegebenen Streams und der Komprimierungsebene und lässt den Stream optional geöffnet.</summary>
      <param name="stream">Der Stream, in den die komprimierten Daten geschrieben werden.</param>
      <param name="compressionLevel">Einer der Enumerationswerte, der angibt, ob die Geschwindigkeit oder Komprimierungsleistungsfähigkeit priorisiert wird, wenn der Stream komprimiert wird.</param>
      <param name="leaveOpen">true, um das Streamobjekt offen zu lassen, nachdem das <see cref="T:System.IO.Compression.GZipStream" />-Objekt freigegeben wurde, andernfalls false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">Der Stream unterstützt keine Schreibvorgänge wie die Komprimierung.(Die <see cref="P:System.IO.Stream.CanWrite" />-Eigenschaft für das Streamobjekt ist false).</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.GZipStream" />-Klasse mithilfe des angegebenen Stream- und Komprimierungsmodus.</summary>
      <param name="stream">Der Stream, in den die komprimierten oder dekomprimierten Daten geschrieben werden.</param>
      <param name="mode">Einer der Enumerationswerte, der angibt, ob der Stream gelesen oder geschrieben wird.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> ist kein gültiger <see cref="T:System.IO.Compression.CompressionMode" />-Enumerationswert.- oder - <see cref="T:System.IO.Compression.CompressionMode" /> ist <see cref="F:System.IO.Compression.CompressionMode.Compress" />  und <see cref="P:System.IO.Stream.CanWrite" /> ist false.- oder - <see cref="T:System.IO.Compression.CompressionMode" /> ist <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  und <see cref="P:System.IO.Stream.CanRead" /> ist false.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.GZipStream" />-Klasse unter Verwendung des angegebenen Streams und des Komprimierungsmodus und lässt den Stream optional geöffnet.</summary>
      <param name="stream">Der Stream, in den die komprimierten oder dekomprimierten Daten geschrieben werden.</param>
      <param name="mode">Einer der Enumerationswerte, der angibt, ob der Stream gelesen oder geschrieben wird.</param>
      <param name="leaveOpen">true, um den Stream offen zu lassen, nachdem das <see cref="T:System.IO.Compression.GZipStream" />-Objekt freigegeben wurde, andernfalls false.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> ist null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" /> ist kein gültiger <see cref="T:System.IO.Compression.CompressionMode" />-Wert.- oder - <see cref="T:System.IO.Compression.CompressionMode" /> ist <see cref="F:System.IO.Compression.CompressionMode.Compress" />  und <see cref="P:System.IO.Stream.CanWrite" /> ist false.- oder - <see cref="T:System.IO.Compression.CompressionMode" /> ist <see cref="F:System.IO.Compression.CompressionMode.Decompress" />  und <see cref="P:System.IO.Stream.CanRead" /> ist false.</exception>
    </member>
    <member name="P:System.IO.Compression.GZipStream.BaseStream">
      <summary>Ruft einen Verweis auf den zugrunde liegenden Stream ab.</summary>
      <returns>Ein Streamobjekt, das den zugrunde liegenden Stream darstellt.</returns>
      <exception cref="T:System.ObjectDisposedException">Der zugrunde liegende Stream ist geschlossen.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanRead">
      <summary>Ruft einen Wert ab, der angibt, ob der Stream Lesevorgänge unterstützt, während eine Datei dekomprimiert wird.</summary>
      <returns>true, wenn der <see cref="T:System.IO.Compression.CompressionMode" />-Wert gleich Decompress, ist und der zugrunde liegende Stream Lesevorgänge unterstützt und nicht geschlossen ist, andernfalls false.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanSeek">
      <summary>Ruft einen Wert, der angibt, ob der Stream Suchvorgänge unterstützt.</summary>
      <returns>false in allen Fällen.</returns>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanWrite">
      <summary>Ruft einen Wert, der angibt, ob der Stream Schreibvorgänge unterstützt.</summary>
      <returns>true, wenn der <see cref="T:System.IO.Compression.CompressionMode" />-Wert gleich Compress ist und der zugrunde liegende Stream Schreibvorgänge unterstützt und nicht geschlossen ist, andernfalls false.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Dispose(System.Boolean)">
      <summary>Gibt die von <see cref="T:System.IO.Compression.GZipStream" /> verwendeten nicht verwalteten Ressourcen und optional die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um sowohl verwaltete als auch nicht verwaltete Ressourcen freizugeben, false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Flush">
      <summary>Die aktuelle Implementierung dieser Methode hat keine Funktion.</summary>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.Length">
      <summary>Diese Eigenschaft wird nicht unterstützt und löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>Ein Long-Wert.</returns>
      <exception cref="T:System.NotSupportedException">Diese Eigenschaft wird für diesen Stream nicht unterstützt.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.Position">
      <summary>Diese Eigenschaft wird nicht unterstützt und löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>Ein Long-Wert.</returns>
      <exception cref="T:System.NotSupportedException">Diese Eigenschaft wird für diesen Stream nicht unterstützt.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>Liest eine Anzahl von dekomprimierten Bytes in das angegebene Bytearray.</summary>
      <returns>Die Anzahl der Bytes, die im Bytearray dekomprimiert wurden.Wenn das Ende des Streams erreicht worden ist, wird 0 oder die Anzahl der gelesenen Bytes zurückgegeben.</returns>
      <param name="array">Das Array, das zum Speichern von dekomprimierten Bytes verwendet wird.</param>
      <param name="offset">Das Byteoffset in <paramref name="array" />, an dem die gelesenen Bytes platziert werden.</param>
      <param name="count">Die maximale Anzahl der zu lesenden dekomprimierten Bytes.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" /> ist null.</exception>
      <exception cref="T:System.InvalidOperationException">Der <see cref="T:System.IO.Compression.CompressionMode" />-Wert war Compress, als das Objekt erstellt wurde.- oder -Der zugrunde liegende Stream unterstützt keine Lesevorgänge.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> oder <paramref name="count" /> ist kleiner als 0.- oder - Die <paramref name="array" />-Länge minus des Indexausgangspunkts ist kleiner als <paramref name="count" />.</exception>
      <exception cref="T:System.IO.InvalidDataException">Die Daten haben ein ungültiges Format.</exception>
      <exception cref="T:System.ObjectDisposedException">Der Stream ist geschlossen.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>Diese Eigenschaft wird nicht unterstützt und löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <returns>Ein Long-Wert.</returns>
      <param name="offset">Die Position im Stream.</param>
      <param name="origin">Einer der <see cref="T:System.IO.SeekOrigin" />-Werte.</param>
      <exception cref="T:System.NotSupportedException">Diese Eigenschaft wird für diesen Stream nicht unterstützt.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.SetLength(System.Int64)">
      <summary>Diese Eigenschaft wird nicht unterstützt und löst immer eine <see cref="T:System.NotSupportedException" /> aus.</summary>
      <param name="value">Die Länge des Streams.</param>
      <exception cref="T:System.NotSupportedException">Diese Eigenschaft wird für diesen Stream nicht unterstützt.</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>Schreibt komprimierte Bytes aus dem angegebenen Bytearray in den zugrunde liegenden Stream.</summary>
      <param name="array">Der Puffer, der die zu komprimierenden Daten enthält.</param>
      <param name="offset">Das Byteoffset in <paramref name="array" />, aus dem die Bytes gelesen werden.</param>
      <param name="count">Die maximale Anzahl der zu schreibenden Bytes.</param>
      <exception cref="T:System.ObjectDisposedException">Der Schreibvorgang kann nicht ausgeführt werden, da der Stream geschlossen ist.</exception>
    </member>
    <member name="T:System.IO.Compression.ZipArchive">
      <summary>Stellt ein Paket komprimierter Dateien im Zip-Archiv-Format dar.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.ZipArchive" />-Klasse für den angegebenen Stream.</summary>
      <param name="stream">Ein Stream, der das zu lesende Archiv enthält.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed or does not support reading.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream are not in the zip archive format.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.ZipArchive" />-Klasse aus dem angegebenen Stream und mit dem angegebenen Modus.</summary>
      <param name="stream">Der Eingabe- oder Ausgabestream.</param>
      <param name="mode">Einer der Enumerationswerte, der angibt, ob das Zip-Archiv verwendet wird, um Einträge zu lesen, zu erstellen oder zu aktualisieren.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode,System.Boolean)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.ZipArchive" />-Klasse für den angegebenen Stream und den angegebenen Modus und lässt den Stream optional geöffnet.</summary>
      <param name="stream">Der Eingabe- oder Ausgabestream.</param>
      <param name="mode">Einer der Enumerationswerte, der angibt, ob das Zip-Archiv verwendet wird, um Einträge zu lesen, zu erstellen oder zu aktualisieren.</param>
      <param name="leaveOpen">true, um den Datenstrom geöffnet zu lassen, nach dem das <see cref="T:System.IO.Compression.ZipArchive" />-Objekt freigegeben wurde; andernfalls false.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode,System.Boolean,System.Text.Encoding)">
      <summary>Initialisiert eine neue Instanz der <see cref="T:System.IO.Compression.ZipArchive" />-Klasse für den angegebenen Stream und den angegebenen Modus und verwendet dabei die angegebene Codierung für Eintragsnamen und lässt den Stream optional geöffnet.</summary>
      <param name="stream">Der Eingabe- oder Ausgabestream.</param>
      <param name="mode">Einer der Enumerationswerte, der angibt, ob das Zip-Archiv verwendet wird, um Einträge zu lesen, zu erstellen oder zu aktualisieren.</param>
      <param name="leaveOpen">true, um den Datenstrom geöffnet zu lassen, nach dem das <see cref="T:System.IO.Compression.ZipArchive" />-Objekt freigegeben wurde; andernfalls false.</param>
      <param name="entryNameEncoding">Die Codierung, die beim Lesen oder Schreiben von Eintragsnamen in diesem Archiv verwendet werden soll.Geben Sie einen Wert für diesen Parameter nur an, wenn eine Codierung für die Interoperabilität mit ZIP-Archiv-Tools und -Bibliotheken erforderlich ist, die die UTF-8-Codierung für Eintragsnamen nicht unterstützen.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.CreateEntry(System.String)">
      <summary>Erstellt einen leeren Eintrag, der den angegebenen Pfad und Eintragsnamen im ZIP-Archiv Ebene verwendet.</summary>
      <returns>Ein leerer Eintrag im ZIP-Archiv.</returns>
      <param name="entryName">Ein Pfad relativ zum Stamm des Archivs, der den Namen des Eintrags angibt, der erstellt werden soll.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.CreateEntry(System.String,System.IO.Compression.CompressionLevel)">
      <summary>Erstellt einen leeren Eintrag, der den angegebenen Eintragsnamen und die Komprimierung im ZIP-Archiv Ebene verwendet.</summary>
      <returns>Ein leerer Eintrag im ZIP-Archiv.</returns>
      <param name="entryName">Ein Pfad relativ zum Stamm des Archivs, der den Namen des Eintrags angibt, der erstellt werden soll.</param>
      <param name="compressionLevel">Einer der Enumerationswerte, der angibt, ob Geschwindigkeit oder Komprimierungseffektivität priorisiert wird, wenn der Eintrag erstellt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.Dispose">
      <summary>Gibt alle von der aktuellen Instanz der <see cref="T:System.IO.Compression.ZipArchive" />-Klasse verwendeten Ressourcen frei.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.Dispose(System.Boolean)">
      <summary>Wird von den <see cref="M:System.IO.Compression.ZipArchive.Dispose" /> und <see cref="M:System.Object.Finalize" />-Methoden aufgerufen, um die nicht verwalteten Ressourcen freizugeben, die von der aktuellen Instanz der <see cref="T:System.IO.Compression.ZipArchive" />-Klasse verwendet werden, und beendet optional das Schreiben ins Archiv und gibt die verwalteten Ressourcen frei.</summary>
      <param name="disposing">true, um das Schreiben des Archivs abzuschließen und sowohl nicht verwaltete als auch verwaltete Ressourcen freizugeben; false, um ausschließlich nicht verwaltete Ressourcen freizugeben.</param>
    </member>
    <member name="P:System.IO.Compression.ZipArchive.Entries">
      <summary>Ruft die Auflistung von Einträgen ab, die das ZIP-Archiv momentan enthält.</summary>
      <returns>Die Auflistung von Einträgen, die das ZIP-Archiv momentan enthält.</returns>
      <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
      <exception cref="T:System.IO.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.GetEntry(System.String)">
      <summary>Ruft einen Wrapper für den angegebenen Eintrag im Zip-Archiv ab.</summary>
      <returns>Ein Wrapper für den angegebenen Eintrag im Archiv. null, wenn der Eintrag nicht im Archiv vorhanden ist.</returns>
      <param name="entryName">Ein Pfad relativ zum Stamm des Archivs, das den Eintrag identifiziert, der abgerufen werden soll.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
      <exception cref="T:System.IO.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchive.Mode">
      <summary>Ruft einen Wert ab, der den Typ der Aktion beschreibt, die das ZIP-Archiv für Einträge ausführen kann.</summary>
      <returns>Einer der Enumerationswerte, der den Typ der Aktion (Lesen, Erstellen oder Update) beschreibt, die das Zip-Archiv bei Einträgen ausführen kann.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchiveEntry">
      <summary>Stellt eine komprimierte Datei in einem Zip-Archiv dar.</summary>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Archive">
      <summary>Ruft das Zip-Archiv ab, zu dem der Eintrag gehört.</summary>
      <returns>Das Zip-Archiv, zu dem der Eintrag gehört, oder null, wenn der Eintrag gelöscht wurde.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.CompressedLength">
      <summary>Ruft die komprimierte Größe des unkomprimierten Eintrags im ZIP-Archiv ab.</summary>
      <returns>Die Größe des komprimierten Eintrags im ZIP-Archiv.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the property is not available because the entry has been modified.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.Delete">
      <summary>Löscht den Eintrag aus dem Zip-Archiv.</summary>
      <exception cref="T:System.IO.IOException">The entry is already open for reading or writing.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive for this entry was opened in a mode other than <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />. </exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.FullName">
      <summary>Ruft den relativen Pfad des Eintrags im ZIP-Archiv ab.</summary>
      <returns>Der relative Pfad des Eintrags im Zip-Archiv.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.LastWriteTime">
      <summary>Ruft den letzten Zeitpunkt ab, zu dem der Eintrag im Zip-Archiv geändert wurde, oder legt diesen fest.</summary>
      <returns>Der letzte Zeitpunkt, an dem der Eintrag im ZIP-Archiv geändert wurde.</returns>
      <exception cref="T:System.NotSupportedException">The attempt to set this property failed, because the zip archive for the entry is in <see cref="F:System.IO.Compression.ZipArchiveMode.Read" /> mode.</exception>
      <exception cref="T:System.IO.IOException">The archive mode is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />.- or -The archive mode is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and the entry has been opened.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An attempt was made to set this property to a value that is either earlier than 1980 January 1 0:00:00 (midnight) or later than 2107 December 31 23:59:58 (one second before midnight).</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Length">
      <summary>Ruft die Größe des unkomprimierten Eintrags im ZIP-Archiv ab.</summary>
      <returns>Die Größe des unkomprimierten Eintrags im ZIP-Archiv.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the property is not available because the entry has been modified.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Name">
      <summary>Ruft den Dateinamen des Eintrags im ZIP-Archiv ab.</summary>
      <returns>Der Dateiname des Eintrags im ZIP-Archiv.</returns>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.Open">
      <summary>Öffnet den Eintrag aus dem Zip-Archiv.</summary>
      <returns>Der Stream, der den Inhalt des Eintrags darstellt.</returns>
      <exception cref="T:System.IO.IOException">The entry is already currently open for writing.-or-The entry has been deleted from the archive.-or-The archive for this entry was opened with the <see cref="F:System.IO.Compression.ZipArchiveMode.Create" /> mode, and this entry has already been written to. </exception>
      <exception cref="T:System.IO.InvalidDataException">The entry is either missing from the archive or is corrupt and cannot be read. -or-The entry has been compressed by using a compression method that is not supported.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.ToString">
      <summary>Ruft den relativen Pfad des Eintrags im Zip-Archiv ab.</summary>
      <returns>Der relative Pfad des Eintrags, der in der <see cref="P:System.IO.Compression.ZipArchiveEntry.FullName" />-Eigenschaft gespeichert ist.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchiveMode">
      <summary>Gibt Werte für die Interaktion mit Zip-Archiveinträgen an.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Create">
      <summary>Nur das Erstellen neuer Archiveinträge ist zulässig.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Read">
      <summary>Nur das Lesen von Archiveinträgen ist zulässig.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Update">
      <summary>Lese- und Schreibvorgänge sind für die Archiveinträge zulässig.</summary>
    </member>
  </members>
</doc>