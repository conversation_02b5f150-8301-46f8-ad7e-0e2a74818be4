﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.IO.Compression</name>
  </assembly>
  <members>
    <member name="T:System.IO.Compression.CompressionLevel">
      <summary>압축 시 속도를 우선할지 압축 크기를 우선할지를 나타내는 값을 지정합니다.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.Fastest">
      <summary>압축 작업은 결과 파일이 최적으로 압축되지 않더라도 가능한 신속하게 완료되어야 합니다.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.NoCompression">
      <summary>파일에 대해 압축을 수행할 수 없습니다.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionLevel.Optimal">
      <summary>작업을 완료하는 데 시간이 더 걸리더라도 압축 작업은 최적으로 압축되어야 합니다.</summary>
    </member>
    <member name="T:System.IO.Compression.CompressionMode">
      <summary> 내부 스트림을 압축할지 또는 압축을 풀지 여부를 지정합니다.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionMode.Compress">
      <summary>내부 스트림을 압축합니다.</summary>
    </member>
    <member name="F:System.IO.Compression.CompressionMode.Decompress">
      <summary>내부 스트림의 압축을 풉니다.</summary>
    </member>
    <member name="T:System.IO.Compression.DeflateStream">
      <summary>Deflate 알고리즘을 사용하여 스트림을 압축하고 압축을 풀기 위한 메서드와 속성을 제공합니다.</summary>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel)">
      <summary>지정된 스트림과 압축 수준을 사용하여 <see cref="T:System.IO.Compression.DeflateStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="stream">압축할 스트림입니다.</param>
      <param name="compressionLevel">스트림을 압축할 때 속도 또는 압축 효율을 강조할지 여부를 나타내는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">스트림이 압축 등의 쓰기 작업을 지원하지 않습니다.(스트림 개체의 <see cref="P:System.IO.Stream.CanWrite" /> 속성이 false입니다.)</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>지정된 스트림과 압축 수준을 사용하여 <see cref="T:System.IO.Compression.DeflateStream" /> 클래스의 새 인스턴스를 초기화하고 스트림을 선택적으로 연 상태로 둘 수 있습니다.</summary>
      <param name="stream">압축할 스트림입니다.</param>
      <param name="compressionLevel">스트림을 압축할 때 속도 또는 압축 효율을 강조할지 여부를 나타내는 열거형 값 중 하나입니다.</param>
      <param name="leaveOpen">
        <see cref="T:System.IO.Compression.DeflateStream" /> 개체를 삭제한 후 스트림 개체를 열어 두려면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">스트림이 압축 등의 쓰기 작업을 지원하지 않습니다.(스트림 개체의 <see cref="P:System.IO.Stream.CanWrite" /> 속성이 false입니다.)</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode)">
      <summary>지정된 스트림과 압축 모드를 사용하여 <see cref="T:System.IO.Compression.DeflateStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="stream">압축하거나 압축을 풀 스트림입니다.</param>
      <param name="mode">스트림을 압축할 것인지 또는 압축 해제할 것인지를 나타내는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" />은(는) 올바른 <see cref="T:System.IO.Compression.CompressionMode" /> 값이 아닙니다.또는<see cref="T:System.IO.Compression.CompressionMode" />가 <see cref="F:System.IO.Compression.CompressionMode.Compress" />이고 <see cref="P:System.IO.Stream.CanWrite" />가 false인 경우또는<see cref="T:System.IO.Compression.CompressionMode" />가 <see cref="F:System.IO.Compression.CompressionMode.Decompress" />이고 <see cref="P:System.IO.Stream.CanRead" />가 false인 경우</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode,System.Boolean)">
      <summary>지정된 스트림과 압축 모드를 사용하여 <see cref="T:System.IO.Compression.DeflateStream" /> 클래스의 새 인스턴스를 초기화하고 스트림을 선택적으로 연 상태로 둘 수 있습니다.</summary>
      <param name="stream">압축하거나 압축을 풀 스트림입니다.</param>
      <param name="mode">스트림을 압축할 것인지 또는 압축 해제할 것인지를 나타내는 열거형 값 중 하나입니다.</param>
      <param name="leaveOpen">
        <see cref="T:System.IO.Compression.DeflateStream" /> 개체를 삭제한 후 스트림을 열어 두려면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" />은(는) 올바른 <see cref="T:System.IO.Compression.CompressionMode" /> 값이 아닙니다.또는<see cref="T:System.IO.Compression.CompressionMode" />가 <see cref="F:System.IO.Compression.CompressionMode.Compress" />이고 <see cref="P:System.IO.Stream.CanWrite" />가 false인 경우또는<see cref="T:System.IO.Compression.CompressionMode" />가 <see cref="F:System.IO.Compression.CompressionMode.Decompress" />이고 <see cref="P:System.IO.Stream.CanRead" />가 false인 경우</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.BaseStream">
      <summary>내부 스트림에 대한 참조를 가져옵니다.</summary>
      <returns>내부 스트림을 나타내는 스트림 개체입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">내부 스트림이 닫혀 있는 경우</exception>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanRead">
      <summary>파일의 압축을 푸는 동안 스트림을 읽을 수 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.IO.Compression.CompressionMode" /> 값이 Decompress이고 내부 스트림이 열려 있으며 읽기를 지원하면 true이고, 이외의 경우는 false입니다.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanSeek">
      <summary>스트림이 검색을 지원하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>모든 경우에 false입니다.</returns>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.CanWrite">
      <summary>스트림이 쓰기를 지원하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.IO.Compression.CompressionMode" /> 값이 Compress이고 내부 스트림이 쓰기를 지원하며 닫혀 있지 않으면 true이고, 이외의 경우는 false입니다.</returns>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.IO.Compression.DeflateStream" />가 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 해제합니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Flush">
      <summary>이 메서드의 현재 클래스에는 기능이 없습니다.</summary>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.Length">
      <summary>이 속성은 지원되지 않으며 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>long 값입니다.</returns>
      <exception cref="T:System.NotSupportedException">이 스트림에 이 속성이 지원되지 않는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.DeflateStream.Position">
      <summary>이 속성은 지원되지 않으며 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>long 값입니다.</returns>
      <exception cref="T:System.NotSupportedException">이 스트림에 이 속성이 지원되지 않는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>압축이 풀린 바이트 수를 지정된 바이트 배열로 읽어 들입니다.</summary>
      <returns>바이트 배열에 읽어 들인 바이트 수입니다.</returns>
      <param name="array">압축이 풀린 바이트를 저장할 배열입니다.</param>
      <param name="offset">읽은 바이트를 넣을 <paramref name="array" />의 바이트 오프셋입니다.</param>
      <param name="count">읽을 최대 압축 풀린 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">개체를 만들 때 <see cref="T:System.IO.Compression.CompressionMode" /> 값이 Compress였던 경우또는 내부 스트림이 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="count" />가 0보다 작은 경우또는<paramref name="array" /> 길이에서 인덱스 시작 위치를 뺀 값이 <paramref name="count" />보다 작은 경우</exception>
      <exception cref="T:System.IO.InvalidDataException">데이터의 형식이 잘못된 경우</exception>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>이 작업은 지원되지 않으며 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>long 값입니다.</returns>
      <param name="offset">스트림 내의 위치입니다.</param>
      <param name="origin">
        <see cref="T:System.IO.SeekOrigin" /> 값 중 하나입니다.</param>
      <exception cref="T:System.NotSupportedException">이 스트림에 이 속성이 지원되지 않는 경우</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.SetLength(System.Int64)">
      <summary>이 작업은 지원되지 않으며 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <param name="value">스트림의 길이입니다.</param>
      <exception cref="T:System.NotSupportedException">이 스트림에 이 속성이 지원되지 않는 경우</exception>
    </member>
    <member name="M:System.IO.Compression.DeflateStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>지정된 바이트 배열에서 압축된 바이트를 내부 스트림에 씁니다.</summary>
      <param name="array">압축할 데이터가 들어 있는 버퍼입니다.</param>
      <param name="offset">바이트를 읽어올 <paramref name="array" />의 바이트 오프셋입니다.</param>
      <param name="count">쓸 최대 바이트 수입니다.</param>
    </member>
    <member name="T:System.IO.Compression.GZipStream">
      <summary>스트림을 압축하거나 압축을 푸는 데 사용되는 메서드 및 속성을 제공합니다.</summary>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel)">
      <summary>지정된 스트림과 압축 수준을 사용하여 <see cref="T:System.IO.Compression.GZipStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="stream">압축된 데이터를 쓸 스트림입니다.</param>
      <param name="compressionLevel">스트림을 압축할 때 속도 또는 압축 효율을 강조할지 여부를 나타내는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">스트림이 압축 등의 쓰기 작업을 지원하지 않습니다.(스트림 개체의 <see cref="P:System.IO.Stream.CanWrite" /> 속성이 false입니다.)</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionLevel,System.Boolean)">
      <summary>지정된 스트림과 압축 수준을 사용하여 <see cref="T:System.IO.Compression.GZipStream" /> 클래스의 새 인스턴스를 초기화하고 스트림을 선택적으로 연 상태로 둘 수 있습니다.</summary>
      <param name="stream">압축된 데이터를 쓸 스트림입니다.</param>
      <param name="compressionLevel">스트림을 압축할 때 속도 또는 압축 효율을 강조할지 여부를 나타내는 열거형 값 중 하나입니다.</param>
      <param name="leaveOpen">
        <see cref="T:System.IO.Compression.GZipStream" /> 개체를 삭제한 후 스트림 개체를 열어 두려면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">스트림이 압축 등의 쓰기 작업을 지원하지 않습니다.(스트림 개체의 <see cref="P:System.IO.Stream.CanWrite" /> 속성이 false입니다.)</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode)">
      <summary>지정된 스트림과 압축 모드를 사용하여 <see cref="T:System.IO.Compression.GZipStream" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="stream">압축되거나 압축을 푼 데이터가를 쓸 스트림입니다.</param>
      <param name="mode">스트림을 압축할 것인지 또는 압축 해제할 것인지를 나타내는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" />가 유효한 <see cref="T:System.IO.Compression.CompressionMode" /> 열거형 값이 아닌 경우또는<see cref="T:System.IO.Compression.CompressionMode" />가 <see cref="F:System.IO.Compression.CompressionMode.Compress" />이고 <see cref="P:System.IO.Stream.CanWrite" />가 false인 경우또는<see cref="T:System.IO.Compression.CompressionMode" />가 <see cref="F:System.IO.Compression.CompressionMode.Decompress" />이고 <see cref="P:System.IO.Stream.CanRead" />가 false인 경우</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.#ctor(System.IO.Stream,System.IO.Compression.CompressionMode,System.Boolean)">
      <summary>지정된 스트림과 압축 모드를 사용하여 <see cref="T:System.IO.Compression.GZipStream" /> 클래스의 새 인스턴스를 초기화하고 스트림을 선택적으로 연 상태로 둘 수 있습니다.</summary>
      <param name="stream">압축되거나 압축을 푼 데이터가를 쓸 스트림입니다.</param>
      <param name="mode">스트림을 압축할 것인지 또는 압축 해제할 것인지를 나타내는 열거형 값 중 하나입니다.</param>
      <param name="leaveOpen">
        <see cref="T:System.IO.Compression.GZipStream" /> 개체를 삭제한 후 스트림을 열어 두려면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" />가 null인 경우</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="mode" />은(는) 올바른 <see cref="T:System.IO.Compression.CompressionMode" /> 값이 아닙니다.또는<see cref="T:System.IO.Compression.CompressionMode" />가 <see cref="F:System.IO.Compression.CompressionMode.Compress" />이고 <see cref="P:System.IO.Stream.CanWrite" />가 false인 경우또는<see cref="T:System.IO.Compression.CompressionMode" />가 <see cref="F:System.IO.Compression.CompressionMode.Decompress" />이고 <see cref="P:System.IO.Stream.CanRead" />가 false인 경우</exception>
    </member>
    <member name="P:System.IO.Compression.GZipStream.BaseStream">
      <summary>내부 스트림에 대한 참조를 가져옵니다.</summary>
      <returns>내부 스트림을 나타내는 스트림 개체입니다.</returns>
      <exception cref="T:System.ObjectDisposedException">내부 스트림이 닫혀 있는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanRead">
      <summary>파일의 압축을 푸는 동안 스트림을 읽을 수 있는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.IO.Compression.CompressionMode" /> 값이 Decompress,이고 내부 스트림이 읽기를 지원하며 닫혀 있지 않으면 true이고, 그렇지 않으면 false입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanSeek">
      <summary>스트림이 검색을 지원하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>모든 경우에 false입니다.</returns>
    </member>
    <member name="P:System.IO.Compression.GZipStream.CanWrite">
      <summary>스트림이 쓰기를 지원하는지 여부를 나타내는 값을 가져옵니다.</summary>
      <returns>
        <see cref="T:System.IO.Compression.CompressionMode" /> 값이 Compress이고 내부 스트림이 쓰기를 지원하며 닫혀 있지 않으면 true이고, 이외의 경우는 false입니다.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.IO.Compression.GZipStream" />에서 사용하는 관리되지 않는 리소스를 해제하고, 관리되는 리소스를 선택적으로 해제할 수 있습니다.</summary>
      <param name="disposing">관리되는 리소스와 관리되지 않는 리소스를 모두 해제하려면 true로 설정하고, 관리되지 않는 리소스만 해제하려면 false로 설정합니다.</param>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Flush">
      <summary>이 메서드의 현재 클래스에는 기능이 없습니다.</summary>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.Length">
      <summary>이 속성은 지원되지 않으며 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>long 값입니다.</returns>
      <exception cref="T:System.NotSupportedException">이 스트림에 이 속성이 지원되지 않는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="P:System.IO.Compression.GZipStream.Position">
      <summary>이 속성은 지원되지 않으며 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>long 값입니다.</returns>
      <exception cref="T:System.NotSupportedException">이 스트림에 이 속성이 지원되지 않는 경우</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode, ControlEvidence" />
      </PermissionSet>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Read(System.Byte[],System.Int32,System.Int32)">
      <summary>압축이 풀린 바이트 수를 지정된 바이트 배열로 읽어 들입니다.</summary>
      <returns>바이트 배열에 압축이 풀린 바이트 수입니다.스트림의 끝에 도달한 경우에는 0 또는 읽은 바이트 수가 반환됩니다.</returns>
      <param name="array">압축이 풀린 바이트를 저장하는 데 사용되는 배열입니다.</param>
      <param name="offset">읽은 바이트를 넣을 <paramref name="array" />의 바이트 오프셋입니다.</param>
      <param name="count">읽을 최대 압축 풀린 바이트 수입니다.</param>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="array" />가 null인 경우</exception>
      <exception cref="T:System.InvalidOperationException">개체를 만들 때 <see cref="T:System.IO.Compression.CompressionMode" /> 값이 Compress였던 경우또는내부 스트림이 읽기를 지원하지 않는 경우</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="offset" /> 또는 <paramref name="count" />가 0보다 작은 경우또는<paramref name="array" /> 길이에서 인덱스 시작 위치를 뺀 값이 <paramref name="count" />보다 작은 경우</exception>
      <exception cref="T:System.IO.InvalidDataException">데이터의 형식이 잘못된 경우</exception>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있는 경우</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Seek(System.Int64,System.IO.SeekOrigin)">
      <summary>이 속성은 지원되지 않으며 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <returns>long 값입니다.</returns>
      <param name="offset">스트림 내의 위치입니다.</param>
      <param name="origin">
        <see cref="T:System.IO.SeekOrigin" /> 값 중 하나입니다.</param>
      <exception cref="T:System.NotSupportedException">이 스트림에 이 속성이 지원되지 않는 경우</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.SetLength(System.Int64)">
      <summary>이 속성은 지원되지 않으며 항상 <see cref="T:System.NotSupportedException" />을 throw합니다.</summary>
      <param name="value">스트림의 길이입니다.</param>
      <exception cref="T:System.NotSupportedException">이 스트림에 이 속성이 지원되지 않는 경우</exception>
    </member>
    <member name="M:System.IO.Compression.GZipStream.Write(System.Byte[],System.Int32,System.Int32)">
      <summary>지정된 바이트 배열에서 압축된 바이트를 내부 스트림에 씁니다.</summary>
      <param name="array">압축할 데이터가 들어 있는 버퍼입니다.</param>
      <param name="offset">바이트를 읽어올 <paramref name="array" />의 바이트 오프셋입니다.</param>
      <param name="count">쓸 최대 바이트 수입니다.</param>
      <exception cref="T:System.ObjectDisposedException">스트림이 닫혀 있어서 쓰기 작업을 수행할 수 없는 경우</exception>
    </member>
    <member name="T:System.IO.Compression.ZipArchive">
      <summary>Zip 보관 파일 형식으로 압축된 파일 패키지를 나타냅니다.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream)">
      <summary>지정된 스트림에서 <see cref="T:System.IO.Compression.ZipArchive" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="stream">읽을 보관 저장소가 포함된 스트림입니다.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed or does not support reading.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream are not in the zip archive format.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode)">
      <summary>지정된 모드를 사용하여 지정된 스트림에서 <see cref="T:System.IO.Compression.ZipArchive" /> 클래스의 새 인스턴스를 초기화합니다.</summary>
      <param name="stream">입력 또는 출력 스트림입니다.</param>
      <param name="mode">Zip 보관 파일이 엔트리를 읽거나 만들기 또는 업데이트하는 데 사용되는지를 나타내는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode,System.Boolean)">
      <summary>지정된 모드에 대해 지정된 스트림에서 <see cref="T:System.IO.Compression.ZipArchive" /> 클래스의 새 인스턴스를 초기화하고 스트림을 선택적으로 연 상태로 둡니다.</summary>
      <param name="stream">입력 또는 출력 스트림입니다.</param>
      <param name="mode">Zip 보관 파일이 엔트리를 읽거나 만들기 또는 업데이트하는 데 사용되는지를 나타내는 열거형 값 중 하나입니다.</param>
      <param name="leaveOpen">
        <see cref="T:System.IO.Compression.ZipArchive" /> 개체를 삭제한 후 스트림을 열어 두려면 true이고, 그렇지 않으면 false입니다.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.#ctor(System.IO.Stream,System.IO.Compression.ZipArchiveMode,System.Boolean,System.Text.Encoding)">
      <summary>지정된 모드에 대해 지정된 스트림에서 <see cref="T:System.IO.Compression.ZipArchive" /> 클래스의 새 인스턴스를 초기화하고 항목 이름에 대해 지정된 인코딩을 사용하며 스트림을 선택적으로 연 상태로 둡니다.</summary>
      <param name="stream">입력 또는 출력 스트림입니다.</param>
      <param name="mode">Zip 보관 파일이 엔트리를 읽거나 만들기 또는 업데이트하는 데 사용되는지를 나타내는 열거형 값 중 하나입니다.</param>
      <param name="leaveOpen">
        <see cref="T:System.IO.Compression.ZipArchive" /> 개체를 삭제한 후 스트림을 열어 두려면 true이고, 그렇지 않으면 false입니다.</param>
      <param name="entryNameEncoding">이 보관 파일에서 이름을 읽거나 쓰는 동안 사용할 인코딩입니다.인코딩이 항목 이름에 대해 UTF-8 인코딩을 지원하지 않는 Zip 보관 도구와 라이브러리를 사용하여 상호 운용성에 인코딩이 필요할 때만 이 매개 변수에 대한 값을 지정합니다.</param>
      <exception cref="T:System.ArgumentException">The stream is already closed, or the capabilities of the stream do not match the mode.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="stream" /> is null.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">
        <paramref name="mode" /> is an invalid value.</exception>
      <exception cref="T:System.IO.InvalidDataException">The contents of the stream could not be interpreted as a zip archive.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is missing from the archive or is corrupt and cannot be read.-or-<paramref name="mode" /> is <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and an entry is too large to fit into memory.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.CreateEntry(System.String)">
      <summary>Zip 보관 파일에 지정된 경로 및 항목 이름을 가진 빈 항목을 만듭니다.</summary>
      <returns>Zip 보관 파일의 빈 항목입니다.</returns>
      <param name="entryName">만들 항목의 이름을 지정하는, 보관 저장소의 루트에 상대적인 경로입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.CreateEntry(System.String,System.IO.Compression.CompressionLevel)">
      <summary>Zip 보관 파일에 지정된 항목 이름 및 압축 수준을 가진 빈 항목을 만듭니다.</summary>
      <returns>Zip 보관 파일의 빈 항목입니다.</returns>
      <param name="entryName">만들 항목의 이름을 지정하는, 보관 저장소의 루트에 상대적인 경로입니다.</param>
      <param name="compressionLevel">항목을 만들 때 속도 또는 압축 효율을 강조할지를 나타내는 열거형 값 중 하나입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support writing.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.Dispose">
      <summary>
        <see cref="T:System.IO.Compression.ZipArchive" /> 클래스의 현재 인스턴스에서 사용하는 리소스를 해제합니다.</summary>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.Dispose(System.Boolean)">
      <summary>
        <see cref="T:System.IO.Compression.ZipArchive" /> 클래스의 현재 인스턴스가 사용하는 관리되지 않는 리소스를 해제하기 위해 <see cref="M:System.IO.Compression.ZipArchive.Dispose" /> 및 <see cref="M:System.Object.Finalize" /> 메서드에 의해 호출되며 선택적으로 보관 파일을 작성하고 관리되는 리소스를 해제합니다.</summary>
      <param name="disposing">보관 파일에 쓰기를 완료하고 관리되지 않은 리소스와 관리되는 리소스를 해제하려면 true이고, 관리되지 않은 리소스만 해제하려면 false입니다.</param>
    </member>
    <member name="P:System.IO.Compression.ZipArchive.Entries">
      <summary>현재 Zip 보관 파일에 있는 항목의 컬렉션을 가져옵니다.</summary>
      <returns>현재 Zip 보관 파일에 있는 항목의 컬렉션입니다.</returns>
      <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
      <exception cref="T:System.IO.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchive.GetEntry(System.String)">
      <summary>Zip 보관 파일 항목에 대한 래퍼를 검색합니다.</summary>
      <returns>보관 저장소의 지정된 항목에 대한 래퍼이거나, 항목이 보관 저장소에 없는 경우 null입니다.</returns>
      <param name="entryName">검색할 항목을 식별하는, 보관 저장소의 루트에 상대적인 경로입니다.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="entryName" /> is <see cref="F:System.String.Empty" />.</exception>
      <exception cref="T:System.ArgumentNullException">
        <paramref name="entryName" /> is null.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive does not support reading.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive has been disposed.</exception>
      <exception cref="T:System.IO.InvalidDataException">The zip archive is corrupt, and its entries cannot be retrieved.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchive.Mode">
      <summary>Zip 보관 파일이 엔트리에 대해 수행할 수 있는 동작의 유형을 나타내는 값을 가져옵니다.</summary>
      <returns>엔트리에 Zip 보관 파일을 보관할 수 있는 동작(읽기, 만들기 또는 업데이트) 유형을 설명하는 열거형 값 중 하나입니다.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchiveEntry">
      <summary>Zip 보관 파일 내의 압축된 파일을 나타냅니다.</summary>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Archive">
      <summary>엔트리가 속한 Zip 보관 파일을 가져옵니다.</summary>
      <returns>항목이 속하는 Zip 보관 파일이거나, 항목이 삭제된 경우 null입니다.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.CompressedLength">
      <summary>Zip 보관 파일에 있는 항목의 압축된 크기를 가져옵니다.</summary>
      <returns>Zip 보관 파일에 있는 항목의 압축된 크기입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the property is not available because the entry has been modified.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.Delete">
      <summary>Zip 보관 파일에서 항목을 삭제합니다.</summary>
      <exception cref="T:System.IO.IOException">The entry is already open for reading or writing.</exception>
      <exception cref="T:System.NotSupportedException">The zip archive for this entry was opened in a mode other than <see cref="F:System.IO.Compression.ZipArchiveMode.Update" />. </exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.FullName">
      <summary>Zip 보관 파일에 있는 항목의 상대 경로를 가져옵니다.</summary>
      <returns>Zip 보관 파일에 있는 항목의 상대 경로입니다.</returns>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.LastWriteTime">
      <summary>Zip 보관 파일의 항목이 마지막으로 변경된 시간을 가져오거나 설정합니다.</summary>
      <returns>Zip 보관 파일의 항목이 마지막으로 변경된 시간입니다.</returns>
      <exception cref="T:System.NotSupportedException">The attempt to set this property failed, because the zip archive for the entry is in <see cref="F:System.IO.Compression.ZipArchiveMode.Read" /> mode.</exception>
      <exception cref="T:System.IO.IOException">The archive mode is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Create" />.- or -The archive mode is set to <see cref="F:System.IO.Compression.ZipArchiveMode.Update" /> and the entry has been opened.</exception>
      <exception cref="T:System.ArgumentOutOfRangeException">An attempt was made to set this property to a value that is either earlier than 1980 January 1 0:00:00 (midnight) or later than 2107 December 31 23:59:58 (one second before midnight).</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Length">
      <summary>Zip 보관 파일에 있는 항목의 압축되지 않은 크기를 가져옵니다.</summary>
      <returns>Zip 보관 파일에 있는 항목의 압축 해제된 크기입니다.</returns>
      <exception cref="T:System.InvalidOperationException">The value of the property is not available because the entry has been modified.</exception>
    </member>
    <member name="P:System.IO.Compression.ZipArchiveEntry.Name">
      <summary>Zip 보관 파일에 있는 항목의 파일 이름을 가져옵니다.</summary>
      <returns>Zip 보관 파일에 있는 항목의 파일 이름입니다.</returns>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.Open">
      <summary>Zip 보관 파일에서 항목을 엽니다.</summary>
      <returns>항목의 내용을 나타내는 스트림입니다.</returns>
      <exception cref="T:System.IO.IOException">The entry is already currently open for writing.-or-The entry has been deleted from the archive.-or-The archive for this entry was opened with the <see cref="F:System.IO.Compression.ZipArchiveMode.Create" /> mode, and this entry has already been written to. </exception>
      <exception cref="T:System.IO.InvalidDataException">The entry is either missing from the archive or is corrupt and cannot be read. -or-The entry has been compressed by using a compression method that is not supported.</exception>
      <exception cref="T:System.ObjectDisposedException">The zip archive for this entry has been disposed.</exception>
    </member>
    <member name="M:System.IO.Compression.ZipArchiveEntry.ToString">
      <summary>Zip 보관 파일에 있는 항목의 상대 경로를 검색합니다.</summary>
      <returns>항목의 상대 경로로, <see cref="P:System.IO.Compression.ZipArchiveEntry.FullName" /> 속성에 저장된 값입니다.</returns>
    </member>
    <member name="T:System.IO.Compression.ZipArchiveMode">
      <summary>Zip 보관 파일 항목과 상호 작용하기 위한 값을 지정합니다.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Create">
      <summary>새 보관 항목 만들기만 허용됩니다.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Read">
      <summary>보관 항목 읽기만 허용됩니다.</summary>
    </member>
    <member name="F:System.IO.Compression.ZipArchiveMode.Update">
      <summary>보관 항목에 대해 읽기 및 쓰기 작업 모두가 허용됩니다.</summary>
    </member>
  </members>
</doc>