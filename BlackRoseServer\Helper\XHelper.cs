﻿using BlackRoseServer.API.Features.Pool;
using CustomPlayerEffects;
using HintServiceMeow.UI.Extension;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Extension;
using Interactables.Interobjects.DoorUtils;
using InventorySystem.Items;
using InventorySystem;
using InventorySystem.Items.Firearms.Modules;
using InventorySystem.Items.Pickups;
using MapGeneration;
using MEC;
using Mirror;
using PlayerRoles;
using PlayerRoles.FirstPersonControl;
using PlayerRoles.FirstPersonControl.Spawnpoints;
using PlayerRoles.Ragdolls;
using RelativePositioning;
using Respawning;
using Scp914;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using InventorySystem.Items.Firearms;
using InventorySystem.Items.Firearms.Attachments;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.API;
using Logger = LabApi.Features.Console.Logger;

namespace BlackRoseServer.Helper
{
    public static class XHelper
    {
        public static System.Random Random = new(DateTime.Now.GetHashCode());
        public static HashSet<Player> PlayerList = new();
        public static HashSet<Player> SpecialPlayerList = new();



        public static Player GetRandomPlayer(RoleTypeId roleTypeId)
        {
            List<Player> players = new List<Player>();

            foreach (Player player in PlayerList)
            {
                if (player.Role == roleTypeId)
                {
                    players.Add(player);
                }
            }

            if (players.Any())
            {
                return players[Random.Next(0, players.Count() - 1)];
            }

            return null;
        }
        public static Player GetRandomPlayer(RoleTypeId roleTypeId, List<Player> playerList)
        {
            List<Player> players = [];
            foreach (Player player in playerList)
            {
                if (player.Role == roleTypeId)
                {
                    players.Add(player);
                }
            }
            if (players.Any())
            {
                return players[Random.Next(0, players.Count - 1)];
            }

            return null;
        }
        public static Player GetRandomPlayer(List<Player> playerList)
        {
            if (playerList.Any())
            {
                return playerList[Random.Next(0, playerList.Count() - 1)];
            }

            return null;
        }
        public static Player GetRandomSpecialPlayer(RoleTypeId roleTypeId)
        {
            List<Player> players = [];
            foreach (Player player in SpecialPlayerList)
            {
                if (player.Role == roleTypeId)
                {
                    players.Add(player);
                }
            }
            if (players.Any())
            {
                var randomPlayer = players[Random.Next(0, players.Count() - 1)];
                SpecialPlayerList.Remove(randomPlayer);
                return randomPlayer;
            }

            return null;
        }
        public static ItemType GetRandomItem()
        {
            var allItems = Enum.GetValues(typeof(ItemType)).ToArray<ItemType>();

            return allItems[Random.Next(0, allItems.Length - 1)];
        }

        public static void SpawnItem(ItemType typeid, Vector3 position, int amount)
        {
            for (int i = 0; i < amount; i++)
            {
                Pickup.Create(typeid, position, new Quaternion(0, 0, 0, 0)).Spawn();
            }
        }
        public static Pickup SpawnItem(ItemType typeid, Vector3 position)
        {
            var item = Pickup.Create(typeid, position, new Quaternion(0, 0, 0, 0));
            item.Spawn();
            return item;
        }

        public static void SetPlayerScale(this Player target, Vector3 scale) => target.SetPlayerScale(scale);
        public static void SetPlayerScale(this Player target, float scale) => SetPlayerScale(target, Vector3.one * scale);
        public static bool PlayerScaleIs(this Player target, Vector3 scale) => target.GameObject.transform.localScale == scale;
        public static bool PlayerScaleIs(this Player target, float scale) => PlayerScaleIs(target, Vector3.one * scale);

        public static void MessageTranslated(string message, string translation, bool isHeld = false, bool isNoisy = true, bool isSubtitles = true)
        {
            StringBuilder announcement = StringBuilderPool.Pool.Get();
            string[] cassies = message.Split('\n');
            string[] translations = translation.Split('\n');
            for (int i = 0; i < cassies.Length; i++)
                announcement.Append($"{translations[i].Replace(' ', ' ')}<size=0> {cassies[i]} </size><split>");

            RespawnEffectsController.PlayCassieAnnouncement(announcement.ToString(), isHeld, isNoisy, isSubtitles);
            StringBuilderPool.Pool.Return(announcement);
        }

        public static bool BreakDoor(DoorVariant doorBase, DoorDamageType type = DoorDamageType.ServerCommand)
        {
            if (doorBase is not IDamageableDoor damageableDoor || damageableDoor.IsDestroyed)
                return false;

            damageableDoor.ServerDamage(ushort.MaxValue, type);
            return true;
        }
        //TODO:ReloadWeapon
        /*public static void ReloadWeapon(this Player player)
        {
            if (player.CurrentItem == null)
                return;

            if (player.CurrentItem is not Firearm firearm)
                return;

            firearm.AmmoManagerModule.ServerTryReload();
            player.Connection.Send(new RequestMessage(firearm.ItemSerial, RequestType.Reload));
        }*/

        public static bool IsAmmo(this ItemType item)
        {
            if (item != ItemType.Ammo9x19 && item != ItemType.Ammo12gauge && item != ItemType.Ammo44cal && item != ItemType.Ammo556x45)
            {
                return item == ItemType.Ammo762x39;
            }

            return true;
        }

        public static bool IsWeapon(this ItemType type, bool checkMicro = true)
        {
            switch (type)
            {
                case ItemType.GunCOM15:
                case ItemType.GunE11SR:
                case ItemType.GunCrossvec:
                case ItemType.GunFSP9:
                case ItemType.GunLogicer:
                case ItemType.GunCOM18:
                case ItemType.GunRevolver:
                case ItemType.GunAK:
                case ItemType.GunShotgun:
                case ItemType.ParticleDisruptor:
                case ItemType.GunCom45:
                case ItemType.GunFRMG0:
                case ItemType.Jailbird:
                    return true;
                case ItemType.MicroHID:
                    if (checkMicro)
                    {
                        return true;
                    }

                    break;
            }

            return false;
        }

        public static bool IsScp(this ItemType type)
        {
            return type is ItemType.SCP018 or ItemType.SCP500 or ItemType.SCP268 or ItemType.SCP207 or ItemType.SCP244a or ItemType.SCP244b or ItemType.SCP2176;
        }

        /// <summary>
        /// 检查角色是否为SCP
        /// </summary>
        /// <param name="role">角色类型</param>
        /// <returns>是否为SCP</returns>
        public static bool IsSCP(RoleTypeId role)
        {
            return role switch
            {
                RoleTypeId.Scp173 => true,
                RoleTypeId.Scp106 => true,
                RoleTypeId.Scp049 => true,
                RoleTypeId.Scp0492 => true,
                RoleTypeId.Scp096 => true,
                RoleTypeId.Scp939 => true,
                RoleTypeId.Scp079 => true,
                _ => false
            };
        }

        public static bool IsThrowable(this ItemType type)
        {
            return type is ItemType.SCP018 or ItemType.GrenadeHE or ItemType.GrenadeFlash or ItemType.SCP2176;
        }

        public static bool IsMedical(this ItemType type)
        {
            return type is ItemType.Painkillers or ItemType.Medkit or ItemType.SCP500 or ItemType.Adrenaline;
        }

        public static bool IsUtility(this ItemType type)
        {
            return type is ItemType.Flashlight or ItemType.Radio;
        }

        public static bool IsArmor(this ItemType type)
        {
            return type is ItemType.ArmorLight or ItemType.ArmorCombat or ItemType.ArmorHeavy;
        }

        public static bool IsKeycard(this ItemType type)
        {
            var keycardTypes = new HashSet<ItemType>
            {
                ItemType.KeycardScientist,
                ItemType.KeycardResearchCoordinator,
                ItemType.KeycardZoneManager,
                ItemType.KeycardGuard,
                ItemType.KeycardMTFPrivate,
                ItemType.KeycardContainmentEngineer,
                ItemType.KeycardMTFOperative,
                ItemType.KeycardMTFCaptain,
                ItemType.KeycardFacilityManager,
                ItemType.KeycardChaosInsurgency,
                ItemType.KeycardO5
            };

            return keycardTypes.Contains(type);
        }

        public static Team GetTeam2(this RoleTypeId typeId)
        {
            return typeId switch
            {
                RoleTypeId.ChaosConscript or RoleTypeId.ChaosRifleman or RoleTypeId.ChaosRepressor or RoleTypeId.ChaosMarauder => Team.ChaosInsurgency,
                RoleTypeId.Scientist => Team.Scientists,
                RoleTypeId.ClassD => Team.ClassD,
                RoleTypeId.Scp173 or RoleTypeId.Scp106 or RoleTypeId.Scp049 or RoleTypeId.Scp079 or RoleTypeId.Scp096 or RoleTypeId.Scp0492 or RoleTypeId.Scp939 or RoleTypeId.Scp3114 => Team.SCPs,
                RoleTypeId.NtfSpecialist or RoleTypeId.NtfSergeant or RoleTypeId.NtfCaptain or RoleTypeId.NtfPrivate or RoleTypeId.FacilityGuard => Team.FoundationForces,
                RoleTypeId.Tutorial => Team.OtherAlive,
                _ => Team.Dead,
            };
        }

        public static void Broadcast(string text, ushort time, Broadcast.BroadcastFlags broadcastFlags)
        {
            global::Broadcast.Singleton.GetComponent<Broadcast>().RpcAddElement(text, time, broadcastFlags);
        }

        public static void ShowBroadcast(this Player player, string text, ushort time, Broadcast.BroadcastFlags broadcastFlags)
        {
            global::Broadcast.Singleton.GetComponent<Broadcast>().TargetAddElement(player.ReferenceHub.characterClassManager.connectionToClient, text, time, broadcastFlags);
        }

        public static Vector3 GetRandomSpawnLocation(this RoleTypeId roleType)
        {
            if (!PlayerRoleLoader.TryGetRoleTemplate(roleType, out PlayerRoleBase @base))
                return Vector3.zero;

            if (@base is not IFpcRole fpc)
                return Vector3.zero;

            ISpawnpointHandler spawn = fpc.SpawnpointHandler;
            if (spawn is null)
                return Vector3.zero;

            if (!spawn.TryGetSpawnpoint(out Vector3 pos, out float _))
                return Vector3.zero;

            return pos;
        }

        public static void ChangeAppearance(this Player player, RoleTypeId type)
        {
            foreach (var pl in PlayerList.Where(x => x.PlayerId != player.PlayerId && x.IsReady))
            {
                pl.Connection.Send(new RoleSyncInfo(player.ReferenceHub, type, pl.ReferenceHub));
            }
        }

        public static bool TryGetRoleBase(this RoleTypeId roleType, out PlayerRoleBase roleBase)
        {
            return PlayerRoleLoader.TryGetRoleTemplate(roleType, out roleBase);
        }

        public static IEnumerator<float> PositionCheckerCoroutine(Player player)
        {
            Vector3 position = player.Position;

            int timeChecker = 0;

            while (true)
            {
                if (player is null || !player.IsAlive || Round.IsRoundEnded || player.Team is not Team.SCPs)
                {
                    yield break;
                }

                if (position != player.Position)
                {
                    timeChecker = 0;
                    position = player.Position;
                }
                else
                {
                    timeChecker++;

                    if (timeChecker >= 2)
                    {
                        player.Heal(3f);
                    }
                }

                yield return Timing.WaitForSeconds(1f);
            }
        }

        public static IEnumerator<float> InAmmo()
        {
            while (true)
            {
                if (Round.IsRoundEnded) yield break;
                foreach (Player Player in PlayerList.Where(x => x.IsHuman))
                {
                    Player.SetAmmo(ItemType.Ammo9x19, 180);
                    Player.SetAmmo(ItemType.Ammo12gauge, 18);
                    Player.SetAmmo(ItemType.Ammo44cal, 18);
                    Player.SetAmmo(ItemType.Ammo762x39, 180);
                    Player.SetAmmo(ItemType.Ammo556x45, 180);
                }
                yield return Timing.WaitForSeconds(3f);
            }
        }

        public static bool IsSameTeam(this Player player1, Player player2)
        {
            if ((player1.Team is Team.FoundationForces || player1.Team is Team.Scientists) && (player2.Team is Team.Scientists || player2.Team is Team.FoundationForces))
            {
                return true;
            }

            if ((player1.Team is Team.ChaosInsurgency || player1.Team is Team.ClassD) && (player2.Team is Team.ClassD || player2.Team is Team.ChaosInsurgency))
            {
                return true;
            }

            if (player1.Team == player2.Team)
            {
                return true;
            }

            return false;
        }

        public static Dictionary<Scp914KnobSetting, string> Scp914KnobSettingTranslate = new()
        {
            {Scp914KnobSetting.Rough , "超粗加工" },
            {Scp914KnobSetting.Coarse , "粗加工" },
            {Scp914KnobSetting.OneToOne , "1:1加工" },
            {Scp914KnobSetting.Fine , "精加工" },
            {Scp914KnobSetting.VeryFine , "超精加工" }
        };

        public static IEnumerator<float> ServerHandle()
        {
            List<RoomName> IGNames = [RoomName.Hcz079, RoomName.Hcz049, RoomName.HczArmory, RoomName.LczArmory, RoomName.HczMicroHID];
            List<ItemType> IGItems = [ItemType.MicroHID, ItemType.ParticleDisruptor, ItemType.KeycardO5,
                                      ItemType.SCP207, ItemType.SCP500, ItemType.SCP268, ItemType.SCP018,
                                      ItemType.SCP1853, ItemType.AntiSCP207, ItemType.SCP2176,
                                      ItemType.SCP244a, ItemType.SCP244b];
            while (true)
            {
                Server.SendBroadcast("<size=40><color=#FFC0CB>阿慈谷日富美</color>正在打扫设施~</size>" , 10 , global::Broadcast.BroadcastFlags.Normal);

                BasicRagdoll[] Ragdolls = [.. (from r in UnityEngine.Object.FindObjectsByType<BasicRagdoll>(FindObjectsSortMode.None)
                                        orderby r.Info.CreationTime descending
                                        select r)];
                for (int i = 0; i < Ragdolls.Length; i++)
                {
                    NetworkServer.Destroy(Ragdolls[i].gameObject);
                }

                ItemPickupBase[] items = UnityEngine.Object.FindObjectsByType<ItemPickupBase>(FindObjectsSortMode.None);

                for (int i = 0; i < items.Length; i++)
                {
                    // 修复逻辑：如果物品在排除房间中或在排除物品列表中，则不删除
                    if (IGNames.Contains(Room.GetRoomAtPosition(items[i].Position).Name) || IGItems.Contains(items[i].Info.ItemId))
                        continue; // 跳过删除，保留物品

                    NetworkServer.Destroy(items[i].gameObject);
                }

                yield return Timing.WaitForSeconds(120f);
            }
        }
        public static List<string> RainbowColors =
        [
            "red" , "yellow" , "cyan" , "green" , "aqua" , "pink" , "white" , "orange"
        ];
        public static IEnumerator<float> RainbowBadge(Player Player)
        {
            while (true)
            {
                yield return Timing.WaitForSeconds(0.5f);

                // 检查玩家是否仍然有效且称号未被隐藏
                if (Player == null || Player.GameObject == null)
                {
                    Logger.Debug($"彩色称号协程停止 - 玩家对象无效");
                    yield break;
                }

                // 检查称号是否被隐藏
                var (badge, color) = Player.GetVisibleBadge();
                if (string.IsNullOrEmpty(badge) || color != "colorful")
                {
                    Logger.Debug($"彩色称号协程停止 - 玩家 {Player.Nickname} 称号被隐藏或不是彩色称号");
                    yield break;
                }

                Player.GroupColor = RainbowColors.RandomItem();
            }
        }

        public static IEnumerator<float> BadgeHandle(LabApi.Features.Wrappers.Player Player , string[] badges)
        {
            int length = badges.Length;
            int index = 0;
            while (true)
            {
                yield return Timing.WaitForSeconds(1f);

                // 检查玩家是否仍然有效且称号未被隐藏
                if (Player == null || Player.GameObject == null)
                {
                    yield break;
                }

                // 检查称号是否被隐藏
                var (badge, color) = Player.GetVisibleBadge();
                if (string.IsNullOrEmpty(badge))
                {
                    yield break;
                }

                if (index < length)
                {
                    Player.GroupName = badges[index];
                    index++;
                }
                else
                {
                    index = 0;
                    Player.GroupName = badges[index];
                }
            }
        }

        public static Dictionary<RoleTypeId, float> healthDict = new()
        {
            [RoleTypeId.Scp173] = 5000,
            [RoleTypeId.Scp939] = 2800,
            [RoleTypeId.Scp049] = 3000,
            [RoleTypeId.Scp0492] = 500,
            [RoleTypeId.Scp096] = 3000,
            [RoleTypeId.Scp106] = 2600
        };

        public static IEnumerator<float> Guard()
        {
            while (true)
            {
                yield return Timing.WaitForSeconds(1f);

                if (Round.IsRoundEnded) yield break;

                // 创建PlayerList的副本来避免迭代时修改的问题
                var playerListCopy = PlayerList.ToList();

                foreach (Player player in playerListCopy)
                {
                    if (player.Role is RoleTypeId.FacilityGuard && IsInOffDutyArea(player.Position))
                    {
                        try
                        {
                            // 创建局部变量避免闭包问题
                            var userId = player.UserId;

                            Logger.Debug($"保安 {player.Nickname} 开始下班流程");

                            List<ItemBase> olditems = [];
                            foreach (var item in player.Items)
                            {
                                olditems.Add(item.Base);
                            }

                            player.SetRole(RoleTypeId.NtfSpecialist, RoleChangeReason.RemoteAdmin);

                            // 延迟显示下班提示，使用AddHint方法
                            Timing.CallDelayed(0.5f, () =>
                            {
                                var targetPlayer = Player.Get(userId: userId);
                                if (targetPlayer != null && targetPlayer.IsReady)
                                {
                                    var offDutyHint = new Hint
                                    {
                                        Alignment = HintServiceMeow.Core.Enum.HintAlignment.Center,
                                        YCoordinate = 400,
                                        FontSize = 20,
                                        LineHeight = 5,
                                        Text = "<color=#00FF00>下班成功！</color>\n<color=#FFFF00>请在5秒内清空背包格数到你逃离前所拥有的物品数量</color>"
                                    };

                                    targetPlayer.AddHint(offDutyHint);

                                    // 10秒后移除提示
                                    Timing.CallDelayed(10f, () =>
                                    {
                                        targetPlayer.RemoveHint(offDutyHint);
                                    });

                                    Logger.Debug($"保安 {targetPlayer.Nickname} 下班提示已显示");
                                }
                            });

                        Timing.CallDelayed(5f, () =>
                        {
                            try
                            {
                                // 通过UserId重新获取玩家，避免闭包问题
                                var targetPlayer = Player.Get(userId: userId);
                                if (targetPlayer == null || !targetPlayer.IsReady)
                                {
                                    Logger.Warn($"无法为玩家 {userId} 恢复物品：玩家不存在或未准备好");
                                    return;
                                }

                                Logger.Debug($"开始为玩家 {targetPlayer.Nickname} 恢复 {olditems.Count} 个物品");

                                foreach (var item in olditems)
                                {
                                    try
                                    {
                                        if (item.ItemTypeId.IsWeapon())
                                        {
                                            var firearm = targetPlayer.ReferenceHub.inventory.ServerAddItem(item.ItemTypeId, ItemAddReason.AdminCommand) as Firearm;
                                            if (firearm != null)
                                            {
                                                firearm.ApplyAttachmentsCode(firearm.GetCurrentAttachmentsCode(), true);
                                            }
                                            Logger.Debug($"恢复武器: {item.ItemTypeId}");
                                        }
                                        else
                                        {
                                            targetPlayer.AddItem(item.ItemTypeId);
                                            Logger.Debug($"恢复物品: {item.ItemTypeId}");
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Logger.Error($"恢复保安物品失败: {ex.Message}");
                                    }
                                }

                                Logger.Debug($"玩家 {targetPlayer.Nickname} 物品恢复完成");
                            }
                            catch (Exception ex)
                            {
                                Logger.Error($"延迟恢复物品失败: {ex.Message}");
                            }
                        });
                        }
                        catch (Exception ex)
                        {
                            Logger.Error($"保安 {player?.Nickname ?? "未知"} 下班处理失败: {ex.Message}");
                            Logger.Debug($"保安下班异常详情: {ex}");
                        }
                    }
                }


            }
        }

        /// <summary>
        /// 检查位置是否在保安下班区域内
        /// </summary>
        /// <param name="position">玩家位置</param>
        /// <returns>是否在下班区域</returns>
        private static bool IsInOffDutyArea(Vector3 position)
        {
            return position.x <= 150 && position.x >= 115 &&
                   position.y <= 300 && position.y >= 280 &&
                   position.z <= 32 && position.z >= 10;
        }
    }
}
