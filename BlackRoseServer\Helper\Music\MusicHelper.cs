﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Mirror;
using SCPSLAudioApi.AudioCore;
using static SCPSLAudioApi.AudioCore.AudioPlayerBase;
using UnityEngine;
using LabApi.Features.Wrappers;
using Logger = LabApi.Features.Console.Logger;

namespace BlackRoseServer.Helper.Music
{
    public class MusicHelper
    {
        private static readonly MusicHelper instance = new();

        private int num = 1;
        private bool _isAudioAvailable = false;

        public static MusicHelper Instance => instance;

        /// <summary>
        /// 获取音频功能是否可用
        /// </summary>
        public bool IsAudioAvailable => _isAudioAvailable;

        /// <summary>
        /// 获取或设置放音频的玩家(NPC)
        /// </summary>
        public Dictionary<string, ReferenceHub> MusicNpc { get; } = [];
        private MusicHelper() { }

        internal void Init()
        {
            try
            {
                OnFinishedTrack += TrackFinished;
                _isAudioAvailable = true;
                Logger.Info("音频系统初始化成功");
            }
            catch (Exception ex)
            {
                _isAudioAvailable = false;
                Logger.Warn($"音频系统初始化失败，音效功能将被禁用: {ex.Message}");
                Logger.Debug($"音频系统初始化详细错误: {ex}");
            }
        }

        private void TrackFinished(AudioPlayerBase playerBase, string track, bool directPlay, ref int nextQueuePos)
        {
            Stop(playerBase);
        }

        private ReferenceHub CreateMusicNpc(string name)
        {
            var newNpc = UnityEngine.Object.Instantiate(NetworkManager.singleton.playerPrefab);
            ReferenceHub hubNpc = newNpc.GetComponent<ReferenceHub>();
            NetworkServer.AddPlayerForConnection(new FakeConnection(0), newNpc);
            hubNpc.nicknameSync.Network_myNickSync = name;
            MusicNpc.Add(name, hubNpc);
            return hubNpc;
        }

        /// <summary>
        /// 立刻停止播放音频
        /// </summary>
        /// <param name="playerBase">AudioPlayerBase</param>
        public void Stop(AudioPlayerBase playerBase)
        {
            if (playerBase == null) return;
            ReferenceHub npc = playerBase.Owner;
            playerBase.Stoptrack(true);
            MusicNpc.Remove(npc.nicknameSync.Network_myNickSync);
            CustomNetworkManager.TypedSingleton.OnServerDisconnect(npc.connectionToClient);
            UnityEngine.Object.Destroy(npc.gameObject);
        }
        /// <summary>
        /// 播放音频
        /// </summary>
        /// <param name="musicFile">音频文件</param>
        /// <param name="npcName">NPC名称</param>
        /// <returns></returns>
        public AudioPlayerBase Play(string musicFile, string npcName)
        {
            return Play(musicFile, npcName, new TrackEvent(), null, 0, [], false, 80, false);
        }
        /// <summary>
        /// 播放音频
        /// </summary>
        /// <param name="musicFile">音频文件</param>
        /// <param name="npcName">NPC名称</param>
        /// <param name="source">传播距离检测源头玩家</param>
        /// <param name="distance">传播距离</param>
        /// <returns></returns>
        public AudioPlayerBase Play(string musicFile, string npcName, Player source, float distance)
        {
            return Play(musicFile, npcName, new TrackEvent(), source, distance, [], false, 80, false);
        }
        public AudioPlayerBase Play(string musicFile, string npcName, Player source)
        {
            return Play(musicFile, npcName, new TrackEvent(), source, [], false, 80, false);
        }
        /// <summary>
        /// 播放音频
        /// </summary>
        /// <param name="musicFile">音频文件</param>
        /// <param name="npcName">NPC名称</param>
        /// <param name="trackEvent">播放事件</param>
        /// <param name="source">传播距离检测源头玩家</param>
        /// <param name="distance">传播距离</param>
        /// <param name="extraPlay">额外可接收音频的玩家</param>
        /// <param name="isSole">是否覆盖播放</param>
        /// <param name="volume">音量大小</param>
        /// <param name="isLoop">是否循环</param>
        /// <returns></returns>
        public AudioPlayerBase Play(string musicFile, string npcName, TrackEvent trackEvent, Player source, float distance, Player[] extraPlay, bool isSole = false, float volume = 80, bool isLoop = false)
        {
            AudioPlayerBase audioPlayerBase = null;
            try
            {
                OnTrackLoaded += trackEvent.TrackLoaded;
                if (!MusicNpc.TryGetValue(npcName, out ReferenceHub npc))
                {
                    npc = CreateMusicNpc(npcName);
                    audioPlayerBase = Get(npc);
                }
                else
                {
                    if (!isSole)
                    {
                        npc = CreateMusicNpc(npcName);
                        audioPlayerBase = Get(npc);
                        MusicNpc.Add(num + npcName, npc);
                        num++;
                    }
                }

                if (extraPlay != null)
                {
                    audioPlayerBase.AudioToPlay = extraPlay.Select((s) => { return s.UserId; }).ToList();
                }

                if (distance != 0)
                {
                    audioPlayerBase.AudioToPlay ??= [];
                    foreach (var player in Player.List.Where(p => Vector3.Distance(p.Position, source.Position) <= distance))
                    {
                        audioPlayerBase.AudioToPlay.Add(player.UserId);
                    }
                }

                audioPlayerBase.Enqueue($"{musicFile}", 0);
                audioPlayerBase.Volume = volume;
                audioPlayerBase.Loop = isLoop;
                audioPlayerBase.Play(0);
            }
            catch (Exception)
            {
                Stop(audioPlayerBase);
            }
            return audioPlayerBase;
        }
        public AudioPlayerBase Play(string musicFile, string npcName, TrackEvent trackEvent, Player source, Player[] extraPlay, bool isSole = false, float volume = 80, bool isLoop = false)
        {
            AudioPlayerBase audioPlayerBase = null;
            try
            {
                OnTrackLoaded += trackEvent.TrackLoaded;
                if (!MusicNpc.TryGetValue(npcName, out ReferenceHub npc))
                {
                    npc = CreateMusicNpc(npcName);
                    audioPlayerBase = Get(npc);
                }
                else
                {
                    if (!isSole)
                    {
                        npc = CreateMusicNpc(npcName);
                        audioPlayerBase = Get(npc);
                        MusicNpc.Add(num + npcName, npc);
                        num++;
                    }
                }

                if (extraPlay != null)
                {
                    audioPlayerBase.BroadcastTo = extraPlay.Select((s) => { return s.PlayerId; }).ToList();
                }

                audioPlayerBase.BroadcastTo ??= [];
                audioPlayerBase.BroadcastTo.Add(source.PlayerId);

                audioPlayerBase.Enqueue($"{musicFile}", 0);
                audioPlayerBase.Volume = volume;
                audioPlayerBase.Loop = isLoop;
                audioPlayerBase.Play(0);
            }
            catch (Exception)
            {
                Stop(audioPlayerBase);
            }
            return audioPlayerBase;
        }

        public readonly struct TrackEvent(TrackLoaded trackLoaded)
        {
            public TrackLoaded TrackLoaded { get; } = trackLoaded;
        }
    }
}
