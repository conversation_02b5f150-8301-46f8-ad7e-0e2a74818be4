﻿using LabApi.Features.Wrappers;
using PlayerRoles;
using System.Collections.Generic;
using UnityEngine;

namespace BlackRoseServer.Helper.SCP
{
    public class SCPHelper
    {
        public Player Player;
        public string OldGroupName;
        public string OldGroupColor;
        public SCPHelper(Player Player, string NewGroupName, string NewGroupColor, List<ItemType> Items)
        {
            this.Player = Player;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            AddItems(Player, Items);
        }
        public SCPHelper(Player Player, string NewGroupName, string NewGroupColor)
        {
            this.Player = Player;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
        }
        public SCPHelper(Player Player, string NewGroupName, string NewGroupColor, Vector3 Vector3)
        {
            this.Player = Player;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            Player.Position = Vector3;
        }
        public SCPHelper(Player Player, RoleTypeId NewRole, string NewGroupName, string NewGroupColor, Vector3 Vector3)
        {
            this.Player = Player;
            Player.SetRole(NewRole);
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            Player.Position = Vector3;
        }
        public SCPHelper(Player Player, RoleTypeId NewRole, float Health, string NewGroupName, string NewGroupColor)
        {
            this.Player = Player;
            Player.SetRole(NewRole);
            Player.Health = Health;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
        }
        public SCPHelper(Player Player, RoleTypeId NewRole, float Health, string NewGroupName, string NewGroupColor, Vector3 Vector3)
        {
            this.Player = Player;
            Player.SetRole(NewRole);
            Player.Health = Health;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            Player.Position = Vector3;
        }
        public SCPHelper(Player Player, RoleTypeId NewRole, string NewGroupName, string NewGroupColor)
        {
            this.Player = Player;
            Player.SetRole(NewRole);
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
        }
        public SCPHelper(Player Player, RoleTypeId NewRole, string NewGroupName, string NewGroupColor, List<ItemType> Items)
        {
            this.Player = Player;
            Player.SetRole(NewRole);
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            AddItems(Player, Items);
        }
        public SCPHelper(Player Player, string NewGroupName, string NewGroupColor, string Broadcast, ushort Time)
        {
            this.Player = Player;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            Player.SendBroadcast(Broadcast, Time);
        }
        public SCPHelper(Player Player, string NewGroupName, string NewGroupColor, List<ItemType> Items, string Broadcast, ushort Time)
        {
            this.Player = Player;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            AddItems(Player, Items);
            Player.SendBroadcast(Broadcast, Time);
        }
        public SCPHelper(Player Player, RoleTypeId NewRole, string NewGroupName, string NewGroupColor, List<ItemType> Items, string Broadcast, ushort Time)
        {
            this.Player = Player;
            Player.SetRole(NewRole);
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            AddItems(Player, Items);
            Player.SendBroadcast(Broadcast, Time);
        }
        public SCPHelper(Player Player, float Health, string NewGroupName, string NewGroupColor, List<ItemType> Items)
        {
            this.Player = Player;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            AddItems(Player, Items);
            Player.Health = Health;
        }
        public SCPHelper(Player Player, float Health, string NewGroupName, string NewGroupColor)
        {
            this.Player = Player;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            Player.Health = Health;
        }
        public SCPHelper(Player Player, float Health, RoleTypeId NewRole, string NewGroupName, string NewGroupColor, List<ItemType> Items)
        {
            this.Player = Player;
            Player.SetRole(NewRole);
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            AddItems(Player, Items);
            Player.Health = Health;
        }
        public SCPHelper(Player Player, float Health, string NewGroupName, string NewGroupColor, string Broadcast, ushort Time)
        {
            this.Player = Player;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            Player.SendBroadcast(Broadcast, Time);
            Player.Health = Health;
        }
        public SCPHelper(Player Player, float Health, string NewGroupName, string NewGroupColor, List<ItemType> Items, string Broadcast, ushort Time)
        {
            this.Player = Player;
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            AddItems(Player, Items);
            Player.SendBroadcast(Broadcast, Time);
            Player.Health = Health;
        }
        public SCPHelper(Player Player, float Health, RoleTypeId NewRole, string NewGroupName, string NewGroupColor, List<ItemType> Items, string Broadcast, ushort Time)
        {
            this.Player = Player;
            Player.SetRole(NewRole);
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            AddItems(Player, Items);
            Player.SendBroadcast(Broadcast, Time);
            Player.Health = Health;
        }
        public SCPHelper(Player Player, float Health, RoleTypeId NewRole, string NewGroupName, string NewGroupColor, string Broadcast, ushort Time)
        {
            this.Player = Player;
            Player.SetRole(NewRole);
            OldGroupName = Player.GroupName;
            OldGroupColor = Player.GroupColor;
            Player.GroupName = NewGroupName;
            Player.GroupColor = NewGroupColor;
            Player.SendBroadcast(Broadcast, Time);
            Player.Health = Health;
        }

        public void OnPlayerDead(Player Player, string cassie, string tcassie)
        {
            Player.GroupName = OldGroupName;
            Player.GroupColor = OldGroupColor;
            XHelper.MessageTranslated(cassie, tcassie);
        }

        public void AddItems(Player Player, List<ItemType> Items)
        {
            foreach (ItemType Item in Items)
            {
                Player.AddItem(Item);
            }
        }
    }
}
