# HintManager使用指南

## 概述

HintManager是一个统一的Hint管理系统，用于集中管理所有游戏内的提示信息显示。它提供了模板化的Hint创建、自动生命周期管理、位置冲突检测等功能。

## 核心特性

### 1. 统一管理 🎯
- 集中管理所有Hint的创建、显示和销毁
- 自动处理Hint生命周期，避免内存泄漏
- 统一的样式和配置管理

### 2. 模板系统 📋
- 预定义常用Hint类型（MVP、升级、通知等）
- 统一的视觉风格和配置
- 支持自定义配置和扩展

### 3. 位置管理 📍
- 按Y坐标分层管理，避免重叠
- 智能冲突检测和处理
- 支持重叠控制和优先级管理

### 4. 生命周期管理 ⏰
- 自动清理过期Hint
- 支持持久化、临时和自动更新三种类型
- 延时显示和批量操作支持

## 快速开始

### 基本使用

```csharp
using BlackRoseServer.Manager;

// 显示通知
player.ShowNotification("欢迎进入服务器！", "#00FF00", 5f);

// 显示警告
player.ShowWarning("请注意安全！", 8f);

// 显示成功信息
player.ShowSuccess("操作成功完成！", 3f);

// 显示错误信息
player.ShowError("操作失败，请重试！", 10f);
```

### 预定义类型使用

```csharp
// MVP显示
player.ShowMVP("PlayerName", 150.5f);

// 升级显示
player.ShowLevelUp(5, 6);

// 保安下班提示
player.ShowSecurityOffDuty();

// SCP-914启动信息
player.ShowSCP914Info("SCP-914已启动，设置：精细");

// 电梯交互
player.ShowElevatorInteraction("电梯正在移动到地面层");
```

### 批量操作

```csharp
// 给所有玩家显示通知
var players = XHelper.PlayerList.Where(p => p != null);
players.ShowNotificationToAll("服务器重启倒计时：5分钟", "#FF0000", 10f);

// 给多个玩家显示MVP
var nearbyPlayers = GetNearbyPlayers(position, 10f);
HintManager.Instance.ShowMVP(nearbyPlayers, "MVP玩家", 200.0f);
```

## 高级使用

### 自定义配置

```csharp
// 创建自定义配置
var customConfig = new HintConfig
{
    Type = HintType.Custom,
    Text = "<color=#FFD700>自定义消息</color>",
    YCoordinate = 600,
    FontSize = 24,
    Duration = 15f,
    Priority = HintPriority.High,
    AllowOverlap = false
};

string hintId = HintManager.Instance.ShowHint(player, customConfig);
```

### 使用模板创建

```csharp
// 使用模板创建MVP配置
var mvpConfig = HintTemplate.CreateMVP("玩家名称", 180.5f);
mvpConfig.Duration = 20f; // 自定义持续时间
string hintId = HintManager.Instance.ShowHint(player, mvpConfig);

// 使用模板创建通知配置
var notificationConfig = HintTemplate.CreateNotification("重要通知", "#FF6600");
notificationConfig.YCoordinate = 350; // 自定义位置
string hintId = HintManager.Instance.ShowHint(player, notificationConfig);
```

### 生命周期管理

```csharp
// 手动移除特定Hint
string hintId = player.ShowNotification("临时消息");
// ... 一些逻辑后
HintManager.Instance.RemoveHint(hintId);

// 移除玩家所有Hint
int removedCount = player.RemoveAllHints();

// 移除特定类型的Hint
int removedCount = player.RemoveHintsByType(HintType.Notification);

// 检查是否有特定类型的Hint
bool hasWarning = player.HasHintType(HintType.Warning);
```

## 预定义Hint类型

### 显示层级（Y坐标）

| 类型 | Y坐标 | 用途 | 优先级 |
|------|-------|------|--------|
| Critical | 50 | 紧急信息 | Critical |
| Timer | 105 | 计时器显示 | High |
| MVP | 150 | MVP显示 | High |
| LevelUp | 200 | 升级显示 | High |
| Chat | 250 | 聊天信息 | Normal |
| Leaderboard | 300 | 排行榜 | Normal |
| Notification | 400 | 通知信息 | Normal |
| Interaction | 800 | 交互信息 | Normal |
| ExperienceBar | 1000 | 经验进度条 | Low |
| Spectator | 20 | 观察者信息 | Normal |

### 生命周期类型

- **Persistent**: 持久化显示，需要手动移除
- **Temporary**: 临时显示，自动移除
- **AutoUpdate**: 自动文本更新

## 迁移指南

### 从旧代码迁移

**旧代码模式：**
```csharp
var hint = new Hint
{
    Alignment = HintAlignment.Center,
    YCoordinate = 400,
    FontSize = 20,
    LineHeight = 5,
    Text = "消息内容"
};

player.AddHint(hint);
Timing.CallDelayed(5f, () => player.RemoveHint(hint));
```

**新代码模式：**
```csharp
// 简单方式
player.ShowNotification("消息内容", "#FFFFFF", 5f);

// 或使用迁移助手
HintMigrationHelper.ShowHintToMultiplePlayers(
    new[] { player }, 
    HintType.Notification, 
    "消息内容", 
    5f
);
```

### 批量迁移

使用迁移助手可以快速迁移现有代码：

```csharp
// MVP显示迁移
HintMigrationHelper.ShowMVPDisplay(players, mvpPlayerName, damage);

// 升级显示迁移
HintMigrationHelper.ShowLevelUpDisplay(player, levelBefore, levelAfter);

// SCP-914信息迁移
HintMigrationHelper.ShowSCP914StartupInfo(nearbyPlayers, message);

// 电梯交互迁移
HintMigrationHelper.ShowElevatorInteraction(nearbyPlayers, interactionMessage);

// 保安下班提示迁移
HintMigrationHelper.ShowSecurityOffDutyHint(player);
```

## 最佳实践

### 1. 选择合适的Hint类型
- 重要信息使用高优先级类型（MVP、LevelUp、Warning、Error）
- 一般信息使用普通优先级类型（Notification、Success）
- 持续显示的信息使用Persistent类型
- 临时提示使用Temporary类型

### 2. 合理设置持续时间
- 重要信息：10-15秒
- 一般通知：3-8秒
- 快速提示：1-3秒
- 错误信息：8-12秒

### 3. 避免信息过载
- 同一时间不要显示过多Hint
- 使用优先级管理重要性
- 合理利用位置分层

### 4. 性能考虑
- 避免频繁创建和销毁Hint
- 使用批量操作处理多个玩家
- 及时清理不需要的Hint

## 调试和监控

### 获取统计信息
```csharp
string stats = HintManager.Instance.GetStatistics();
Logger.Info(stats);
```

### 检查玩家Hint状态
```csharp
int hintCount = player.GetHintCount();
bool hasNotification = player.HasHintType(HintType.Notification);
```

### 测试功能
```csharp
// 运行基本功能测试
HintManagerTest.RunBasicTests();

// 运行性能测试
HintManagerTest.RunPerformanceTests();

// 运行集成测试（需要真实玩家）
HintManagerTest.RunIntegrationTests(testPlayer);
```

## 常见问题

### Q: Hint不显示怎么办？
A: 检查以下几点：
1. 玩家对象是否有效
2. 配置是否正确
3. 是否有位置冲突
4. 检查日志中的错误信息

### Q: 如何处理位置冲突？
A: 
1. 设置`AllowOverlap = true`允许重叠
2. 使用不同的Y坐标
3. 移除冲突的Hint后再显示新的

### Q: 如何自定义样式？
A: 
1. 创建自定义HintConfig
2. 修改预定义模板
3. 使用HTML标签设置颜色和格式

### Q: 性能优化建议？
A: 
1. 使用批量操作
2. 避免频繁创建销毁
3. 合理设置持续时间
4. 定期清理不需要的Hint

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的Hint管理功能
- 提供预定义模板和扩展方法
- 包含迁移助手和测试工具
