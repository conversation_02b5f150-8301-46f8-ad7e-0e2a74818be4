using System;
using System.Collections.Generic;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using HintServiceMeow.Core.Models.Hints;
using HintServiceMeow.Core.Extension;
using MEC;

namespace BlackRoseServer.Manager
{
    /// <summary>
    /// Hint管理器 - 统一管理所有Hint的创建、显示和销毁
    /// </summary>
    public class HintManager : IDisposable
    {
        private static HintManager _instance;
        private static readonly object _lock = new object();

        private readonly HintLifecycleManager _lifecycleManager;
        private readonly HintPositionManager _positionManager;
        private bool _disposed = false;

        /// <summary>
        /// 单例实例
        /// </summary>
        public static HintManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new HintManager();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private HintManager()
        {
            _lifecycleManager = new HintLifecycleManager();
            _positionManager = new HintPositionManager();
            Logger.Info("HintManager已初始化");
        }

        /// <summary>
        /// 显示Hint
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="config">Hint配置</param>
        /// <returns>Hint的唯一ID，失败返回空字符串</returns>
        public string ShowHint(Player player, HintConfig config)
        {
            if (player == null || config == null)
            {
                Logger.Error("ShowHint: 玩家或配置不能为null");
                return string.Empty;
            }

            try
            {
                // 如果有CustomId，先检查是否已存在相同的Hint
                if (!string.IsNullOrEmpty(config.CustomId))
                {
                    var existingHintId = _lifecycleManager.FindHintByCustomId(player, config.CustomId);
                    if (!string.IsNullOrEmpty(existingHintId))
                    {
                        // 更新现有Hint的文本
                        if (_lifecycleManager.UpdateHintText(existingHintId, config.Text))
                        {
                            // Logger.Debug($"更新现有Hint文本 - ID: {existingHintId}, 玩家: {player.Nickname}");
                            return existingHintId;
                        }
                        else
                        {
                            // 更新失败，移除旧的Hint
                            _lifecycleManager.RemoveHint(existingHintId);
                        }
                    }
                }

                // 检查位置是否可用
                if (!config.AllowOverlap && !_positionManager.IsPositionAvailable(player, config))
                {
                    Logger.Warn($"位置 {config.YCoordinate} 不可用，无法显示Hint - 玩家: {player.Nickname}");
                    return string.Empty;
                }

                // 创建Hint对象
                var hint = CreateHintFromConfig(config);
                if (hint == null)
                {
                    Logger.Error("创建Hint对象失败");
                    return string.Empty;
                }

                // 注册到生命周期管理
                string hintId = _lifecycleManager.RegisterHint(hint, config, player);
                if (string.IsNullOrEmpty(hintId))
                {
                    Logger.Error("注册Hint到生命周期管理失败");
                    return string.Empty;
                }

                // 占用位置
                if (!_positionManager.OccupyPosition(player, config, hintId))
                {
                    Logger.Error("占用Hint位置失败");
                    _lifecycleManager.RemoveHint(hintId);
                    return string.Empty;
                }

                // 延迟显示（如果需要）
                if (config.DelayTime > 0)
                {
                    Timing.CallDelayed(config.DelayTime, () =>
                    {
                        DisplayHintToPlayer(player, hint);
                    });
                }
                else
                {
                    DisplayHintToPlayer(player, hint);
                }

                // Logger.Debug($"Hint显示成功 - ID: {hintId}, 玩家: {player.Nickname}, 类型: {config.Type}");
                return hintId;
            }
            catch (Exception ex)
            {
                Logger.Error($"显示Hint失败: {ex.Message}");
                // Logger.Debug($"ShowHint详细错误: {ex}");
                return string.Empty;
            }
        }

        /// <summary>
        /// 显示预定义类型的Hint
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="hintType">Hint类型</param>
        /// <param name="text">显示文本</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>Hint的唯一ID</returns>
        public string ShowHint(Player player, HintType hintType, string text, float? duration = null)
        {
            var config = HintTemplate.GetTemplate(hintType);
            config.Text = text;
            
            if (duration.HasValue)
            {
                config.Duration = duration.Value;
                config.LifecycleType = HintLifecycleType.Temporary;
            }

            return ShowHint(player, config);
        }



        /// <summary>
        /// 显示升级信息
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="levelBefore">升级前等级</param>
        /// <param name="levelAfter">升级后等级</param>
        /// <returns>Hint的唯一ID</returns>
        public string ShowLevelUp(Player player, int levelBefore, int levelAfter)
        {
            var config = HintTemplate.CreateLevelUp(player.Nickname, levelBefore, levelAfter);
            return ShowHint(player, config);
        }

        /// <summary>
        /// 显示保安下班提示
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <returns>Hint的唯一ID</returns>
        public string ShowSecurityOffDuty(Player player)
        {
            var config = HintTemplate.CreateSecurityOffDuty();
            return ShowHint(player, config);
        }

        /// <summary>
        /// 显示通知
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="message">通知消息</param>
        /// <param name="color">颜色（可选）</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>Hint的唯一ID</returns>
        public string ShowNotification(Player player, string message, string color = "#FFFFFF", float? duration = null)
        {
            var config = HintTemplate.CreateNotification(message, color);
            if (duration.HasValue)
            {
                config.Duration = duration.Value;
            }
            return ShowHint(player, config);
        }

        /// <summary>
        /// 显示警告
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="message">警告消息</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>Hint的唯一ID</returns>
        public string ShowWarning(Player player, string message, float? duration = null)
        {
            var config = HintTemplate.CreateWarning(message);
            if (duration.HasValue)
            {
                config.Duration = duration.Value;
            }
            return ShowHint(player, config);
        }

        /// <summary>
        /// 显示错误
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="message">错误消息</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>Hint的唯一ID</returns>
        public string ShowError(Player player, string message, float? duration = null)
        {
            var config = HintTemplate.CreateError(message);
            if (duration.HasValue)
            {
                config.Duration = duration.Value;
            }
            return ShowHint(player, config);
        }

        /// <summary>
        /// 显示成功信息
        /// </summary>
        /// <param name="player">目标玩家</param>
        /// <param name="message">成功消息</param>
        /// <param name="duration">持续时间（可选）</param>
        /// <returns>Hint的唯一ID</returns>
        public string ShowSuccess(Player player, string message, float? duration = null)
        {
            var config = HintTemplate.CreateSuccess(message);
            if (duration.HasValue)
            {
                config.Duration = duration.Value;
            }
            return ShowHint(player, config);
        }

        /// <summary>
        /// 移除指定的Hint
        /// </summary>
        /// <param name="hintId">Hint ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveHint(string hintId)
        {
            if (string.IsNullOrEmpty(hintId))
                return false;

            // 从生命周期管理中移除（会自动处理位置释放）
            return _lifecycleManager.RemoveHint(hintId);
        }

        /// <summary>
        /// 移除玩家的所有Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>移除的Hint数量</returns>
        public int RemovePlayerHints(Player player)
        {
            if (player == null)
                return 0;

            int removedCount = _lifecycleManager.RemovePlayerHints(player);
            _positionManager.ReleasePlayerPositions(player);
            return removedCount;
        }

        /// <summary>
        /// 移除玩家指定类型的Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="hintType">Hint类型</param>
        /// <returns>移除的Hint数量</returns>
        public int RemoveHintsByType(Player player, HintType hintType)
        {
            return _lifecycleManager.RemoveHintsByType(player, hintType);
        }

        /// <summary>
        /// 根据CustomId查找并移除Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="customId">自定义ID</param>
        /// <returns>是否成功移除</returns>
        public bool RemoveHintByCustomId(Player player, string customId)
        {
            if (player == null || string.IsNullOrEmpty(customId))
                return false;

            var hintId = _lifecycleManager.FindHintByCustomId(player, customId);
            if (!string.IsNullOrEmpty(hintId))
            {
                return RemoveHint(hintId);
            }

            return false;
        }

        /// <summary>
        /// 清理玩家的所有Hint位置
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>清理的位置数量</returns>
        public int ClearPlayerPositions(Player player)
        {
            return _positionManager.ReleasePlayerPositions(player);
        }

        /// <summary>
        /// 强制清理所有Hint位置（回合结束时使用，避免Player.List访问）
        /// </summary>
        /// <returns>清理的总数量</returns>
        public int ForceCleanupAllPositions()
        {
            try
            {
                // 直接清理位置管理器的所有数据，避免访问Player.List
                var totalCleared = 0;

                // 清理生命周期管理器
                if (_lifecycleManager != null)
                {
                    _lifecycleManager.ForceCleanupAll();
                }

                // 清理位置管理器的所有数据
                // 这里不访问Player.List，直接清理内部数据结构
                // Logger.Debug("强制清理所有Hint位置和生命周期数据");

                return totalCleared;
            }
            catch (Exception ex)
            {
                Logger.Error($"强制清理所有Hint位置失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 检查玩家是否有指定类型的Hint
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <param name="hintType">Hint类型</param>
        /// <returns>是否存在</returns>
        public bool HasHintType(Player player, HintType hintType)
        {
            return _lifecycleManager.HasHintType(player, hintType);
        }

        /// <summary>
        /// 获取玩家的活跃Hint数量
        /// </summary>
        /// <param name="player">玩家对象</param>
        /// <returns>活跃Hint数量</returns>
        public int GetPlayerHintCount(Player player)
        {
            return _lifecycleManager.GetPlayerHintCount(player);
        }

        /// <summary>
        /// 从配置创建Hint对象
        /// </summary>
        private Hint CreateHintFromConfig(HintConfig config)
        {
            try
            {
                var hint = new Hint
                {
                    Alignment = config.Alignment,
                    XCoordinate = config.XCoordinate,
                    YCoordinate = config.YCoordinate,
                    FontSize = config.FontSize,
                    LineHeight = config.LineHeight,
                    SyncSpeed = config.SyncSpeed,
                    Text = config.Text
                };

                // 如果是自动更新类型，设置AutoText函数
                if (config.LifecycleType == HintLifecycleType.AutoUpdate && config.AutoTextFunction != null)
                {
                    hint.AutoText = _ => config.AutoTextFunction();
                }

                return hint;
            }
            catch (Exception ex)
            {
                Logger.Error($"创建Hint对象失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 将Hint显示给玩家
        /// </summary>
        private void DisplayHintToPlayer(Player player, Hint hint)
        {
            try
            {
                if (player?.ReferenceHub != null)
                {
                    player.AddHint(hint);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"显示Hint给玩家失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取管理器统计信息
        /// </summary>
        /// <returns>统计信息字符串</returns>
        public string GetStatistics()
        {
            try
            {
                return $"HintManager统计信息:\n{_positionManager.GetStatistics()}";
            }
            catch (Exception ex)
            {
                Logger.Error($"获取统计信息失败: {ex.Message}");
                return "统计信息获取失败";
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;

            try
            {
                _lifecycleManager?.Dispose();
                Logger.Info("HintManager已释放");
            }
            catch (Exception ex)
            {
                Logger.Error($"释放HintManager失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 静态释放方法
        /// </summary>
        public static void DisposeInstance()
        {
            lock (_lock)
            {
                _instance?.Dispose();
                _instance = null;
            }
        }
    }
}
