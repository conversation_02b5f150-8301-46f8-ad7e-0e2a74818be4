﻿using System;
using System.Linq;
using HarmonyLib;
using LabApi.Features.Console;

namespace BlackRoseServer.Patches
{
    using BlackRoseServer.API;
    using LabApi.Features.Wrappers;

    [HarmonyPatch(typeof(NicknameSync), "set_Network_displayName")]
    public class SetNickPatch
    {
        internal static void Postfix(NicknameSync __instance, string value)
        {
            try
            {
                var ply = Player.Get(__instance._hub);
                if (ply == null)
                {

                    return;
                }

                // 安全获取配置，提供默认值作为后备
                string nickStructure = Config.Instance?.NickStructure ?? "[Lv.%lvl%]%name%";

                string formattedName;
                if (ply.DoNotTrack)
                {
                    // DNT玩家使用特殊格式
                    formattedName = $"[DNT]{value ?? "Unknown"}";
                }
                else
                {
                    string levelText = "0";
                    try
                    {
                        levelText = ply.GetLVL().ToString();
                    }
                    catch (Exception ex)
                    {

                        levelText = "0";
                    }

                    formattedName = nickStructure
                        .Replace("%lvl%", levelText)
                        .Replace("%name%", value ?? "Unknown");
                }

                __instance._displayName = formattedName;

                Logger.Debug($"SetNickPatch: 玩家 {value} 显示名称已设置为: {formattedName}");
            }
            catch (Exception ex)
            {
                Logger.Error($"SetNickPatch发生异常: {ex.Message}");


                // 发生异常时使用简单的显示名称
                __instance._displayName = value ?? "Unknown";
            }
        }
    }
}