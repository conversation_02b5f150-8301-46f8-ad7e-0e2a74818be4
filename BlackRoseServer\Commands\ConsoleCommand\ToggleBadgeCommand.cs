using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using CommandSystem;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.API;
using RemoteAdmin;
using MEC;

namespace BlackRoseServer.Commands.ConsoleCommand
{
    /// <summary>
    /// 切换称号显示状态的玩家命令
    /// </summary>
    [CommandHandler(typeof(ClientCommandHandler))]
    public class ToggleBadgeCommand : ICommand
    {
        public string Command => "togglebadge";
        public string[] Aliases => new[] { "tb", "hidebadge", "showbadge" };
        public string Description => "切换称号显示状态";

        /// <summary>
        /// 冷却时间（秒）
        /// </summary>
        private const int CooldownSeconds = 10;

        /// <summary>
        /// 玩家冷却时间记录
        /// </summary>
        private static readonly ConcurrentDictionary<string, DateTime> PlayerCooldowns = new();

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            try
            {
                if (!(sender is PlayerCommandSender playerSender))
                {
                    response = "此命令只能由玩家使用";
                    return false;
                }

                var player = Player.Get(playerSender.ReferenceHub);
                if (player == null)
                {
                    response = "无法获取玩家信息";
                    return false;
                }

                if (player.DoNotTrack)
                {
                    response = "DNT玩家无法使用此功能";
                    return false;
                }

                // 检查冷却时间
                if (IsPlayerOnCooldown(player.UserId, out TimeSpan remainingTime))
                {
                    response = $"命令冷却中，请等待 {remainingTime.TotalSeconds:F1} 秒后再试";
                    return false;
                }

                // 切换称号显示状态
                bool isHidden = player.ToggleBadgeVisibility();

                // 设置冷却时间
                SetPlayerCooldown(player.UserId);

                // 立即刷新称号显示
                RefreshPlayerBadgeDisplay(player);

                if (isHidden)
                {
                    response = $"称号已隐藏，重新输入命令可恢复显示（冷却时间：{CooldownSeconds}秒）";
                }
                else
                {
                    response = $"称号已显示，重新输入命令可隐藏（冷却时间：{CooldownSeconds}秒）";
                }

                Logger.Debug($"玩家 {player.Nickname} 切换称号显示状态: {(isHidden ? "隐藏" : "显示")}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"切换称号显示状态失败: {ex.Message}");
                response = $"操作失败: {ex.Message}";
                return false;
            }
        }

        /// <summary>
        /// 刷新玩家称号显示
        /// </summary>
        private void RefreshPlayerBadgeDisplay(Player player)
        {
            try
            {
                // 首先停止所有现有的称号协程
                StopBadgeCoroutines(player);

                // 获取可见的称号
                var (badge, color) = player.GetVisibleBadge();

                if (!string.IsNullOrEmpty(badge))
                {
                    // 检查称号是否有效（未过期）
                    if (player.TryGetBadge(out _, out _, out DateTime time) && time > DateTime.Now)
                    {
                        if (color != "colorful")
                        {
                            // 普通称号处理
                            if (!string.IsNullOrEmpty(badge))
                            {
                                if (badge.Contains(","))
                                {
                                    // 动态切换称号
                                    string[] badges = badge.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                                    Timing.RunCoroutine(BlackRoseServer.Helper.XHelper.BadgeHandle(player, badges).CancelWith(player.GameObject));
                                }
                                else
                                {
                                    // 单一称号
                                    player.GroupName = badge;
                                }
                            }
                            player.GroupColor = color;
                        }
                        else
                        {
                            // 彩色称号处理
                            if (badge.Contains(","))
                            {
                                // 彩色动态切换称号
                                string[] badges = badge.Split(new[] { "," }, StringSplitOptions.RemoveEmptyEntries);
                                Timing.RunCoroutine(BlackRoseServer.Helper.XHelper.BadgeHandle(player, badges).CancelWith(player.GameObject));
                            }
                            else
                            {
                                // 彩色单一称号
                                player.GroupName = badge;
                            }
                            // 启动彩色效果
                            Timing.RunCoroutine(BlackRoseServer.Helper.XHelper.RainbowBadge(player).CancelWith(player.GameObject));
                        }
                    }
                    else
                    {
                        // 称号已过期
                        player.GroupName = "";
                        player.GroupColor = "default";
                    }
                }
                else
                {
                    // 称号被隐藏或没有称号，清空显示
                    player.GroupName = "";
                    player.GroupColor = "default";
                }


            }
            catch (Exception ex)
            {
                Logger.Error($"刷新称号显示失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 停止玩家的所有称号协程
        /// </summary>
        private void StopBadgeCoroutines(Player player)
        {
            try
            {
                if (player?.GameObject == null) return;

                // 停止所有与该玩家GameObject关联的协程
                // 这会停止BadgeHandle和RainbowBadge协程
                Timing.KillCoroutines(player.GameObject);

                Logger.Debug($"已停止玩家 {player.Nickname} 的所有称号协程");
            }
            catch (Exception ex)
            {
                Logger.Error($"停止称号协程失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查玩家是否在冷却时间内
        /// </summary>
        /// <param name="userId">玩家ID</param>
        /// <param name="remainingTime">剩余冷却时间</param>
        /// <returns>是否在冷却中</returns>
        private bool IsPlayerOnCooldown(string userId, out TimeSpan remainingTime)
        {
            remainingTime = TimeSpan.Zero;

            if (PlayerCooldowns.TryGetValue(userId, out DateTime lastUseTime))
            {
                var elapsed = DateTime.Now - lastUseTime;
                var cooldownTime = TimeSpan.FromSeconds(CooldownSeconds);

                if (elapsed < cooldownTime)
                {
                    remainingTime = cooldownTime - elapsed;
                    return true;
                }
                else
                {
                    // 冷却时间已过，清理记录
                    PlayerCooldowns.TryRemove(userId, out _);
                }
            }

            return false;
        }

        /// <summary>
        /// 设置玩家冷却时间
        /// </summary>
        /// <param name="userId">玩家ID</param>
        private void SetPlayerCooldown(string userId)
        {
            PlayerCooldowns.AddOrUpdate(userId, DateTime.Now, (key, oldValue) => DateTime.Now);
        }

        /// <summary>
        /// 清理过期的冷却时间记录（可选的维护方法）
        /// </summary>
        public static void CleanupExpiredCooldowns()
        {
            var expiredKeys = new List<string>();
            var cutoffTime = DateTime.Now.AddSeconds(-CooldownSeconds);

            foreach (var kvp in PlayerCooldowns)
            {
                if (kvp.Value < cutoffTime)
                {
                    expiredKeys.Add(kvp.Key);
                }
            }

            foreach (var key in expiredKeys)
            {
                PlayerCooldowns.TryRemove(key, out _);
            }

            if (expiredKeys.Count > 0)
            {
                Logger.Debug($"清理了 {expiredKeys.Count} 个过期的称号切换冷却记录");
            }
        }

        /// <summary>
        /// 获取当前冷却状态统计
        /// </summary>
        /// <returns>冷却状态信息</returns>
        public static string GetCooldownStats()
        {
            var activeCount = 0;
            var expiredCount = 0;
            var cutoffTime = DateTime.Now.AddSeconds(-CooldownSeconds);

            foreach (var kvp in PlayerCooldowns)
            {
                if (kvp.Value >= cutoffTime)
                {
                    activeCount++;
                }
                else
                {
                    expiredCount++;
                }
            }

            return $"称号切换冷却状态 - 活跃: {activeCount}, 过期: {expiredCount}, 总计: {PlayerCooldowns.Count}";
        }
    }

    /// <summary>
    /// 管理员命令：查看称号切换冷却状态
    /// </summary>
    [CommandHandler(typeof(RemoteAdminCommandHandler))]
    public class BadgeCooldownStatsCommand : ICommand
    {
        public string Command => "badgecooldownstats";
        public string[] Aliases => new[] { "bcs", "badgestats" };
        public string Description => "查看称号切换冷却状态统计";

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            try
            {
                response = ToggleBadgeCommand.GetCooldownStats();
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取称号冷却统计失败: {ex.Message}");
                response = $"获取统计失败: {ex.Message}";
                return false;
            }
        }
    }

    /// <summary>
    /// 管理员命令：清理过期的称号切换冷却记录
    /// </summary>
    [CommandHandler(typeof(RemoteAdminCommandHandler))]
    public class CleanBadgeCooldownCommand : ICommand
    {
        public string Command => "cleanbadgecooldown";
        public string[] Aliases => new[] { "cbc", "cleanbadge" };
        public string Description => "清理过期的称号切换冷却记录";

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            try
            {
                var statsBefore = ToggleBadgeCommand.GetCooldownStats();
                ToggleBadgeCommand.CleanupExpiredCooldowns();
                var statsAfter = ToggleBadgeCommand.GetCooldownStats();

                response = $"清理完成\n清理前: {statsBefore}\n清理后: {statsAfter}";
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"清理称号冷却记录失败: {ex.Message}");
                response = $"清理失败: {ex.Message}";
                return false;
            }
        }
    }
}
