{"RootPath": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer", "ProjectFileName": "BlackRoseServer.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "API\\Features\\Pool\\IPool.cs"}, {"SourceFile": "API\\Features\\Pool\\StringBuilderPool.cs"}, {"SourceFile": "API\\LiteDBAPI.cs"}, {"SourceFile": "API\\DataModels.cs"}, {"SourceFile": "API\\LiteDBService.cs"}, {"SourceFile": "API\\ExperienceCalculator.cs"}, {"SourceFile": "API\\DatabaseMigrationTool.cs"}, {"SourceFile": "API\\DatabaseTest.cs"}, {"SourceFile": "Manager\\HintManager.cs"}, {"SourceFile": "Manager\\HintTemplate.cs"}, {"SourceFile": "Manager\\HintConfig.cs"}, {"SourceFile": "Manager\\HintLifecycleManager.cs"}, {"SourceFile": "Manager\\HintPositionManager.cs"}, {"SourceFile": "Manager\\HintExtensions.cs"}, {"SourceFile": "Manager\\HintMigrationHelper.cs"}, {"SourceFile": "Manager\\HintManagerTest.cs"}, {"SourceFile": "Display\\ZoneDetector.cs"}, {"SourceFile": "Display\\SimpleExperienceDisplay.cs"}, {"SourceFile": "Display\\SCPTeammateDisplay.cs"}, {"SourceFile": "Display\\SCP079InfoDisplay.cs"}, {"SourceFile": "Display\\RightBottomDisplayManager.cs"}, {"SourceFile": "Display\\DisplaySystemTest.cs"}, {"SourceFile": "Commands\\ChatCommand\\AcCommand.cs"}, {"SourceFile": "Commands\\ChatCommand\\BcCommand.cs"}, {"SourceFile": "Commands\\ChatCommand\\CCommand.cs"}, {"SourceFile": "Commands\\ScpCommand.cs"}, {"SourceFile": "Config.cs"}, {"SourceFile": "FakeConnection.cs"}, {"SourceFile": "Helper\\Chat\\ChatHelper.cs"}, {"SourceFile": "Helper\\Event\\EventHelper.cs"}, {"SourceFile": "Helper\\Music\\MusicHelper.cs"}, {"SourceFile": "Helper\\Players\\ExperienceHelper.cs"}, {"SourceFile": "Helper\\Players\\ExperienceSystemHelper.cs"}, {"SourceFile": "Helper\\Players\\RoundHelper.cs"}, {"SourceFile": "Helper\\Players\\SpectatorHelper.cs"}, {"SourceFile": "Helper\\SCP\\DisconnectSCPHelper.cs"}, {"SourceFile": "Helper\\TimerDisplayHelper.cs"}, {"SourceFile": "Helper\\SCP\\SCPHelper.cs"}, {"SourceFile": "Helper\\XHelper.cs"}, {"SourceFile": "Patches\\MaxHealthGetPatch.cs"}, {"SourceFile": "Patches\\PlayerDisconnectPatch.cs"}, {"SourceFile": "Patches\\SetNickPatch.cs"}, {"SourceFile": "Plugin.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.8.AssemblyAttributes.cs"}], "References": [{"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\Lib.Harmony.2.3.6\\lib\\net48\\0Harmony.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer\\bin\\Debug\\Assembly-CSharp-firstpass.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer\\bin\\Debug\\Assembly-CSharp_publicized.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\AudioApi.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\BouncyCastle.Cryptography.2.5.1\\lib\\net461\\BouncyCastle.Cryptography.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer\\bin\\Debug\\CommandSystem.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\Google.Protobuf.3.30.0\\lib\\net45\\Google.Protobuf.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer\\bin\\Debug\\HintServiceMeow-LabAPI.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\K4os.Compression.LZ4.1.3.8\\lib\\net462\\K4os.Compression.LZ4.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\K4os.Compression.LZ4.Streams.1.3.8\\lib\\net462\\K4os.Compression.LZ4.Streams.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\K4os.Hash.xxHash.1.0.8\\lib\\net462\\K4os.Hash.xxHash.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\Northwood.LabAPI.1.0.2\\lib\\net48\\LabApi.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\LiteDB.5.0.21\\lib\\net45\\LiteDB.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\Microsoft.Bcl.AsyncInterfaces.5.0.0\\lib\\net461\\Microsoft.Bcl.AsyncInterfaces.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer\\bin\\Debug\\Mirror.Components.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer\\bin\\Debug\\Mirror_publicized.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\Facades\\netstandard.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files\\IIS\\Microsoft Web Deploy V3\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer\\bin\\Debug\\NorthwoodLib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer\\bin\\Debug\\Pooling.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\SCPSLAudioApi.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\System.Buffers.4.5.1\\lib\\net461\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\System.Configuration.ConfigurationManager.8.0.0\\lib\\net462\\System.Configuration.ConfigurationManager.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Configuration.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\System.Diagnostics.DiagnosticSource.8.0.1\\lib\\net462\\System.Diagnostics.DiagnosticSource.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.IO.Compression.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\System.IO.Pipelines.5.0.2\\lib\\net461\\System.IO.Pipelines.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Management.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\System.Memory.4.5.5\\lib\\net461\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\System.Numerics.Vectors.4.5.0\\lib\\net46\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\System.Runtime.CompilerServices.Unsafe.6.0.0\\lib\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\System.Threading.Tasks.Extensions.4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.8\\System.Transactions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer\\bin\\Debug\\UnityEngine.CoreModule.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\YamlDotNet.11.0.1\\lib\\net45\\YamlDotNet.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "J:\\vsrepos\\Mahui_Source\\packages\\ZstdSharp.Port.0.8.5\\lib\\net462\\ZstdSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "J:\\vsrepos\\Mahui_Source\\BlackRoseServer\\bin\\Debug\\MahuiServer.dll", "OutputItemRelativePath": "MahuiServer.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}