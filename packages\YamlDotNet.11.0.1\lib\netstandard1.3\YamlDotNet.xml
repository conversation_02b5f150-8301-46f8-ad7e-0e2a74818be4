<?xml version="1.0"?>
<doc>
    <assembly>
        <name>YamlDotNet</name>
    </assembly>
    <members>
        <member name="T:YamlDotNet.Core.AnchorNotFoundException">
            <summary>
            The exception that is thrown when an alias references an anchor that does not exist.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.AnchorNotFoundException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.AnchorNotFoundException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:YamlDotNet.Core.AnchorNotFoundException.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.AnchorNotFoundException"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.AnchorNotFoundException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.AnchorNotFoundException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="inner">The inner.</param>
        </member>
        <member name="T:YamlDotNet.Core.Constants">
            <summary>
            Defines constants that relate to the YAML specification.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Emitter">
            <summary>
            Emits YAML streams.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.#ctor(System.IO.TextWriter)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Emitter"/> class.
            </summary>
            <param name="output">The <see cref="T:System.IO.TextWriter"/> where the emitter will write.</param>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.#ctor(System.IO.TextWriter,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Emitter"/> class.
            </summary>
            <param name="output">The <see cref="T:System.IO.TextWriter"/> where the emitter will write.</param>
            <param name="bestIndent">The preferred indentation.</param>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.#ctor(System.IO.TextWriter,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Emitter"/> class.
            </summary>
            <param name="output">The <see cref="T:System.IO.TextWriter"/> where the emitter will write.</param>
            <param name="bestIndent">The preferred indentation.</param>
            <param name="bestWidth">The preferred text width.</param>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.#ctor(System.IO.TextWriter,System.Int32,System.Int32,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Emitter"/> class.
            </summary>
            <param name="output">The <see cref="T:System.IO.TextWriter"/> where the emitter will write.</param>
            <param name="bestIndent">The preferred indentation.</param>
            <param name="bestWidth">The preferred text width.</param>
            <param name="isCanonical">If true, write the output in canonical form.</param>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.Emit(YamlDotNet.Core.Events.ParsingEvent)">
            <summary>
            Emit an evt.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.NeedMoreEvents">
            <summary>
            Check if we need to accumulate more events before emitting.
            
            We accumulate extra
             - 1 event for DOCUMENT-START
             - 2 events for SEQUENCE-START
             - 3 events for MAPPING-START
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitStreamStart(YamlDotNet.Core.Events.ParsingEvent)">
            <summary>
            Expect STREAM-START.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitDocumentStart(YamlDotNet.Core.Events.ParsingEvent,System.Boolean)">
            <summary>
            Expect DOCUMENT-START or STREAM-END.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitDocumentContent(YamlDotNet.Core.Events.ParsingEvent)">
            <summary>
            Expect the root node.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitNode(YamlDotNet.Core.Events.ParsingEvent,System.Boolean,System.Boolean)">
            <summary>
            Expect a node.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitAlias">
            <summary>
            Expect ALIAS.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitScalar(YamlDotNet.Core.Events.ParsingEvent)">
            <summary>
            Expect SCALAR.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitSequenceStart(YamlDotNet.Core.Events.ParsingEvent)">
            <summary>
            Expect SEQUENCE-START.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitMappingStart(YamlDotNet.Core.Events.ParsingEvent)">
            <summary>
            Expect MAPPING-START.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitDocumentEnd(YamlDotNet.Core.Events.ParsingEvent)">
            <summary>
            Expect DOCUMENT-END.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitFlowSequenceItem(YamlDotNet.Core.Events.ParsingEvent,System.Boolean)">
            <summary>
            Expect a flow item node.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitFlowMappingKey(YamlDotNet.Core.Events.ParsingEvent,System.Boolean)">
            <summary>
            Expect a flow key node.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitFlowMappingValue(YamlDotNet.Core.Events.ParsingEvent,System.Boolean)">
            <summary>
            Expect a flow value node.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitBlockSequenceItem(YamlDotNet.Core.Events.ParsingEvent,System.Boolean)">
            <summary>
            Expect a block item node.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitBlockMappingKey(YamlDotNet.Core.Events.ParsingEvent,System.Boolean)">
            <summary>
            Expect a block key node.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.EmitBlockMappingValue(YamlDotNet.Core.Events.ParsingEvent,System.Boolean)">
            <summary>
            Expect a block value node.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.CheckEmptyDocument">
            <summary>
            Check if the document content is an empty scalar.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Emitter.CheckSimpleKey">
            <summary>
            Check if the next node can be expressed as a simple key.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.EmitterSettings.BestIndent">
            <summary>
            The preferred indentation.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.EmitterSettings.BestWidth">
            <summary>
            The preferred text width.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.EmitterSettings.IsCanonical">
            <summary>
            If true, write the output in canonical form.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.EmitterSettings.SkipAnchorName">
            <summary>
            If true, write output without anchor names.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.EmitterSettings.MaxSimpleKeyLength">
            <summary>
            The maximum allowed length for simple keys.
            </summary>
            <remarks>
            The specifiction mandates 1024 characters, but any desired value may be used.
            </remarks>
        </member>
        <member name="P:YamlDotNet.Core.EmitterSettings.IndentSequences">
            <summary>
            Indent sequences. The default is to not indent.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Events.AnchorAlias">
            <summary>
            Represents an alias event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.AnchorAlias.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.AnchorAlias.Value">
            <summary>
            Gets the value of the alias.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.AnchorAlias.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.AnchorAlias"/> class.
            </summary>
            <param name="value">The value of the alias.</param>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.AnchorAlias.#ctor(YamlDotNet.Core.AnchorName)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.AnchorAlias"/> class.
            </summary>
            <param name="value">The value of the alias.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.AnchorAlias.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Events.AnchorAlias.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Invokes run-time type specific Visit() method of the specified visitor.
            </summary>
            <param name="visitor">visitor, may not be null.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.Comment.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="T:YamlDotNet.Core.Events.DocumentEnd">
            <summary>
            Represents a document end event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.DocumentEnd.NestingIncrease">
            <summary>
            Gets a value indicating the variation of depth caused by this event.
            The value can be either -1, 0 or 1. For start events, it will be 1,
            for end events, it will be -1, and for the remaining events, it will be 0.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.DocumentEnd.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.DocumentEnd.IsImplicit">
            <summary>
            Gets a value indicating whether this instance is implicit.
            </summary>
            <value>
                <c>true</c> if this instance is implicit; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:YamlDotNet.Core.Events.DocumentEnd.#ctor(System.Boolean,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.DocumentEnd"/> class.
            </summary>
            <param name="isImplicit">Indicates whether the event is implicit.</param>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.DocumentEnd.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.DocumentEnd"/> class.
            </summary>
            <param name="isImplicit">Indicates whether the event is implicit.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.DocumentEnd.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Events.DocumentEnd.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Invokes run-time type specific Visit() method of the specified visitor.
            </summary>
            <param name="visitor">visitor, may not be null.</param>
        </member>
        <member name="T:YamlDotNet.Core.Events.DocumentStart">
            <summary>
            Represents a document start event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.DocumentStart.NestingIncrease">
            <summary>
            Gets a value indicating the variation of depth caused by this event.
            The value can be either -1, 0 or 1. For start events, it will be 1,
            for end events, it will be -1, and for the remaining events, it will be 0.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.DocumentStart.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.DocumentStart.Tags">
            <summary>
            Gets the tags.
            </summary>
            <value>The tags.</value>
        </member>
        <member name="P:YamlDotNet.Core.Events.DocumentStart.Version">
            <summary>
            Gets the version.
            </summary>
            <value>The version.</value>
        </member>
        <member name="P:YamlDotNet.Core.Events.DocumentStart.IsImplicit">
            <summary>
            Gets a value indicating whether this instance is implicit.
            </summary>
            <value>
                <c>true</c> if this instance is implicit; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:YamlDotNet.Core.Events.DocumentStart.#ctor(YamlDotNet.Core.Tokens.VersionDirective,YamlDotNet.Core.TagDirectiveCollection,System.Boolean,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.DocumentStart"/> class.
            </summary>
            <param name="version">The version.</param>
            <param name="tags">The tags.</param>
            <param name="isImplicit">Indicates whether the event is implicit.</param>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.DocumentStart.#ctor(YamlDotNet.Core.Tokens.VersionDirective,YamlDotNet.Core.TagDirectiveCollection,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.DocumentStart"/> class.
            </summary>
            <param name="version">The version.</param>
            <param name="tags">The tags.</param>
            <param name="isImplicit">Indicates whether the event is implicit.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.DocumentStart.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.DocumentStart"/> class.
            </summary>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.DocumentStart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.DocumentStart"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.DocumentStart.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Events.DocumentStart.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Invokes run-time type specific Visit() method of the specified visitor.
            </summary>
            <param name="visitor">visitor, may not be null.</param>
        </member>
        <member name="T:YamlDotNet.Core.Events.IParsingEventVisitor">
            <summary>
            Callback interface for external event Visitor.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Events.MappingEnd">
            <summary>
            Represents a mapping end event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.MappingEnd.NestingIncrease">
            <summary>
            Gets a value indicating the variation of depth caused by this event.
            The value can be either -1, 0 or 1. For start events, it will be 1,
            for end events, it will be -1, and for the remaining events, it will be 0.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.MappingEnd.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.MappingEnd.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.MappingEnd"/> class.
            </summary>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.MappingEnd.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.MappingEnd"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.MappingEnd.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Events.MappingEnd.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Invokes run-time type specific Visit() method of the specified visitor.
            </summary>
            <param name="visitor">visitor, may not be null.</param>
        </member>
        <member name="T:YamlDotNet.Core.Events.MappingStart">
            <summary>
            Represents a mapping start event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.MappingStart.NestingIncrease">
            <summary>
            Gets a value indicating the variation of depth caused by this event.
            The value can be either -1, 0 or 1. For start events, it will be 1,
            for end events, it will be -1, and for the remaining events, it will be 0.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.MappingStart.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.MappingStart.IsImplicit">
            <summary>
            Gets a value indicating whether this instance is implicit.
            </summary>
            <value>
                <c>true</c> if this instance is implicit; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:YamlDotNet.Core.Events.MappingStart.IsCanonical">
            <summary>
            Gets a value indicating whether this instance is canonical.
            </summary>
            <value></value>
        </member>
        <member name="P:YamlDotNet.Core.Events.MappingStart.Style">
            <summary>
            Gets the style of the mapping.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.MappingStart.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.TagName,System.Boolean,YamlDotNet.Core.Events.MappingStyle,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.MappingStart"/> class.
            </summary>
            <param name="anchor">The anchor.</param>
            <param name="tag">The tag.</param>
            <param name="isImplicit">Indicates whether the event is implicit.</param>
            <param name="style">The style of the mapping.</param>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.MappingStart.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.TagName,System.Boolean,YamlDotNet.Core.Events.MappingStyle)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.MappingStart"/> class.
            </summary>
            <param name="anchor">The anchor.</param>
            <param name="tag">The tag.</param>
            <param name="isImplicit">Indicates whether the event is implicit.</param>
            <param name="style">The style of the mapping.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.MappingStart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.MappingStart"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.MappingStart.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Events.MappingStart.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Invokes run-time type specific Visit() method of the specified visitor.
            </summary>
            <param name="visitor">visitor, may not be null.</param>
        </member>
        <member name="T:YamlDotNet.Core.Events.MappingStyle">
            <summary>
            Specifies the style of a mapping.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.Events.MappingStyle.Any">
            <summary>
            Let the emitter choose the style.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.Events.MappingStyle.Block">
            <summary>
            The block mapping style.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.Events.MappingStyle.Flow">
            <summary>
            The flow mapping style.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Events.NodeEvent">
            <summary>
            Contains the behavior that is common between node events.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.NodeEvent.Anchor">
            <summary>
            Gets the anchor.
            </summary>
            <value></value>
        </member>
        <member name="P:YamlDotNet.Core.Events.NodeEvent.Tag">
            <summary>
            Gets the tag.
            </summary>
            <value></value>
        </member>
        <member name="P:YamlDotNet.Core.Events.NodeEvent.IsCanonical">
            <summary>
            Gets a value indicating whether this instance is canonical.
            </summary>
            <value></value>
        </member>
        <member name="M:YamlDotNet.Core.Events.NodeEvent.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.TagName,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.NodeEvent"/> class.
            </summary>
            <param name="anchor">The anchor.</param>
            <param name="tag">The tag.</param>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.NodeEvent.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.TagName)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.NodeEvent"/> class.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Events.ParsingEvent">
            <summary>
            Base class for parsing events.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.ParsingEvent.NestingIncrease">
            <summary>
            Gets a value indicating the variation of depth caused by this event.
            The value can be either -1, 0 or 1. For start events, it will be 1,
            for end events, it will be -1, and for the remaining events, it will be 0.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.ParsingEvent.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.ParsingEvent.Start">
            <summary>
            Gets the position in the input stream where the event starts.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.ParsingEvent.End">
            <summary>
            Gets the position in the input stream where the event ends.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.ParsingEvent.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Accepts the specified visitor.
            </summary>
            <param name="visitor">Visitor to accept, may not be null</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.ParsingEvent.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.ParsingEvent"/> class.
            </summary>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="T:YamlDotNet.Core.Events.Scalar">
            <summary>
            Represents a scalar event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.Scalar.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.Scalar.Value">
            <summary>
            Gets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:YamlDotNet.Core.Events.Scalar.Style">
            <summary>
            Gets the style of the scalar.
            </summary>
            <value>The style.</value>
        </member>
        <member name="P:YamlDotNet.Core.Events.Scalar.IsPlainImplicit">
            <summary>
            Gets a value indicating whether the tag is optional for the plain style.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.Scalar.IsQuotedImplicit">
            <summary>
            Gets a value indicating whether the tag is optional for any non-plain style.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.Scalar.IsCanonical">
            <summary>
            Gets a value indicating whether this instance is canonical.
            </summary>
            <value></value>
        </member>
        <member name="M:YamlDotNet.Core.Events.Scalar.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.TagName,System.String,YamlDotNet.Core.ScalarStyle,System.Boolean,System.Boolean,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.Scalar"/> class.
            </summary>
            <param name="anchor">The anchor.</param>
            <param name="tag">The tag.</param>
            <param name="value">The value.</param>
            <param name="style">The style.</param>
            <param name="isPlainImplicit">.</param>
            <param name="isQuotedImplicit">.</param>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.Scalar.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.TagName,System.String,YamlDotNet.Core.ScalarStyle,System.Boolean,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.Scalar"/> class.
            </summary>
            <param name="anchor">The anchor.</param>
            <param name="tag">The tag.</param>
            <param name="value">The value.</param>
            <param name="style">The style.</param>
            <param name="isPlainImplicit">.</param>
            <param name="isQuotedImplicit">.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.Scalar.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.Scalar"/> class.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.Scalar.#ctor(YamlDotNet.Core.TagName,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.Scalar"/> class.
            </summary>
            <param name="tag">The tag.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.Scalar.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.TagName,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.Scalar"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.Scalar.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Events.Scalar.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Invokes run-time type specific Visit() method of the specified visitor.
            </summary>
            <param name="visitor">visitor, may not be null.</param>
        </member>
        <member name="T:YamlDotNet.Core.Events.SequenceEnd">
            <summary>
            Represents a sequence end event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.SequenceEnd.NestingIncrease">
            <summary>
            Gets a value indicating the variation of depth caused by this event.
            The value can be either -1, 0 or 1. For start events, it will be 1,
            for end events, it will be -1, and for the remaining events, it will be 0.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.SequenceEnd.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.SequenceEnd.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.SequenceEnd"/> class.
            </summary>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.SequenceEnd.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.SequenceEnd"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.SequenceEnd.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Events.SequenceEnd.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Invokes run-time type specific Visit() method of the specified visitor.
            </summary>
            <param name="visitor">visitor, may not be null.</param>
        </member>
        <member name="T:YamlDotNet.Core.Events.SequenceStart">
            <summary>
            Represents a sequence start event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.SequenceStart.NestingIncrease">
            <summary>
            Gets a value indicating the variation of depth caused by this event.
            The value can be either -1, 0 or 1. For start events, it will be 1,
            for end events, it will be -1, and for the remaining events, it will be 0.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.SequenceStart.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.SequenceStart.IsImplicit">
            <summary>
            Gets a value indicating whether this instance is implicit.
            </summary>
            <value>
                <c>true</c> if this instance is implicit; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:YamlDotNet.Core.Events.SequenceStart.IsCanonical">
            <summary>
            Gets a value indicating whether this instance is canonical.
            </summary>
            <value></value>
        </member>
        <member name="P:YamlDotNet.Core.Events.SequenceStart.Style">
            <summary>
            Gets the style.
            </summary>
            <value>The style.</value>
        </member>
        <member name="M:YamlDotNet.Core.Events.SequenceStart.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.TagName,System.Boolean,YamlDotNet.Core.Events.SequenceStyle,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.SequenceStart"/> class.
            </summary>
            <param name="anchor">The anchor.</param>
            <param name="tag">The tag.</param>
            <param name="isImplicit">if set to <c>true</c> [is implicit].</param>
            <param name="style">The style.</param>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.SequenceStart.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.TagName,System.Boolean,YamlDotNet.Core.Events.SequenceStyle)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.SequenceStart"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.SequenceStart.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Events.SequenceStart.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Invokes run-time type specific Visit() method of the specified visitor.
            </summary>
            <param name="visitor">visitor, may not be null.</param>
        </member>
        <member name="T:YamlDotNet.Core.Events.SequenceStyle">
            <summary>
            Specifies the style of a sequence.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.Events.SequenceStyle.Any">
            <summary>
            Let the emitter choose the style.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.Events.SequenceStyle.Block">
            <summary>
            The block sequence style.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.Events.SequenceStyle.Flow">
            <summary>
            The flow sequence style.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Events.StreamEnd">
            <summary>
            Represents a stream end event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.StreamEnd.NestingIncrease">
            <summary>
            Gets a value indicating the variation of depth caused by this event.
            The value can be either -1, 0 or 1. For start events, it will be 1,
            for end events, it will be -1, and for the remaining events, it will be 0.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.StreamEnd.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.StreamEnd.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.StreamEnd"/> class.
            </summary>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.StreamEnd.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.StreamEnd"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.StreamEnd.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Events.StreamEnd.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Invokes run-time type specific Visit() method of the specified visitor.
            </summary>
            <param name="visitor">visitor, may not be null.</param>
        </member>
        <member name="T:YamlDotNet.Core.Events.StreamStart">
            <summary>
            Represents a stream start event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.StreamStart.NestingIncrease">
            <summary>
            Gets a value indicating the variation of depth caused by this event.
            The value can be either -1, 0 or 1. For start events, it will be 1,
            for end events, it will be -1, and for the remaining events, it will be 0.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Events.StreamStart.Type">
            <summary>
            Gets the event type, which allows for simpler type comparisons.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.StreamStart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.StreamStart"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Events.StreamStart.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Events.StreamStart"/> class.
            </summary>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="M:YamlDotNet.Core.Events.StreamStart.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Events.StreamStart.Accept(YamlDotNet.Core.Events.IParsingEventVisitor)">
            <summary>
            Invokes run-time type specific Visit() method of the specified visitor.
            </summary>
            <param name="visitor">visitor, may not be null.</param>
        </member>
        <member name="T:YamlDotNet.Core.ForwardAnchorNotSupportedException">
            <summary>
            The exception that is thrown when an alias references an anchor
            that has not yet been defined in a context that does not support forward references.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.ForwardAnchorNotSupportedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.AnchorNotFoundException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:YamlDotNet.Core.ForwardAnchorNotSupportedException.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.AnchorNotFoundException"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.ForwardAnchorNotSupportedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.AnchorNotFoundException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="inner">The inner.</param>
        </member>
        <member name="T:YamlDotNet.Core.HashCode">
            <summary>
            Supports implementations of <see cref="M:System.Object.GetHashCode"/> by providing methods to combine two hash codes.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.HashCode.CombineHashCodes(System.Int32,System.Int32)">
            <summary>
            Combines two hash codes.
            </summary>
            <param name="h1">The first hash code.</param>
            <param name="h2">The second hash code.</param>
            <returns></returns>
        </member>
        <member name="T:YamlDotNet.Core.IEmitter">
            <summary>
            Represents a YAML stream emitter.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.IEmitter.Emit(YamlDotNet.Core.Events.ParsingEvent)">
            <summary>
            Emits an event.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.ILookAheadBuffer.EndOfInput">
            <summary>
            Gets a value indicating whether the end of the input reader has been reached.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.ILookAheadBuffer.Peek(System.Int32)">
            <summary>
            Gets the character at the specified offset.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.ILookAheadBuffer.Skip(System.Int32)">
            <summary>
            Skips the next <paramref name="length"/> characters. Those characters must have been
            obtained first by calling the <see cref="M:YamlDotNet.Core.ILookAheadBuffer.Peek(System.Int32)"/> method.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.InsertionQueue`1">
            <summary>
            Generic queue on which items may be inserted
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.InsertionQueue`1.Count">
            <summary>
            Gets the number of items that are contained by the queue.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.InsertionQueue`1.Enqueue(`0)">
            <summary>
            Enqueues the specified item.
            </summary>
            <param name="item">The item to be enqueued.</param>
        </member>
        <member name="M:YamlDotNet.Core.InsertionQueue`1.Dequeue">
            <summary>
            Dequeues an item.
            </summary>
            <returns>Returns the item that been dequeued.</returns>
        </member>
        <member name="M:YamlDotNet.Core.InsertionQueue`1.Insert(System.Int32,`0)">
            <summary>
            Inserts an item at the specified index.
            </summary>
            <param name="index">The index where to insert the item.</param>
            <param name="item">The item to be inserted.</param>
        </member>
        <member name="T:YamlDotNet.Core.IParser">
            <summary>
            Represents a YAML stream parser.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.IParser.Current">
            <summary>
            Gets the current event. Returns null before the first call to <see cref="M:YamlDotNet.Core.IParser.MoveNext" />,
            and also after <see cref="M:YamlDotNet.Core.IParser.MoveNext" /> returns false.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.IParser.MoveNext">
            <summary>
            Moves to the next event.
            </summary>
            <returns>Returns true if there are more events available, otherwise returns false.</returns>
        </member>
        <member name="T:YamlDotNet.Core.IScanner">
            <summary>
            Defines the interface for a stand-alone YAML scanner that
            converts a sequence of characters into a sequence of YAML tokens.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.IScanner.CurrentPosition">
            <summary>
            Gets the current position inside the input stream.
            </summary>
            <value>The current position.</value>
        </member>
        <member name="P:YamlDotNet.Core.IScanner.Current">
            <summary>
            Gets the current token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.IScanner.MoveNext">
            <summary>
            Moves to the next token and consumes the current token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.IScanner.MoveNextWithoutConsuming">
            <summary>
            Moves to the next token without consuming the current token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.IScanner.ConsumeCurrent">
            <summary>
            Consumes the current token.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.LookAheadBuffer">
            <summary>
            Provides access to a stream and allows to peek at the next characters,
            up to the buffer's capacity.
            </summary>
            <remarks>
            This class implements a circular buffer with a fixed capacity.
            </remarks>
        </member>
        <member name="M:YamlDotNet.Core.LookAheadBuffer.#ctor(System.IO.TextReader,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.LookAheadBuffer"/> class.
            </summary>
            <param name="input">The input.</param>
            <param name="capacity">The capacity.</param>
        </member>
        <member name="P:YamlDotNet.Core.LookAheadBuffer.EndOfInput">
            <summary>
            Gets a value indicating whether the end of the input reader has been reached.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.LookAheadBuffer.GetIndexForOffset(System.Int32)">
            <summary>
            Gets the index of the character for the specified offset.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.LookAheadBuffer.Peek(System.Int32)">
            <summary>
            Gets the character at the specified offset.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.LookAheadBuffer.Cache(System.Int32)">
            <summary>
            Reads characters until at least <paramref name="length"/> characters are in the buffer.
            </summary>
            <param name="length">
            Number of characters to cache.
            </param>
        </member>
        <member name="M:YamlDotNet.Core.LookAheadBuffer.Skip(System.Int32)">
            <summary>
            Skips the next <paramref name="length"/> characters. Those characters must have been
            obtained first by calling the <see cref="M:YamlDotNet.Core.LookAheadBuffer.Peek(System.Int32)"/> or <see cref="M:YamlDotNet.Core.LookAheadBuffer.Cache(System.Int32)"/> methods.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Mark">
            <summary>
            Represents a location inside a file
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.Mark.Empty">
            <summary>
            Gets a <see cref="T:YamlDotNet.Core.Mark"/> with empty values.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Mark.Index">
            <summary>
            Gets / sets the absolute offset in the file
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Mark.Line">
            <summary>
            Gets / sets the number of the line
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Mark.Column">
            <summary>
            Gets / sets the index of the column
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Mark.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Mark.Equals(System.Object)">
            <summary />
        </member>
        <member name="M:YamlDotNet.Core.Mark.Equals(YamlDotNet.Core.Mark)">
            <summary />
        </member>
        <member name="M:YamlDotNet.Core.Mark.GetHashCode">
            <summary />
        </member>
        <member name="M:YamlDotNet.Core.Mark.CompareTo(System.Object)">
            <summary />
        </member>
        <member name="M:YamlDotNet.Core.Mark.CompareTo(YamlDotNet.Core.Mark)">
            <summary />
        </member>
        <member name="T:YamlDotNet.Core.MaximumRecursionLevelReachedException">
            <summary>
            Exception that is thrown when an infinite recursion is detected.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.MaximumRecursionLevelReachedException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:YamlDotNet.Core.MaximumRecursionLevelReachedException.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.MaximumRecursionLevelReachedException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="inner">The inner.</param>
        </member>
        <member name="T:YamlDotNet.Core.MergingParser">
            <summary>
            Simple implementation of <see cref="T:YamlDotNet.Core.IParser"/> that implements merging: http://yaml.org/type/merge.html
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Parser">
            <summary>
            Parses YAML streams.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.#ctor(System.IO.TextReader)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Parser"/> class.
            </summary>
            <param name="input">The input where the YAML stream is to be read.</param>
        </member>
        <member name="M:YamlDotNet.Core.Parser.#ctor(YamlDotNet.Core.IScanner)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Parser"/> class.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Parser.Current">
            <summary>
            Gets the current event.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.MoveNext">
            <summary>
            Moves to the next event.
            </summary>
            <returns>Returns true if there are more events available, otherwise returns false.</returns>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseStreamStart">
            <summary>
            Parse the production:
            stream   ::= STREAM-START implicit_document? explicit_document* STREAM-END
                         ************
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseDocumentStart(System.Boolean)">
            <summary>
            Parse the productions:
            implicit_document    ::= block_node DOCUMENT-END*
                                     *
            explicit_document    ::= DIRECTIVE* DOCUMENT-START block_node? DOCUMENT-END*
                                     *************************
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ProcessDirectives(YamlDotNet.Core.TagDirectiveCollection)">
            <summary>
            Parse directives.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseDocumentContent">
            <summary>
            Parse the productions:
            explicit_document    ::= DIRECTIVE* DOCUMENT-START block_node? DOCUMENT-END*
                                                               ***********
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ProcessEmptyScalar(YamlDotNet.Core.Mark)">
            <summary>
            Generate an empty scalar event.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseNode(System.Boolean,System.Boolean)">
            <summary>
            Parse the productions:
            block_node_or_indentless_sequence    ::=
                                     ALIAS
                                     *****
                                     | properties (block_content | indentless_block_sequence)?
                                       **********  *
                                     | block_content | indentless_block_sequence
                                       *
            block_node           ::= ALIAS
                                     *****
                                     | properties block_content?
                                       ********** *
                                     | block_content
                                       *
            flow_node            ::= ALIAS
                                     *****
                                     | properties flow_content?
                                       ********** *
                                     | flow_content
                                       *
            properties           ::= TAG ANCHOR? | ANCHOR TAG?
                                     *************************
            block_content        ::= block_collection | flow_collection | SCALAR
                                                                          ******
            flow_content         ::= flow_collection | SCALAR
                                                       ******
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseDocumentEnd">
            <summary>
            Parse the productions:
            implicit_document    ::= block_node DOCUMENT-END*
                                                *************
            explicit_document    ::= DIRECTIVE* DOCUMENT-START block_node? DOCUMENT-END*
                                                                           *************
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseBlockSequenceEntry(System.Boolean)">
            <summary>
            Parse the productions:
            block_sequence ::= BLOCK-SEQUENCE-START (BLOCK-ENTRY block_node?)* BLOCK-END
                               ********************  *********** *             *********
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseIndentlessSequenceEntry">
            <summary>
            Parse the productions:
            indentless_sequence  ::= (BLOCK-ENTRY block_node?)+
                                      *********** *
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseBlockMappingKey(System.Boolean)">
             <summary>
             Parse the productions:
             block_mapping        ::= BLOCK-MAPPING_START
                                      *******************
                                      ((KEY block_node_or_indentless_sequence?)?
                                        *** *
                                      (VALUE block_node_or_indentless_sequence?)?)*
            
                                      BLOCK-END
                                      *********
             </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseBlockMappingValue">
             <summary>
             Parse the productions:
             block_mapping        ::= BLOCK-MAPPING_START
            
                                      ((KEY block_node_or_indentless_sequence?)?
            
                                      (VALUE block_node_or_indentless_sequence?)?)*
                                       ***** *
                                      BLOCK-END
            
             </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseFlowSequenceEntry(System.Boolean)">
            <summary>
            Parse the productions:
            flow_sequence        ::= FLOW-SEQUENCE-START
                                     *******************
                                     (flow_sequence_entry FLOW-ENTRY)*
                                      *                   **********
                                     flow_sequence_entry?
                                     *
                                     FLOW-SEQUENCE-END
                                     *****************
            flow_sequence_entry  ::= flow_node | KEY flow_node? (VALUE flow_node?)?
                                     *
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseFlowSequenceEntryMappingKey">
            <summary>
            Parse the productions:
            flow_sequence_entry  ::= flow_node | KEY flow_node? (VALUE flow_node?)?
                                                 *** *
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseFlowSequenceEntryMappingValue">
            <summary>
            Parse the productions:
            flow_sequence_entry  ::= flow_node | KEY flow_node? (VALUE flow_node?)?
                                                                 ***** *
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseFlowSequenceEntryMappingEnd">
            <summary>
            Parse the productions:
            flow_sequence_entry  ::= flow_node | KEY flow_node? (VALUE flow_node?)?
                                                                                 *
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseFlowMappingKey(System.Boolean)">
            <summary>
            Parse the productions:
            flow_mapping         ::= FLOW-MAPPING-START
                                     ******************
                                     (flow_mapping_entry FLOW-ENTRY)*
                                      *                  **********
                                     flow_mapping_entry?
                                     ******************
                                     FLOW-MAPPING-END
                                     ****************
            flow_mapping_entry   ::= flow_node | KEY flow_node? (VALUE flow_node?)?
                                     *           *** *
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Parser.ParseFlowMappingValue(System.Boolean)">
            <summary>
            Parse the productions:
            flow_mapping_entry   ::= flow_node | KEY flow_node? (VALUE flow_node?)?
                                              *                  ***** *
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.ParserExtensions">
            <summary>
            Extension methods that provide useful abstractions over <see cref="T:YamlDotNet.Core.IParser"/>.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.ParserExtensions.Consume``1(YamlDotNet.Core.IParser)">
            <summary>
            Ensures that the current event is of the specified type, returns it and moves to the next event.
            </summary>
            <typeparam name="T">Type of the <see cref="T:YamlDotNet.Core.Events.ParsingEvent"/>.</typeparam>
            <returns>Returns the current event.</returns>
            <exception cref="T:YamlDotNet.Core.YamlException">If the current event is not of the specified type.</exception>
        </member>
        <member name="M:YamlDotNet.Core.ParserExtensions.TryConsume``1(YamlDotNet.Core.IParser,``0@)">
            <summary>
            Checks whether the current event is of the specified type.
            If the event is of the specified type, returns it and moves to the next event.
            Otherwise returns null.
            </summary>
            <typeparam name="T">Type of the <see cref="T:YamlDotNet.Core.Events.ParsingEvent"/>.</typeparam>
            <returns>Returns true if the current event is of type T; otherwise returns null.</returns>
        </member>
        <member name="M:YamlDotNet.Core.ParserExtensions.Require``1(YamlDotNet.Core.IParser)">
            <summary>
            Enforces that the current event is of the specified type.
            </summary>
            <typeparam name="T">Type of the <see cref="T:YamlDotNet.Core.Events.ParsingEvent"/>.</typeparam>
            <returns>Returns the current event.</returns>
            <exception cref="T:YamlDotNet.Core.YamlException">If the current event is not of the specified type.</exception>
        </member>
        <member name="M:YamlDotNet.Core.ParserExtensions.Accept``1(YamlDotNet.Core.IParser,``0@)">
            <summary>
            Checks whether the current event is of the specified type.
            </summary>
            <typeparam name="T">Type of the event.</typeparam>
            <returns>Returns true if the current event is of type <typeparamref name="T"/>. Otherwise returns false.</returns>
        </member>
        <member name="M:YamlDotNet.Core.ParserExtensions.SkipThisAndNestedEvents(YamlDotNet.Core.IParser)">
            <summary>
            Skips the current event and any nested event.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.RecursionLevel">
            <summary>
            Keeps track of the <see cref="F:YamlDotNet.Core.RecursionLevel.current"/> recursion level,
            and throws <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/>
            whenever <see cref="P:YamlDotNet.Core.RecursionLevel.Maximum"/> is reached.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.RecursionLevel.Increment">
            <summary>
            Increments the <see cref="F:YamlDotNet.Core.RecursionLevel.current"/> recursion level,
            and throws <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/>
            if <see cref="P:YamlDotNet.Core.RecursionLevel.Maximum"/> is reached.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.RecursionLevel.TryIncrement">
            <summary>
            Increments the <see cref="F:YamlDotNet.Core.RecursionLevel.current"/> recursion level,
            and returns whether <see cref="F:YamlDotNet.Core.RecursionLevel.current"/> is still less than <see cref="P:YamlDotNet.Core.RecursionLevel.Maximum"/>.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.RecursionLevel.Decrement">
            <summary>
            Decrements the <see cref="F:YamlDotNet.Core.RecursionLevel.current"/> recursion level.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.ScalarStyle">
            <summary>
            Specifies the style of a YAML scalar.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.ScalarStyle.Any">
            <summary>
            Let the emitter choose the style.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.ScalarStyle.Plain">
            <summary>
            The plain scalar style.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.ScalarStyle.SingleQuoted">
            <summary>
            The single-quoted scalar style.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.ScalarStyle.DoubleQuoted">
            <summary>
            The double-quoted scalar style.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.ScalarStyle.Literal">
            <summary>
            The literal scalar style.
            </summary>
        </member>
        <member name="F:YamlDotNet.Core.ScalarStyle.Folded">
            <summary>
            The folded scalar style.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Scanner">
            <summary>
            Converts a sequence of characters into a sequence of YAML tokens.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Scanner.Current">
            <summary>
            Gets the current token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.#ctor(System.IO.TextReader,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Scanner"/> class.
            </summary>
            <param name="input">The input.</param>
            <param name="skipComments">Indicates whether comments should be ignored</param>
        </member>
        <member name="P:YamlDotNet.Core.Scanner.CurrentPosition">
            <summary>
            Gets the current position inside the input stream.
            </summary>
            <value>The current position.</value>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.MoveNext">
            <summary>
            Moves to the next token.
            </summary>
            <returns></returns>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ConsumeCurrent">
            <summary>
            Consumes the current token and increments the parsed token count
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.StaleSimpleKeys">
            <summary>
            Check the list of potential simple keys and remove the positions that
            cannot contain simple keys anymore.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.UnrollIndent(System.Int32)">
            <summary>
            Pop indentation levels from the indents stack until the current level
            becomes less or equal to the column.  For each indentation level, append
            the BLOCK-END token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchStreamEnd">
            <summary>
            Produce the STREAM-END token and shut down the scanner.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanDirective">
             <summary>
             Scan a YAML-DIRECTIVE or TAG-DIRECTIVE token.
            
             Scope:
                  %YAML    1.1    # a comment \n
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                  %TAG    !yaml!  tag:yaml.org,2002:  \n
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
             </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchDocumentIndicator(System.Boolean)">
            <summary>
            Produce the DOCUMENT-START or DOCUMENT-END token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchFlowCollectionStart(System.Boolean)">
            <summary>
            Produce the FLOW-SEQUENCE-START or FLOW-MAPPING-START token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.IncreaseFlowLevel">
            <summary>
            Increase the flow level and resize the simple key list if needed.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchFlowCollectionEnd(System.Boolean)">
            <summary>
            Produce the FLOW-SEQUENCE-END or FLOW-MAPPING-END token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.DecreaseFlowLevel">
            <summary>
            Decrease the flow level.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchFlowEntry">
            <summary>
            Produce the FLOW-ENTRY token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchBlockEntry">
            <summary>
            Produce the BLOCK-ENTRY token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchKey">
            <summary>
            Produce the KEY token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchValue">
            <summary>
            Produce the VALUE token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.RollIndent(System.Int32,System.Int32,System.Boolean,YamlDotNet.Core.Mark)">
            <summary>
            Push the current indentation level to the stack and set the new level
            the current column is greater than the indentation level.  In this case,
            append or insert the specified token into the token queue.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchAnchor(System.Boolean)">
            <summary>
            Produce the ALIAS or ANCHOR token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchTag">
            <summary>
            Produce the TAG token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanTag">
            <summary>
            Scan a TAG token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchBlockScalar(System.Boolean)">
            <summary>
            Produce the SCALAR(...,literal) or SCALAR(...,folded) tokens.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanBlockScalar(System.Boolean)">
            <summary>
            Scan a block scalar.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanBlockScalarBreaks(System.Int32,System.Text.StringBuilder,System.Boolean,YamlDotNet.Core.Mark@,System.Nullable{System.Boolean}@)">
            <summary>
            Scan indentation spaces and line breaks for a block scalar.  Determine the
            indentation level if needed.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchFlowScalar(System.Boolean)">
            <summary>
            Produce the SCALAR(...,single-quoted) or SCALAR(...,double-quoted) tokens.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanFlowScalar(System.Boolean)">
            <summary>
            Scan a quoted scalar.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.FetchPlainScalar">
            <summary>
            Produce the SCALAR(...,plain) token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanPlainScalar(System.Boolean@)">
            <summary>
            Scan a plain scalar.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.RemoveSimpleKey">
            <summary>
            Remove a potential simple key at the current flow level.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanDirectiveName(YamlDotNet.Core.Mark)">
             <summary>
             Scan the directive name.
            
             Scope:
                  %YAML   1.1     # a comment \n
                   ^^^^
                  %TAG    !yaml!  tag:yaml.org,2002:  \n
                   ^^^
             </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanVersionDirectiveValue(YamlDotNet.Core.Mark)">
             <summary>
             Scan the value of VERSION-DIRECTIVE.
            
             Scope:
                  %YAML   1.1     # a comment \n
                       ^^^^^^
             </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanTagDirectiveValue(YamlDotNet.Core.Mark)">
             <summary>
             Scan the value of a TAG-DIRECTIVE token.
            
             Scope:
                  %TAG    !yaml!  tag:yaml.org,2002:  \n
                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
             </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanTagUri(System.String,YamlDotNet.Core.Mark)">
            <summary>
            Scan a tag.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanUriEscapes(YamlDotNet.Core.Mark)">
            <summary>
            Decode an URI-escape sequence corresponding to a single UTF-8 character.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanTagHandle(System.Boolean,YamlDotNet.Core.Mark)">
            <summary>
            Scan a tag handle.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.ScanVersionDirectiveNumber(YamlDotNet.Core.Mark)">
             <summary>
             Scan the version number of VERSION-DIRECTIVE.
            
             Scope:
                  %YAML   1.1     # a comment \n
                          ^
                  %YAML   1.1     # a comment \n
                            ^
             </summary>
        </member>
        <member name="M:YamlDotNet.Core.Scanner.SaveSimpleKey">
            <summary>
            Check if a simple key may start at the current position and add it if
            needed.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.SemanticErrorException">
            <summary>
            Exception that is thrown when a semantic error is detected on a YAML stream.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.SemanticErrorException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.SemanticErrorException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:YamlDotNet.Core.SemanticErrorException.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.SemanticErrorException"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.SemanticErrorException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.SemanticErrorException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="inner">The inner.</param>
        </member>
        <member name="T:YamlDotNet.Core.SyntaxErrorException">
            <summary>
            Exception that is thrown when a syntax error is detected on a YAML stream.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.SyntaxErrorException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.SyntaxErrorException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:YamlDotNet.Core.SyntaxErrorException.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.SyntaxErrorException"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.SyntaxErrorException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.SyntaxErrorException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="inner">The inner.</param>
        </member>
        <member name="T:YamlDotNet.Core.TagDirectiveCollection">
            <summary>
            Collection of <see cref="T:YamlDotNet.Core.Tokens.TagDirective"/>.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.TagDirectiveCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.TagDirectiveCollection"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.TagDirectiveCollection.#ctor(System.Collections.Generic.IEnumerable{YamlDotNet.Core.Tokens.TagDirective})">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.TagDirectiveCollection"/> class.
            </summary>
            <param name="tagDirectives">Initial content of the collection.</param>
        </member>
        <member name="M:YamlDotNet.Core.TagDirectiveCollection.GetKeyForItem(YamlDotNet.Core.Tokens.TagDirective)">
            <summary/>
        </member>
        <member name="M:YamlDotNet.Core.TagDirectiveCollection.Contains(YamlDotNet.Core.Tokens.TagDirective)">
            <summary>
            Gets a value indicating whether the collection contains a directive with the same handle
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.Anchor">
            <summary>
            Represents an anchor token.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.Anchor.Value">
            <summary>
            Gets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Anchor.#ctor(YamlDotNet.Core.AnchorName)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Anchor"/> class.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Anchor.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Anchor"/> class.
            </summary>
            <param name="value">The value.</param>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.AnchorAlias">
            <summary>
            Represents an alias token.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.AnchorAlias.Value">
            <summary>
            Gets the value of the alias.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.AnchorAlias.#ctor(YamlDotNet.Core.AnchorName)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.AnchorAlias"/> class.
            </summary>
            <param name="value">The value of the anchor.</param>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.AnchorAlias.#ctor(YamlDotNet.Core.AnchorName,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.AnchorAlias"/> class.
            </summary>
            <param name="value">The value of the anchor.</param>
            <param name="start">The start position of the event.</param>
            <param name="end">The end position of the event.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.BlockEnd">
            <summary>
            Represents a block end token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.BlockEnd.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.BlockEnd"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.BlockEnd.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.BlockEnd"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.BlockEntry">
            <summary>
            Represents a block entry event.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.BlockEntry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.BlockEntry"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.BlockEntry.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.BlockEntry"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.BlockMappingStart">
            <summary>
            Represents a block mapping start token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.BlockMappingStart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.BlockMappingStart"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.BlockMappingStart.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.BlockMappingStart"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.BlockSequenceStart">
            <summary>
            Represents a block sequence start token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.BlockSequenceStart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.BlockSequenceStart"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.BlockSequenceStart.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.BlockSequenceStart"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.Comment">
            <summary>
            Represents a comment
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.Comment.Value">
            <summary>
            Gets the value of the comment
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.Comment.IsInline">
            <summary>
            Gets a value indicating whether the comment appears other tokens on that line.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Comment.#ctor(System.String,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Comment"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Comment.#ctor(System.String,System.Boolean,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Comment"/> class.
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.DocumentEnd">
            <summary>
            Represents a document end token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.DocumentEnd.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.DocumentEnd"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.DocumentEnd.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.DocumentEnd"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.DocumentStart">
            <summary>
            Represents a document start token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.DocumentStart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.DocumentStart"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.DocumentStart.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.DocumentStart"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.Error">
            <summary>
            Base class for YAML tokens.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.Error.Value">
            <summary>
            Gets the value of the comment
            </summary>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.FlowEntry">
            <summary>
            Represents a flow entry event.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.FlowEntry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.FlowEntry"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.FlowEntry.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.FlowEntry"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.FlowMappingEnd">
            <summary>
            Represents a flow mapping end token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.FlowMappingEnd.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.FlowMappingEnd"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.FlowMappingEnd.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.FlowMappingEnd"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.FlowMappingStart">
            <summary>
            Represents a flow mapping start token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.FlowMappingStart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.FlowMappingStart"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.FlowMappingStart.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.FlowMappingStart"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.FlowSequenceEnd">
            <summary>
            Represents a flow sequence end token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.FlowSequenceEnd.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.FlowSequenceEnd"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.FlowSequenceEnd.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.FlowSequenceEnd"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.FlowSequenceStart">
            <summary>
            Represents a flow sequence start token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.FlowSequenceStart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.FlowSequenceStart"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.FlowSequenceStart.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.FlowSequenceStart"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.Key">
            <summary>
            Represents a key token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Key.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Key"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Key.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Key"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.Scalar">
            <summary>
            Represents a scalar token.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.Scalar.Value">
            <summary>
            Gets the value.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.Scalar.Style">
            <summary>
            Gets the style.
            </summary>
            <value>The style.</value>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Scalar.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Scalar"/> class.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Scalar.#ctor(System.String,YamlDotNet.Core.ScalarStyle)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Scalar"/> class.
            </summary>
            <param name="value">The value.</param>
            <param name="style">The style.</param>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Scalar.#ctor(System.String,YamlDotNet.Core.ScalarStyle,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Scalar"/> class.
            </summary>
            <param name="value">The value.</param>
            <param name="style">The style.</param>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.StreamEnd">
            <summary>
            Represents a stream end event.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.StreamEnd.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.StreamEnd"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.StreamEnd.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.StreamEnd"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.StreamStart">
            <summary>
            Represents a stream start token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.StreamStart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.StreamStart"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.StreamStart.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.StreamStart"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.Tag">
            <summary>
            Represents a tag token.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.Tag.Handle">
            <summary>
            Gets the handle.
            </summary>
            <value>The handle.</value>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.Tag.Suffix">
            <summary>
            Gets the suffix.
            </summary>
            <value>The suffix.</value>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Tag.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Tag"/> class.
            </summary>
            <param name="handle">The handle.</param>
            <param name="suffix">The suffix.</param>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Tag.#ctor(System.String,System.String,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Tag"/> class.
            </summary>
            <param name="handle">The handle.</param>
            <param name="suffix">The suffix.</param>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.TagDirective">
            <summary>
            Represents a tag directive token.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.TagDirective.Handle">
            <summary>
            Gets the handle.
            </summary>
            <value>The handle.</value>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.TagDirective.Prefix">
            <summary>
            Gets the prefix.
            </summary>
            <value>The prefix.</value>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.TagDirective.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.TagDirective"/> class.
            </summary>
            <param name="handle">The handle.</param>
            <param name="prefix">The prefix.</param>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.TagDirective.#ctor(System.String,System.String,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.TagDirective"/> class.
            </summary>
            <param name="handle">The handle.</param>
            <param name="prefix">The prefix.</param>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.TagDirective.Equals(System.Object)">
            <summary>
            Determines whether the specified System.Object is equal to the current System.Object.
            </summary>
            <param name="obj">The System.Object to compare with the current System.Object.</param>
            <returns>
            true if the specified System.Object is equal to the current System.Object; otherwise, false.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.TagDirective.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.TagDirective.ToString">
            <summary/>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.Token">
            <summary>
            Base class for YAML tokens.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.Token.Start">
            <summary>
            Gets the start of the token in the input stream.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.Token.End">
            <summary>
            Gets the end of the token in the input stream.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Token.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Token"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.Value">
            <summary>
            Represents a value token.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Value.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Value"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.Value.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.Value"/> class.
            </summary>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="T:YamlDotNet.Core.Tokens.VersionDirective">
            <summary>
            Represents a version directive token.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Tokens.VersionDirective.Version">
            <summary>
            Gets the version.
            </summary>
            <value>The version.</value>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.VersionDirective.#ctor(YamlDotNet.Core.Version)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.VersionDirective"/> class.
            </summary>
            <param name="version">The version.</param>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.VersionDirective.#ctor(YamlDotNet.Core.Version,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Tokens.VersionDirective"/> class.
            </summary>
            <param name="version">The version.</param>
            <param name="start">The start position of the token.</param>
            <param name="end">The end position of the token.</param>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.VersionDirective.Equals(System.Object)">
            <summary>
            Determines whether the specified System.Object is equal to the current System.Object.
            </summary>
            <param name="obj">The System.Object to compare with the current System.Object.</param>
            <returns>
            true if the specified System.Object is equal to the current System.Object; otherwise, false.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Tokens.VersionDirective.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="T:YamlDotNet.Core.Version">
            <summary>
            Specifies the version of the YAML language.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Version.Major">
            <summary>
            Gets the major version number.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.Version.Minor">
            <summary>
            Gets the minor version number.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.Version.#ctor(System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.Version"/> class.
            </summary>
            <param name="major">The major version number.</param>
            <param name="minor">The minor version number.</param>
        </member>
        <member name="M:YamlDotNet.Core.Version.Equals(System.Object)">
            <summary>
            Determines whether the specified System.Object is equal to the current System.Object.
            </summary>
            <param name="obj">The System.Object to compare with the current System.Object.</param>
            <returns>
            true if the specified System.Object is equal to the current System.Object; otherwise, false.
            </returns>
        </member>
        <member name="M:YamlDotNet.Core.Version.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="T:YamlDotNet.Core.YamlException">
            <summary>
            Base exception that is thrown when the a problem occurs in the YamlDotNet library.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.YamlException.Start">
            <summary>
            Gets the position in the input stream where the event that originated the exception starts.
            </summary>
        </member>
        <member name="P:YamlDotNet.Core.YamlException.End">
            <summary>
            Gets the position in the input stream where the event that originated the exception ends.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.YamlException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.YamlException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:YamlDotNet.Core.YamlException.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.YamlException"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.YamlException.#ctor(YamlDotNet.Core.Mark,YamlDotNet.Core.Mark,System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.YamlException"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Core.YamlException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Core.YamlException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="inner">The inner.</param>
        </member>
        <member name="M:YamlDotNet.Helpers.ExpressionExtensions.AsProperty(System.Linq.Expressions.LambdaExpression)">
            <summary>
            Returns the <see cref="T:System.Reflection.PropertyInfo" /> that describes the property that
            is being returned in an expression in the form:
            <code>
              x => x.SomeProperty
            </code>
            </summary>
        </member>
        <member name="T:YamlDotNet.Helpers.GenericCollectionToNonGenericAdapter`1">
            <summary>
            Adapts an <see cref="T:System.Collections.Generic.ICollection`1" /> to <see cref="T:System.Collections.IList" />
            because not all generic collections implement <see cref="T:System.Collections.IList" />.
            </summary>
        </member>
        <member name="T:YamlDotNet.Helpers.GenericDictionaryToNonGenericAdapter`2">
            <summary>
            Adapts an <see cref="T:System.Collections.Generic.IDictionary`2" /> to <see cref="T:System.Collections.IDictionary" />
            because not all generic dictionaries implement <see cref="T:System.Collections.IDictionary" />.
            </summary>
        </member>
        <member name="P:YamlDotNet.Helpers.IOrderedDictionary`2.Item(System.Int32)">
            <summary>
            Gets or sets the element with the specified index.
            </summary>
            <param name="index">The index of the element to get or set.</param>
            <returns>The element with the specified index.</returns>
        </member>
        <member name="M:YamlDotNet.Helpers.IOrderedDictionary`2.Insert(System.Int32,`0,`1)">
            <summary>
            Adds an element with the provided key and value to the <see cref="T:YamlDotNet.Helpers.IOrderedDictionary`2"/>
            at the given index.
            </summary>
            <param name="index">The zero-based index at which the item should be inserted.</param>
            <param name="key">The object to use as the key of the element to add.</param>
            <param name="value">The object to use as the value of the element to add.</param>
        </member>
        <member name="M:YamlDotNet.Helpers.IOrderedDictionary`2.RemoveAt(System.Int32)">
            <summary>
            Removes the element at the specified index.
            </summary>
            <param name="index">The zero-based index of the element to remove.</param>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.DocumentLoadingState">
            <summary>
            Manages the state of a <see cref="T:YamlDotNet.RepresentationModel.YamlDocument" /> while it is loading.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.DocumentLoadingState.AddAnchor(YamlDotNet.RepresentationModel.YamlNode)">
            <summary>
            Adds the specified node to the anchor list.
            </summary>
            <param name="node">The node.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.DocumentLoadingState.GetNode(YamlDotNet.Core.AnchorName,YamlDotNet.Core.Mark,YamlDotNet.Core.Mark)">
            <summary>
            Gets the node with the specified anchor.
            </summary>
            <param name="anchor">The anchor.</param>
            <param name="start">The start position.</param>
            <param name="end">The end position.</param>
            <returns></returns>
            <exception cref="T:YamlDotNet.Core.AnchorNotFoundException">if there is no node with that anchor.</exception>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.DocumentLoadingState.TryGetNode(YamlDotNet.Core.AnchorName,YamlDotNet.RepresentationModel.YamlNode@)">
            <summary>
            Gets the node with the specified anchor.
            </summary>
            <param name="anchor">The anchor.</param>
            <param name="node">The node that was retrieved.</param>
            <returns>true if the anchor was found; otherwise false.</returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.DocumentLoadingState.AddNodeWithUnresolvedAliases(YamlDotNet.RepresentationModel.YamlNode)">
            <summary>
            Adds the specified node to the collection of nodes with unresolved aliases.
            </summary>
            <param name="node">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlNode"/> that has unresolved aliases.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.DocumentLoadingState.ResolveAliases">
            <summary>
            Resolves the aliases that could not be resolved while loading the document.
            </summary>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.EmitterState">
            <summary>
            Holds state that is used when emitting a stream.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.EmitterState.EmittedAnchors">
            <summary>
            Gets the already emitted anchors.
            </summary>
            <value>The emitted anchors.</value>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.IYamlVisitor">
            <summary>
            Defines the method needed to be able to visit Yaml elements.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.IYamlVisitor.Visit(YamlDotNet.RepresentationModel.YamlStream)">
            <summary>
            Visits a <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/>.
            </summary>
            <param name="stream">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.IYamlVisitor.Visit(YamlDotNet.RepresentationModel.YamlDocument)">
            <summary>
            Visits a <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/>.
            </summary>
            <param name="document">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.IYamlVisitor.Visit(YamlDotNet.RepresentationModel.YamlScalarNode)">
            <summary>
            Visits a <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/>.
            </summary>
            <param name="scalar">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.IYamlVisitor.Visit(YamlDotNet.RepresentationModel.YamlSequenceNode)">
            <summary>
            Visits a <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/>.
            </summary>
            <param name="sequence">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.IYamlVisitor.Visit(YamlDotNet.RepresentationModel.YamlMappingNode)">
            <summary>
            Visits a <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/>.
            </summary>
            <param name="mapping">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> that is being visited.
            </param>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.LibYamlEventStream">
            <summary>
            Represents a LibYAML event stream.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.LibYamlEventStream.#ctor(YamlDotNet.Core.IParser)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.LibYamlEventStream"/> class
            from the specified <see cref="T:YamlDotNet.Core.IParser"/>.
            </summary>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlAliasNode">
            <summary>
            Represents an alias node in the YAML document.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlAliasNode.#ctor(YamlDotNet.Core.AnchorName)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlAliasNode"/> class.
            </summary>
            <param name="anchor">The anchor.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlAliasNode.ResolveAliases(YamlDotNet.RepresentationModel.DocumentLoadingState)">
            <summary>
            Resolves the aliases that could not be resolved when the node was created.
            </summary>
            <param name="state">The state of the document.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlAliasNode.Emit(YamlDotNet.Core.IEmitter,YamlDotNet.RepresentationModel.EmitterState)">
            <summary>
            Saves the current node to the specified emitter.
            </summary>
            <param name="emitter">The emitter where the node is to be saved.</param>
            <param name="state">The state.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlAliasNode.Accept(YamlDotNet.RepresentationModel.IYamlVisitor)">
            <summary>
            Accepts the specified visitor by calling the appropriate Visit method on it.
            </summary>
            <param name="visitor">
            A <see cref="T:YamlDotNet.RepresentationModel.IYamlVisitor"/>.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlAliasNode.Equals(System.Object)">
            <summary />
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlAliasNode.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlAliasNode.ToString(YamlDotNet.Core.RecursionLevel)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlAliasNode.SafeAllNodes(YamlDotNet.Core.RecursionLevel)">
            <summary>
            Recursively enumerates all the nodes from the document, starting on the current node,
            and throwing <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/>
            if <see cref="P:YamlDotNet.Core.RecursionLevel.Maximum"/> is reached.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlAliasNode.NodeType">
            <summary>
            Gets the type of node.
            </summary>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlDocument">
            <summary>
            Represents an YAML document.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlDocument.RootNode">
            <summary>
            Gets or sets the root node.
            </summary>
            <value>The root node.</value>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlDocument.#ctor(YamlDotNet.RepresentationModel.YamlNode)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlDocument.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/> class with a single scalar node.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlDocument.#ctor(YamlDotNet.Core.IParser)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/> class.
            </summary>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlDocument.AnchorAssigningVisitor">
            <summary>
            Visitor that assigns anchors to nodes that are referenced more than once.
            Existing anchors are preserved as much as possible.
            </summary>
        </member>
        <member name="F:YamlDotNet.RepresentationModel.YamlDocument.AnchorAssigningVisitor.visitedNodes">
            <summary>
            Key: Node, Value: IsDuplicate
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlDocument.AnchorAssigningVisitor.VisitNodeAndFindDuplicates(YamlDotNet.RepresentationModel.YamlNode)">
            <summary>
            Returns whether the visited node is a duplicate.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlDocument.Accept(YamlDotNet.RepresentationModel.IYamlVisitor)">
            <summary>
            Accepts the specified visitor by calling the appropriate Visit method on it.
            </summary>
            <param name="visitor">
            A <see cref="T:YamlDotNet.RepresentationModel.IYamlVisitor"/>.
            </param>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlDocument.AllNodes">
            <summary>
            Gets all nodes from the document.
            <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/> is thrown if an infinite recursion is detected.
            </summary>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlMappingNode">
            <summary>
            Represents a mapping node in the YAML document.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlMappingNode.Children">
            <summary>
            Gets the children of the current node.
            </summary>
            <value>The children.</value>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlMappingNode.Style">
            <summary>
            Gets or sets the style of the node.
            </summary>
            <value>The style.</value>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.#ctor(YamlDotNet.Core.IParser,YamlDotNet.RepresentationModel.DocumentLoadingState)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.#ctor(System.Collections.Generic.KeyValuePair{YamlDotNet.RepresentationModel.YamlNode,YamlDotNet.RepresentationModel.YamlNode}[])">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.#ctor(System.Collections.Generic.IEnumerable{System.Collections.Generic.KeyValuePair{YamlDotNet.RepresentationModel.YamlNode,YamlDotNet.RepresentationModel.YamlNode}})">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.#ctor(YamlDotNet.RepresentationModel.YamlNode[])">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> class.
            </summary>
            <param name="children">A sequence of <see cref="T:YamlDotNet.RepresentationModel.YamlNode"/> where even elements are keys and odd elements are values.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.#ctor(System.Collections.Generic.IEnumerable{YamlDotNet.RepresentationModel.YamlNode})">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> class.
            </summary>
            <param name="children">A sequence of <see cref="T:YamlDotNet.RepresentationModel.YamlNode"/> where even elements are keys and odd elements are values.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.Add(YamlDotNet.RepresentationModel.YamlNode,YamlDotNet.RepresentationModel.YamlNode)">
            <summary>
            Adds the specified mapping to the <see cref="P:YamlDotNet.RepresentationModel.YamlMappingNode.Children"/> collection.
            </summary>
            <param name="key">The key node.</param>
            <param name="value">The value node.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.Add(System.String,YamlDotNet.RepresentationModel.YamlNode)">
            <summary>
            Adds the specified mapping to the <see cref="P:YamlDotNet.RepresentationModel.YamlMappingNode.Children"/> collection.
            </summary>
            <param name="key">The key node.</param>
            <param name="value">The value node.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.Add(YamlDotNet.RepresentationModel.YamlNode,System.String)">
            <summary>
            Adds the specified mapping to the <see cref="P:YamlDotNet.RepresentationModel.YamlMappingNode.Children"/> collection.
            </summary>
            <param name="key">The key node.</param>
            <param name="value">The value node.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.Add(System.String,System.String)">
            <summary>
            Adds the specified mapping to the <see cref="P:YamlDotNet.RepresentationModel.YamlMappingNode.Children"/> collection.
            </summary>
            <param name="key">The key node.</param>
            <param name="value">The value node.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.ResolveAliases(YamlDotNet.RepresentationModel.DocumentLoadingState)">
            <summary>
            Resolves the aliases that could not be resolved when the node was created.
            </summary>
            <param name="state">The state of the document.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.Emit(YamlDotNet.Core.IEmitter,YamlDotNet.RepresentationModel.EmitterState)">
            <summary>
            Saves the current node to the specified emitter.
            </summary>
            <param name="emitter">The emitter where the node is to be saved.</param>
            <param name="state">The state.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.Accept(YamlDotNet.RepresentationModel.IYamlVisitor)">
            <summary>
            Accepts the specified visitor by calling the appropriate Visit method on it.
            </summary>
            <param name="visitor">
            A <see cref="T:YamlDotNet.RepresentationModel.IYamlVisitor"/>.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.Equals(System.Object)">
            <summary />
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.SafeAllNodes(YamlDotNet.Core.RecursionLevel)">
            <summary>
            Recursively enumerates all the nodes from the document, starting on the current node,
            and throwing <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/>
            if <see cref="P:YamlDotNet.Core.RecursionLevel.Maximum"/> is reached.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlMappingNode.NodeType">
            <summary>
            Gets the type of node.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.ToString(YamlDotNet.Core.RecursionLevel)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.GetEnumerator">
            <summary />
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlMappingNode.FromObject(System.Object)">
            <summary>
            Creates a <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode" /> containing a key-value pair for each property of the specified object.
            </summary>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlNode">
            <summary>
            Represents a single node in the YAML document.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlNode.Anchor">
            <summary>
            Gets or sets the anchor of the node.
            </summary>
            <value>The anchor.</value>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlNode.Tag">
            <summary>
            Gets or sets the tag of the node.
            </summary>
            <value>The tag.</value>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlNode.Start">
            <summary>
            Gets the position in the input stream where the event that originated the node starts.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlNode.End">
            <summary>
            Gets the position in the input stream where the event that originated the node ends.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNode.Load(YamlDotNet.Core.Events.NodeEvent,YamlDotNet.RepresentationModel.DocumentLoadingState)">
            <summary>
            Loads the specified event.
            </summary>
            <param name="yamlEvent">The event.</param>
            <param name="state">The state of the document.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNode.ParseNode(YamlDotNet.Core.IParser,YamlDotNet.RepresentationModel.DocumentLoadingState)">
            <summary>
            Parses the node represented by the next event in <paramref name="parser" />.
            </summary>
            <returns>Returns the node that has been parsed.</returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNode.ResolveAliases(YamlDotNet.RepresentationModel.DocumentLoadingState)">
            <summary>
            Resolves the aliases that could not be resolved when the node was created.
            </summary>
            <param name="state">The state of the document.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNode.Save(YamlDotNet.Core.IEmitter,YamlDotNet.RepresentationModel.EmitterState)">
            <summary>
            Saves the current node to the specified emitter.
            </summary>
            <param name="emitter">The emitter where the node is to be saved.</param>
            <param name="state">The state.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNode.Emit(YamlDotNet.Core.IEmitter,YamlDotNet.RepresentationModel.EmitterState)">
            <summary>
            Saves the current node to the specified emitter.
            </summary>
            <param name="emitter">The emitter where the node is to be saved.</param>
            <param name="state">The state.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNode.Accept(YamlDotNet.RepresentationModel.IYamlVisitor)">
            <summary>
            Accepts the specified visitor by calling the appropriate Visit method on it.
            </summary>
            <param name="visitor">
            A <see cref="T:YamlDotNet.RepresentationModel.IYamlVisitor"/>.
            </param>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlNode.AllNodes">
            <summary>
            Gets all nodes from the document, starting on the current node.
            <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/> is thrown if an infinite recursion is detected.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNode.SafeAllNodes(YamlDotNet.Core.RecursionLevel)">
            <summary>
            When implemented, recursively enumerates all the nodes from the document, starting on the current node.
            If <see cref="P:YamlDotNet.Core.RecursionLevel.Maximum"/> is reached, a <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/> is thrown
            instead of continuing and crashing with a <see cref="!:StackOverflowException"/>.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlNode.NodeType">
            <summary>
            Gets the type of node.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNode.op_Implicit(System.String)~YamlDotNet.RepresentationModel.YamlNode">
            <summary>
            Performs an implicit conversion from <see cref="T:System.String"/> to <see cref="T:YamlDotNet.RepresentationModel.YamlNode"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNode.op_Implicit(System.String[])~YamlDotNet.RepresentationModel.YamlNode">
            <summary>
            Performs an implicit conversion from string[] to <see cref="T:YamlDotNet.RepresentationModel.YamlNode"/>.
            </summary>
            <param name="sequence">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNode.op_Explicit(YamlDotNet.RepresentationModel.YamlNode)~System.String">
            <summary>
            Converts a <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode" /> to a string by returning its value.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlNode.Item(System.Int32)">
            <summary>
            Gets the nth element in a <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode" />.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlNode.Item(YamlDotNet.RepresentationModel.YamlNode)">
            <summary>
            Gets the value associated with a key in a <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode" />.
            </summary>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlNodeIdentityEqualityComparer">
            <summary>
            Comparer that is based on identity comparisons.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNodeIdentityEqualityComparer.Equals(YamlDotNet.RepresentationModel.YamlNode,YamlDotNet.RepresentationModel.YamlNode)">
            <summary />
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlNodeIdentityEqualityComparer.GetHashCode(YamlDotNet.RepresentationModel.YamlNode)">
            <summary />
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlNodeType">
            <summary>
            Specifies the type of node in the representation model.
            </summary>
        </member>
        <member name="F:YamlDotNet.RepresentationModel.YamlNodeType.Alias">
            <summary>
            The node is a <see cref="T:YamlDotNet.RepresentationModel.YamlAliasNode"/>.
            </summary>
        </member>
        <member name="F:YamlDotNet.RepresentationModel.YamlNodeType.Mapping">
            <summary>
            The node is a <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/>.
            </summary>
        </member>
        <member name="F:YamlDotNet.RepresentationModel.YamlNodeType.Scalar">
            <summary>
            The node is a <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/>.
            </summary>
        </member>
        <member name="F:YamlDotNet.RepresentationModel.YamlNodeType.Sequence">
            <summary>
            The node is a <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/>.
            </summary>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlScalarNode">
            <summary>
            Represents a scalar node in the YAML document.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlScalarNode.Value">
            <summary>
            Gets or sets the value of the node.
            </summary>
            <value>The value.</value>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlScalarNode.Style">
            <summary>
            Gets or sets the style of the node.
            </summary>
            <value>The style.</value>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.#ctor(YamlDotNet.Core.IParser,YamlDotNet.RepresentationModel.DocumentLoadingState)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/> class.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.ResolveAliases(YamlDotNet.RepresentationModel.DocumentLoadingState)">
            <summary>
            Resolves the aliases that could not be resolved when the node was created.
            </summary>
            <param name="state">The state of the document.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.Emit(YamlDotNet.Core.IEmitter,YamlDotNet.RepresentationModel.EmitterState)">
            <summary>
            Saves the current node to the specified emitter.
            </summary>
            <param name="emitter">The emitter where the node is to be saved.</param>
            <param name="state">The state.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.Accept(YamlDotNet.RepresentationModel.IYamlVisitor)">
            <summary>
            Accepts the specified visitor by calling the appropriate Visit method on it.
            </summary>
            <param name="visitor">
            A <see cref="T:YamlDotNet.RepresentationModel.IYamlVisitor"/>.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.Equals(System.Object)">
            <summary />
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.op_Explicit(YamlDotNet.RepresentationModel.YamlScalarNode)~System.String">
            <summary>
            Performs an explicit conversion from <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/> to <see cref="T:System.String"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.ToString(YamlDotNet.Core.RecursionLevel)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlScalarNode.SafeAllNodes(YamlDotNet.Core.RecursionLevel)">
            <summary>
            Recursively enumerates all the nodes from the document, starting on the current node,
            and throwing <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/>
            if <see cref="P:YamlDotNet.Core.RecursionLevel.Maximum"/> is reached.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlScalarNode.NodeType">
            <summary>
            Gets the type of node.
            </summary>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlSequenceNode">
            <summary>
            Represents a sequence node in the YAML document.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlSequenceNode.Children">
            <summary>
            Gets the collection of child nodes.
            </summary>
            <value>The children.</value>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlSequenceNode.Style">
            <summary>
            Gets or sets the style of the node.
            </summary>
            <value>The style.</value>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.#ctor(YamlDotNet.Core.IParser,YamlDotNet.RepresentationModel.DocumentLoadingState)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.#ctor(YamlDotNet.RepresentationModel.YamlNode[])">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.#ctor(System.Collections.Generic.IEnumerable{YamlDotNet.RepresentationModel.YamlNode})">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.Add(YamlDotNet.RepresentationModel.YamlNode)">
            <summary>
            Adds the specified child to the <see cref="P:YamlDotNet.RepresentationModel.YamlSequenceNode.Children"/> collection.
            </summary>
            <param name="child">The child.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.Add(System.String)">
            <summary>
            Adds a scalar node to the <see cref="P:YamlDotNet.RepresentationModel.YamlSequenceNode.Children"/> collection.
            </summary>
            <param name="child">The child.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.ResolveAliases(YamlDotNet.RepresentationModel.DocumentLoadingState)">
            <summary>
            Resolves the aliases that could not be resolved when the node was created.
            </summary>
            <param name="state">The state of the document.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.Emit(YamlDotNet.Core.IEmitter,YamlDotNet.RepresentationModel.EmitterState)">
            <summary>
            Saves the current node to the specified emitter.
            </summary>
            <param name="emitter">The emitter where the node is to be saved.</param>
            <param name="state">The state.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.Accept(YamlDotNet.RepresentationModel.IYamlVisitor)">
            <summary>
            Accepts the specified visitor by calling the appropriate Visit method on it.
            </summary>
            <param name="visitor">
            A <see cref="T:YamlDotNet.RepresentationModel.IYamlVisitor"/>.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.Equals(System.Object)">
            <summary />
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>
            A hash code for the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.SafeAllNodes(YamlDotNet.Core.RecursionLevel)">
            <summary>
            Recursively enumerates all the nodes from the document, starting on the current node,
            and throwing <see cref="T:YamlDotNet.Core.MaximumRecursionLevelReachedException"/>
            if <see cref="P:YamlDotNet.Core.RecursionLevel.Maximum"/> is reached.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlSequenceNode.NodeType">
            <summary>
            Gets the type of node.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.ToString(YamlDotNet.Core.RecursionLevel)">
            <summary>
            Returns a <see cref="T:System.String"/> that represents this instance.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents this instance.
            </returns>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlSequenceNode.GetEnumerator">
            <summary />
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlStream">
            <summary>
            Represents an YAML stream.
            </summary>
        </member>
        <member name="P:YamlDotNet.RepresentationModel.YamlStream.Documents">
            <summary>
            Gets the documents inside the stream.
            </summary>
            <value>The documents.</value>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.#ctor(YamlDotNet.RepresentationModel.YamlDocument[])">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.#ctor(System.Collections.Generic.IEnumerable{YamlDotNet.RepresentationModel.YamlDocument})">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.Add(YamlDotNet.RepresentationModel.YamlDocument)">
            <summary>
            Adds the specified document to the <see cref="P:YamlDotNet.RepresentationModel.YamlStream.Documents"/> collection.
            </summary>
            <param name="document">The document.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.Load(System.IO.TextReader)">
            <summary>
            Loads the stream from the specified input.
            </summary>
            <param name="input">The input.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.Load(YamlDotNet.Core.IParser)">
            <summary>
            Loads the stream from the specified <see cref="T:YamlDotNet.Core.IParser"/>.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.Save(System.IO.TextWriter)">
            <summary>
            Saves the stream to the specified output.
            </summary>
            <param name="output">The output.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.Save(System.IO.TextWriter,System.Boolean)">
            <summary>
            Saves the stream to the specified output.
            </summary>
            <param name="output">The output.</param>
            <param name="assignAnchors">Indicates whether or not to assign node anchors.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.Save(YamlDotNet.Core.IEmitter,System.Boolean)">
            <summary>
            Saves the stream to the specified emitter.
            </summary>
            <param name="emitter">The emitter.</param>
            <param name="assignAnchors">Indicates whether or not to assign node anchors.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.Accept(YamlDotNet.RepresentationModel.IYamlVisitor)">
            <summary>
            Accepts the specified visitor by calling the appropriate Visit method on it.
            </summary>
            <param name="visitor">
            A <see cref="T:YamlDotNet.RepresentationModel.IYamlVisitor"/>.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlStream.GetEnumerator">
            <summary />
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlVisitor">
            <summary>
            Abstract implementation of <see cref="T:YamlDotNet.RepresentationModel.IYamlVisitor"/> that knows how to walk a complete Yaml object model.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.Visit(YamlDotNet.RepresentationModel.YamlStream)">
            <summary>
            Called when this object is visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/>.
            </summary>
            <param name="stream">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.Visited(YamlDotNet.RepresentationModel.YamlStream)">
            <summary>
            Called after this object finishes visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/>.
            </summary>
            <param name="stream">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/> that has been visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.Visit(YamlDotNet.RepresentationModel.YamlDocument)">
            <summary>
            Called when this object is visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/>.
            </summary>
            <param name="document">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.Visited(YamlDotNet.RepresentationModel.YamlDocument)">
            <summary>
            Called after this object finishes visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/>.
            </summary>
            <param name="document">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/> that has been visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.Visit(YamlDotNet.RepresentationModel.YamlScalarNode)">
            <summary>
            Called when this object is visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/>.
            </summary>
            <param name="scalar">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.Visited(YamlDotNet.RepresentationModel.YamlScalarNode)">
            <summary>
            Called after this object finishes visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/>.
            </summary>
            <param name="scalar">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/> that has been visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.Visit(YamlDotNet.RepresentationModel.YamlSequenceNode)">
            <summary>
            Called when this object is visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/>.
            </summary>
            <param name="sequence">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.Visited(YamlDotNet.RepresentationModel.YamlSequenceNode)">
            <summary>
            Called after this object finishes visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/>.
            </summary>
            <param name="sequence">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/> that has been visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.Visit(YamlDotNet.RepresentationModel.YamlMappingNode)">
            <summary>
            Called when this object is visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/>.
            </summary>
            <param name="mapping">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.Visited(YamlDotNet.RepresentationModel.YamlMappingNode)">
            <summary>
            Called after this object finishes visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/>.
            </summary>
            <param name="mapping">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> that has been visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.VisitChildren(YamlDotNet.RepresentationModel.YamlStream)">
            <summary>
            Visits every child of a <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/>.
            </summary>
            <param name="stream">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.VisitChildren(YamlDotNet.RepresentationModel.YamlDocument)">
            <summary>
            Visits every child of a <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/>.
            </summary>
            <param name="document">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.VisitChildren(YamlDotNet.RepresentationModel.YamlSequenceNode)">
            <summary>
            Visits every child of a <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/>.
            </summary>
            <param name="sequence">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitor.VisitChildren(YamlDotNet.RepresentationModel.YamlMappingNode)">
            <summary>
            Visits every child of a <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/>.
            </summary>
            <param name="mapping">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> that is being visited.
            </param>
        </member>
        <member name="T:YamlDotNet.RepresentationModel.YamlVisitorBase">
            <summary>
            Abstract implementation of <see cref="T:YamlDotNet.RepresentationModel.IYamlVisitor"/> that knows how to walk a complete YAML object model.
            </summary>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitorBase.Visit(YamlDotNet.RepresentationModel.YamlStream)">
            <summary>
            Called when this object is visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/>.
            </summary>
            <param name="stream">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitorBase.Visit(YamlDotNet.RepresentationModel.YamlDocument)">
            <summary>
            Called when this object is visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/>.
            </summary>
            <param name="document">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitorBase.Visit(YamlDotNet.RepresentationModel.YamlScalarNode)">
            <summary>
            Called when this object is visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/>.
            </summary>
            <param name="scalar">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlScalarNode"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitorBase.Visit(YamlDotNet.RepresentationModel.YamlSequenceNode)">
            <summary>
            Called when this object is visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/>.
            </summary>
            <param name="sequence">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitorBase.Visit(YamlDotNet.RepresentationModel.YamlMappingNode)">
            <summary>
            Called when this object is visiting a <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/>.
            </summary>
            <param name="mapping">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitorBase.VisitPair(YamlDotNet.RepresentationModel.YamlNode,YamlDotNet.RepresentationModel.YamlNode)">
            <summary>
            Called when this object is visiting a key-value pair.
            </summary>
            <param name="key">The left (key) <see cref="T:YamlDotNet.RepresentationModel.YamlNode"/> that is being visited.</param>
            <param name="value">The right (value) <see cref="T:YamlDotNet.RepresentationModel.YamlNode"/> that is being visited.</param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitorBase.VisitChildren(YamlDotNet.RepresentationModel.YamlStream)">
            <summary>
            Visits every child of a <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/>.
            </summary>
            <param name="stream">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlStream"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitorBase.VisitChildren(YamlDotNet.RepresentationModel.YamlDocument)">
            <summary>
            Visits every child of a <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/>.
            </summary>
            <param name="document">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlDocument"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitorBase.VisitChildren(YamlDotNet.RepresentationModel.YamlSequenceNode)">
            <summary>
            Visits every child of a <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/>.
            </summary>
            <param name="sequence">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlSequenceNode"/> that is being visited.
            </param>
        </member>
        <member name="M:YamlDotNet.RepresentationModel.YamlVisitorBase.VisitChildren(YamlDotNet.RepresentationModel.YamlMappingNode)">
            <summary>
            Visits every child of a <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/>.
            </summary>
            <param name="mapping">
            The <see cref="T:YamlDotNet.RepresentationModel.YamlMappingNode"/> that is being visited.
            </param>
        </member>
        <member name="T:YamlDotNet.Serialization.BuilderSkeleton`1">
            <summary>
            Common implementation of <see cref="T:YamlDotNet.Serialization.SerializerBuilder" /> and <see cref="T:YamlDotNet.Serialization.DeserializerBuilder" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.IgnoreFields">
            <summary>
            Prevents serialization and deserialization of fields.
            </summary>
            <returns></returns>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.IncludeNonPublicProperties">
            <summary>
            Allows serialization and deserialization of non-public properties.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithNamingConvention(YamlDotNet.Serialization.INamingConvention)">
            <summary>
            Sets the <see cref="T:YamlDotNet.Serialization.INamingConvention" /> that will be used by the (de)serializer.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithTypeResolver(YamlDotNet.Serialization.ITypeResolver)">
            <summary>
            Sets the <see cref="T:YamlDotNet.Serialization.ITypeResolver" /> that will be used by the (de)serializer.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithAttributeOverride``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}},System.Attribute)">
            <summary>
            Register an <see cref="T:System.Attribute"/> for a given property.
            </summary>
            <typeparam name="TClass"></typeparam>
            <param name="propertyAccessor">An expression in the form: x => x.SomeProperty</param>
            <param name="attribute">The attribute to register.</param>
            <returns></returns>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithAttributeOverride(System.Type,System.String,System.Attribute)">
            <summary>
            Register an <see cref="T:System.Attribute"/> for a given property.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithTypeConverter(YamlDotNet.Serialization.IYamlTypeConverter)">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IYamlTypeConverter" /> to be used by the (de)serializer.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithTypeConverter(YamlDotNet.Serialization.IYamlTypeConverter,System.Action{YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.IYamlTypeConverter}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IYamlTypeConverter" /> to be used by the (de)serializer.
            </summary>
            <param name="typeConverter"></param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.IYamlTypeConverter" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithTypeConverter``1(YamlDotNet.Serialization.WrapperFactory{YamlDotNet.Serialization.IYamlTypeConverter,YamlDotNet.Serialization.IYamlTypeConverter},System.Action{YamlDotNet.Serialization.ITrackingRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.IYamlTypeConverter}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IYamlTypeConverter" /> to be used by the (de)serializer.
            </summary>
            <param name="typeConverterFactory">A factory that creates the <see cref="T:YamlDotNet.Serialization.IYamlTypeConverter" /> based on a previously registered <see cref="T:YamlDotNet.Serialization.IYamlTypeConverter" />.</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.IYamlTypeConverter" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithoutTypeConverter``1">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.IYamlTypeConverter" /> of type <typeparam name="TYamlTypeConverter" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithoutTypeConverter(System.Type)">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.IYamlTypeConverter" /> of type <param name="converterType" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithTypeInspector``1(System.Func{YamlDotNet.Serialization.ITypeInspector,``0})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.ITypeInspector" /> to be used by the (de)serializer.
            </summary>
            <param name="typeInspectorFactory">A function that instantiates the type inspector.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithTypeInspector``1(System.Func{YamlDotNet.Serialization.ITypeInspector,``0},System.Action{YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.ITypeInspector}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.ITypeInspector" /> to be used by the (de)serializer.
            </summary>
            <param name="typeInspectorFactory">A function that instantiates the type inspector.</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.ITypeInspector" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithTypeInspector``1(YamlDotNet.Serialization.WrapperFactory{YamlDotNet.Serialization.ITypeInspector,YamlDotNet.Serialization.ITypeInspector,``0},System.Action{YamlDotNet.Serialization.ITrackingRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.ITypeInspector}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.ITypeInspector" /> to be used by the (de)serializer.
            </summary>
            <param name="typeInspectorFactory">A function that instantiates the type inspector based on a previously registered <see cref="T:YamlDotNet.Serialization.ITypeInspector" />..</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.ITypeInspector" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithoutTypeInspector``1">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.ITypeInspector" /> of type <typeparam name="TTypeInspector" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.BuilderSkeleton`1.WithoutTypeInspector(System.Type)">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.ITypeInspector" /> of type <param name="inspectorType" />.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.WrapperFactory`2">
            <summary>
            A factory that creates instances of <typeparamref name="TComponent" /> based on an existing <typeparamref name="TComponentBase" />.
            </summary>
            <typeparam name="TComponentBase">The type of the wrapped component.</typeparam>
            <typeparam name="TComponent">The type of the component that this factory creates.</typeparam>
            <param name="wrapped">The component that is to be wrapped.</param>
            <returns>Returns a new instance of <typeparamref name="TComponent" /> that is based on <paramref name="wrapped" />.</returns>
        </member>
        <member name="T:YamlDotNet.Serialization.WrapperFactory`3">
            <summary>
            A factory that creates instances of <typeparamref name="TComponent" /> based on an existing <typeparamref name="TComponentBase" /> and an argument.
            </summary>
            <typeparam name="TArgument">The type of the argument.</typeparam>
            <typeparam name="TComponentBase">The type of the wrapped component.</typeparam>
            <typeparam name="TComponent">The type of the component that this factory creates.</typeparam>
            <param name="wrapped">The component that is to be wrapped.</param>
            <param name="argument">The argument of the factory.</param>
            <returns>Returns a new instance of <typeparamref name="TComponent" /> that is based on <paramref name="wrapped" /> and <paramref name="argument" />.</returns>
        </member>
        <member name="T:YamlDotNet.Serialization.Converters.DateTimeConverter">
            <summary>
            This represents the YAML converter entity for <see cref="T:System.DateTime"/>.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.Converters.DateTimeConverter.#ctor(System.DateTimeKind,System.IFormatProvider,System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Serialization.Converters.DateTimeConverter"/> class.
            </summary>
            <param name="kind"><see cref="T:System.DateTimeKind"/> value. Default value is <see cref="F:System.DateTimeKind.Utc"/>. <see cref="F:System.DateTimeKind.Unspecified"/> is considered as <see cref="F:System.DateTimeKind.Utc"/>.</param>
            <param name="provider"><see cref="T:System.IFormatProvider"/> instance. Default value is <see cref="P:System.Globalization.CultureInfo.InvariantCulture"/>.</param>
            <param name="formats">List of date/time formats for parsing. Default value is "<c>G</c>".</param>
            <remarks>On deserializing, all formats in the list are used for conversion, while on serializing, the first format in the list is used.</remarks>
        </member>
        <member name="M:YamlDotNet.Serialization.Converters.DateTimeConverter.Accepts(System.Type)">
            <summary>
            Gets a value indicating whether the current converter supports converting the specified type.
            </summary>
            <param name="type"><see cref="T:System.Type"/> to check.</param>
            <returns>Returns <c>True</c>, if the current converter supports; otherwise returns <c>False</c>.</returns>
        </member>
        <member name="M:YamlDotNet.Serialization.Converters.DateTimeConverter.ReadYaml(YamlDotNet.Core.IParser,System.Type)">
            <summary>
            Reads an object's state from a YAML parser.
            </summary>
            <param name="parser"><see cref="T:YamlDotNet.Core.IParser"/> instance.</param>
            <param name="type"><see cref="T:System.Type"/> to convert.</param>
            <returns>Returns the <see cref="T:System.DateTime"/> instance converted.</returns>
            <remarks>On deserializing, all formats in the list are used for conversion.</remarks>
        </member>
        <member name="M:YamlDotNet.Serialization.Converters.DateTimeConverter.WriteYaml(YamlDotNet.Core.IEmitter,System.Object,System.Type)">
            <summary>
            Writes the specified object's state to a YAML emitter.
            </summary>
            <param name="emitter"><see cref="T:YamlDotNet.Core.IEmitter"/> instance.</param>
            <param name="value">Value to write.</param>
            <param name="type"><see cref="T:System.Type"/> to convert.</param>
            <remarks>On serializing, the first format in the list is used.</remarks>
        </member>
        <member name="T:YamlDotNet.Serialization.Converters.GuidConverter">
            <summary>
            Converter for System.Guid.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.Converters.SystemTypeConverter">
            <summary>
            Converter for System.Type.
            </summary>
            <remarks>
            Converts <see cref="T:System.Type" /> to a scalar containing the assembly qualified name of the type.
            </remarks>
        </member>
        <member name="T:YamlDotNet.Serialization.DefaultValuesHandling">
            <summary>
            Specifies the strategy to handle default and null values during serialization of properties.
            </summary>
        </member>
        <member name="F:YamlDotNet.Serialization.DefaultValuesHandling.Preserve">
            <summary>
            Specifies that all properties are to be emitted regardless of their value. This is the default behavior.
            </summary>
        </member>
        <member name="F:YamlDotNet.Serialization.DefaultValuesHandling.OmitNull">
            <summary>
            Specifies that properties that contain null references or a null Nullable&lt;T&gt; are to be omitted. 
            </summary>
        </member>
        <member name="F:YamlDotNet.Serialization.DefaultValuesHandling.OmitDefaults">
            <summary>
            Specifies that properties that that contain their default value, either default(T) or the value specified in DefaultValueAttribute are to be omitted. 
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.Deserializer">
            <summary>
            Deserializes objects from the YAML format.
            To customize the behavior of <see cref="T:YamlDotNet.Serialization.Deserializer" />,
            use the <see cref="T:YamlDotNet.Serialization.DeserializerBuilder" /> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.Deserializer.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:YamlDotNet.Serialization.Deserializer" /> using the default configuration.
            </summary>
            <remarks>
            To customize the behavior of the deserializer, use <see cref="T:YamlDotNet.Serialization.DeserializerBuilder" />.
            </remarks>
        </member>
        <member name="M:YamlDotNet.Serialization.Deserializer.#ctor(YamlDotNet.Serialization.IValueDeserializer)">
            <remarks>
            This constructor is private to discourage its use.
            To invoke it, call the <see cref="M:YamlDotNet.Serialization.Deserializer.FromValueDeserializer(YamlDotNet.Serialization.IValueDeserializer)"/> method.
            </remarks>
        </member>
        <member name="M:YamlDotNet.Serialization.Deserializer.FromValueDeserializer(YamlDotNet.Serialization.IValueDeserializer)">
            <summary>
            Creates a new <see cref="T:YamlDotNet.Serialization.Deserializer" /> that uses the specified <see cref="T:YamlDotNet.Serialization.IValueDeserializer" />.
            This method is available for advanced scenarios. The preferred way to customize the behavior of the
            deserializer is to use <see cref="T:YamlDotNet.Serialization.DeserializerBuilder" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.Deserializer.Deserialize(YamlDotNet.Core.IParser,System.Type)">
            <summary>
            Deserializes an object of the specified type.
            </summary>
            <param name="parser">The <see cref="T:YamlDotNet.Core.IParser" /> from where to deserialize the object.</param>
            <param name="type">The static type of the object to deserialize.</param>
            <returns>Returns the deserialized object.</returns>
        </member>
        <member name="T:YamlDotNet.Serialization.DeserializerBuilder">
            <summary>
            Creates and configures instances of <see cref="T:YamlDotNet.Serialization.Deserializer" />.
            This class is used to customize the behavior of <see cref="T:YamlDotNet.Serialization.Deserializer" />. Use the relevant methods
            to apply customizations, then call <see cref="M:YamlDotNet.Serialization.DeserializerBuilder.Build" /> to create an instance of the deserializer
            with the desired customizations.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.#ctor">
            <summary>
            Initializes a new <see cref="T:YamlDotNet.Serialization.DeserializerBuilder" /> using the default component registrations.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithObjectFactory(YamlDotNet.Serialization.IObjectFactory)">
            <summary>
            Sets the <see cref="T:YamlDotNet.Serialization.IObjectFactory" /> that will be used by the deserializer.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithObjectFactory(System.Func{System.Type,System.Object})">
            <summary>
            Sets the <see cref="T:YamlDotNet.Serialization.IObjectFactory" /> that will be used by the deserializer.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithNodeDeserializer(YamlDotNet.Serialization.INodeDeserializer)">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.INodeDeserializer" /> to be used by the deserializer.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithNodeDeserializer(YamlDotNet.Serialization.INodeDeserializer,System.Action{YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.INodeDeserializer}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.INodeDeserializer" /> to be used by the deserializer.
            </summary>
            <param name="nodeDeserializer"></param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.INodeDeserializer" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithNodeDeserializer``1(YamlDotNet.Serialization.WrapperFactory{YamlDotNet.Serialization.INodeDeserializer,``0},System.Action{YamlDotNet.Serialization.ITrackingRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.INodeDeserializer}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.INodeDeserializer" /> to be used by the deserializer.
            </summary>
            <param name="nodeDeserializerFactory">A factory that creates the <see cref="T:YamlDotNet.Serialization.INodeDeserializer" /> based on a previously registered <see cref="T:YamlDotNet.Serialization.INodeDeserializer" />.</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.INodeDeserializer" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithoutNodeDeserializer``1">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.INodeDeserializer" /> of type <typeparam name="TNodeDeserializer" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithoutNodeDeserializer(System.Type)">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.INodeDeserializer" /> of type <param name="nodeDeserializerType" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithNodeTypeResolver(YamlDotNet.Serialization.INodeTypeResolver)">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.INodeTypeResolver" /> to be used by the deserializer.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithNodeTypeResolver(YamlDotNet.Serialization.INodeTypeResolver,System.Action{YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.INodeTypeResolver}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.INodeTypeResolver" /> to be used by the deserializer.
            </summary>
            <param name="nodeTypeResolver"></param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.INodeTypeResolver" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithNodeTypeResolver``1(YamlDotNet.Serialization.WrapperFactory{YamlDotNet.Serialization.INodeTypeResolver,``0},System.Action{YamlDotNet.Serialization.ITrackingRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.INodeTypeResolver}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.INodeTypeResolver" /> to be used by the deserializer.
            </summary>
            <param name="nodeTypeResolverFactory">A factory that creates the <see cref="T:YamlDotNet.Serialization.INodeTypeResolver" /> based on a previously registered <see cref="T:YamlDotNet.Serialization.INodeTypeResolver" />.</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.INodeTypeResolver" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithoutNodeTypeResolver``1">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.INodeTypeResolver" /> of type <typeparam name="TNodeTypeResolver" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithoutNodeTypeResolver(System.Type)">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.INodeTypeResolver" /> of type <param name="nodeTypeResolverType" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithTagMapping(YamlDotNet.Core.TagName,System.Type)">
            <summary>
            Registers a tag mapping.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.WithoutTagMapping(YamlDotNet.Core.TagName)">
            <summary>
            Unregisters an existing tag mapping.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.IgnoreUnmatchedProperties">
            <summary>
            Instructs the deserializer to ignore unmatched properties instead of throwing an exception.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.Build">
            <summary>
            Creates a new <see cref="T:YamlDotNet.Serialization.Deserializer" /> according to the current configuration.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.DeserializerBuilder.BuildValueDeserializer">
            <summary>
            Creates a new <see cref="T:YamlDotNet.Serialization.IValueDeserializer" /> that implements the current configuration.
            This method is available for advanced scenarios. The preferred way to customize the behavior of the
            deserializer is to use the <see cref="M:YamlDotNet.Serialization.DeserializerBuilder.Build" /> method.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.EmissionPhaseObjectGraphVisitorArgs.InnerVisitor">
            <summary>
            Gets the next visitor that should be called by the current visitor.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.EmissionPhaseObjectGraphVisitorArgs.EventEmitter">
            <summary>
            Gets the <see cref="T:YamlDotNet.Serialization.IEventEmitter" /> that is to be used for serialization.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.EmissionPhaseObjectGraphVisitorArgs.NestedObjectSerializer">
            <summary>
            Gets a function that, when called, serializes the specified object.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.EmissionPhaseObjectGraphVisitorArgs.GetPreProcessingPhaseObjectGraphVisitor``1">
            <summary>
            Gets the visitor of type <typeparamref name="T" /> that was used during the pre-processing phase.
            </summary>
            <typeparam name="T">The type of the visitor.s</typeparam>
            <returns></returns>
            <exception cref="T:System.InvalidOperationException">
            No visitor of that type has been registered,
            or ore than one visitors registered are of type <typeparamref name="T"/>.
            </exception>
        </member>
        <member name="T:YamlDotNet.Serialization.EventEmitters.ChainedEventEmitter">
            <summary>
            Provided the base implementation for an IEventEmitter that is a
            decorator for another IEventEmitter.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IDeserializer.Deserialize(YamlDotNet.Core.IParser,System.Type)">
            <summary>
            Deserializes an object of the specified type.
            </summary>
            <param name="parser">The <see cref="T:YamlDotNet.Core.IParser" /> from where to deserialize the object.</param>
            <param name="type">The static type of the object to deserialize.</param>
            <returns>Returns the deserialized object.</returns>
        </member>
        <member name="T:YamlDotNet.Serialization.INamingConvention">
            <summary>
            Translates property names according to a specific convention.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.INodeTypeResolver.Resolve(YamlDotNet.Core.Events.NodeEvent,System.Type@)">
            <summary>
            Determines the type of the specified node.
            </summary>
            <param name="nodeEvent">The node to be deserialized.</param>
            <param name="currentType">The type that has been determined so far.</param>
            <returns>
            true if <paramref name="currentType"/> has been resolved completely;
            false if the next type <see cref="T:YamlDotNet.Serialization.INodeTypeResolver"/> should be invoked.
            </returns>
        </member>
        <member name="T:YamlDotNet.Serialization.IObjectDescriptor">
            <summary>
            Represents an object along with its type.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.IObjectDescriptor.Value">
            <summary>
            A reference to the object.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.IObjectDescriptor.Type">
            <summary>
            The type that should be used when to interpret the <see cref="P:YamlDotNet.Serialization.IObjectDescriptor.Value" />.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.IObjectDescriptor.StaticType">
            <summary>
            The type of <see cref="P:YamlDotNet.Serialization.IObjectDescriptor.Value" /> as determined by its container (e.g. a property).
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.IObjectDescriptor.ScalarStyle">
            <summary>
            The style to be used for scalars.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.ObjectDescriptorExtensions.NonNullValue(YamlDotNet.Serialization.IObjectDescriptor)">
            <summary>
            Returns the Value property of the <paramref name="objectDescriptor"/> if it is not null.
            This is useful in all places that the value must not be null.
            </summary>
            <param name="objectDescriptor">An object descriptor.</param>
            <exception cref="T:System.InvalidOperationException">Thrown when the Value is null</exception>
            <returns></returns>
        </member>
        <member name="T:YamlDotNet.Serialization.IObjectFactory">
            <summary>
            Creates instances of types.
            </summary>
            <remarks>
            This interface allows to provide a custom logic for creating instances during deserialization.
            </remarks>
        </member>
        <member name="M:YamlDotNet.Serialization.IObjectFactory.Create(System.Type)">
            <summary>
            Creates an instance of the specified type.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.IObjectGraphTraversalStrategy">
            <summary>
            Defines a strategy that walks through an object graph.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IObjectGraphTraversalStrategy.Traverse``1(YamlDotNet.Serialization.IObjectDescriptor,YamlDotNet.Serialization.IObjectGraphVisitor{``0},``0)">
            <summary>
            Traverses the specified object graph.
            </summary>
            <param name="graph">The graph.</param>
            <param name="visitor">An <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1"/> that is to be notified during the traversal.</param>
            <param name="context">A <typeparamref name="TContext" /> that will be passed to the <paramref name="visitor" />.</param>
        </member>
        <member name="T:YamlDotNet.Serialization.IObjectGraphVisitor`1">
            <summary>
            Defined the interface of a type that can be notified during an object graph traversal.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IObjectGraphVisitor`1.Enter(YamlDotNet.Serialization.IObjectDescriptor,`0)">
            <summary>
            Indicates whether the specified value should be entered. This allows the visitor to
            override the handling of a particular object or type.
            </summary>
            <param name="value">The value that is about to be entered.</param>
            <param name="context">The context that this implementation depend on.</param>
            <returns>If the value is to be entered, returns true; otherwise returns false;</returns>
        </member>
        <member name="M:YamlDotNet.Serialization.IObjectGraphVisitor`1.EnterMapping(YamlDotNet.Serialization.IObjectDescriptor,YamlDotNet.Serialization.IObjectDescriptor,`0)">
            <summary>
            Indicates whether the specified mapping should be entered. This allows the visitor to
            override the handling of a particular pair.
            </summary>
            <param name="key">The key of the mapping that is about to be entered.</param>
            <param name="value">The value of the mapping that is about to be entered.</param>
            <param name="context">The context that this implementation depend on.</param>
            <returns>If the mapping is to be entered, returns true; otherwise returns false;</returns>
        </member>
        <member name="M:YamlDotNet.Serialization.IObjectGraphVisitor`1.EnterMapping(YamlDotNet.Serialization.IPropertyDescriptor,YamlDotNet.Serialization.IObjectDescriptor,`0)">
            <summary>
            Indicates whether the specified mapping should be entered. This allows the visitor to
            override the handling of a particular pair. This overload should be invoked when the
            mapping is produced by an object's property.
            </summary>
            <param name="key">The <see cref="T:YamlDotNet.Serialization.IPropertyDescriptor"/> that provided access to <paramref name="value"/>.</param>
            <param name="value">The value of the mapping that is about to be entered.</param>
            <param name="context">The context that this implementation depend on.</param>
            <returns>If the mapping is to be entered, returns true; otherwise returns false;</returns>
        </member>
        <member name="M:YamlDotNet.Serialization.IObjectGraphVisitor`1.VisitScalar(YamlDotNet.Serialization.IObjectDescriptor,`0)">
            <summary>
            Notifies the visitor that a scalar value has been encountered.
            </summary>
            <param name="scalar">The value of the scalar.</param>
            <param name="context">The context that this implementation depend on.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.IObjectGraphVisitor`1.VisitMappingStart(YamlDotNet.Serialization.IObjectDescriptor,System.Type,System.Type,`0)">
            <summary>
            Notifies the visitor that the traversal of a mapping is about to begin.
            </summary>
            <param name="mapping">The value that corresponds to the mapping.</param>
            <param name="keyType">The static type of the keys of the mapping.</param>
            <param name="valueType">The static type of the values of the mapping.</param>
            <param name="context">The context that this implementation depend on.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.IObjectGraphVisitor`1.VisitMappingEnd(YamlDotNet.Serialization.IObjectDescriptor,`0)">
            <summary>
            Notifies the visitor that the traversal of a mapping has ended.
            </summary>
            <param name="mapping">The value that corresponds to the mapping.</param>
            <param name="context">The context that this implementation depend on.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.IObjectGraphVisitor`1.VisitSequenceStart(YamlDotNet.Serialization.IObjectDescriptor,System.Type,`0)">
            <summary>
            Notifies the visitor that the traversal of a sequence is about to begin.
            </summary>
            <param name="sequence">The value that corresponds to the sequence.</param>
            <param name="elementType">The static type of the elements of the sequence.</param>
            <param name="context">The context that this implementation depend on.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.IObjectGraphVisitor`1.VisitSequenceEnd(YamlDotNet.Serialization.IObjectDescriptor,`0)">
            <summary>
            Notifies the visitor that the traversal of a sequence has ended.
            </summary>
            <param name="sequence">The value that corresponds to the sequence.</param>
            <param name="context">The context that this implementation depend on.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax`1.InsteadOf``1">
            <summary>
            Registers the component in place of the already registered component of type <typeparamref name="TRegistrationType" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax`1.Before``1">
            <summary>
            Registers the component before the already registered component of type <typeparamref name="TRegistrationType" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax`1.After``1">
            <summary>
            Registers the component after the already registered component of type <typeparamref name="TRegistrationType" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax`1.OnTop">
            <summary>
            Registers the component before every other previously registered component.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax`1.OnBottom">
            <summary>
            Registers the component after every other previously registered component.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.ITrackingRegistrationLocationSelectionSyntax`1.InsteadOf``1">
            <summary>
            Registers the component in place of the already registered component of type <typeparamref name="TRegistrationType" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.ISerializer.Serialize(System.IO.TextWriter,System.Object)">
            <summary>
            Serializes the specified object.
            </summary>
            <param name="writer">The <see cref="T:System.IO.TextWriter" /> where to serialize the object.</param>
            <param name="graph">The object to serialize.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.ISerializer.Serialize(System.Object)">
            <summary>
            Serializes the specified object into a string.
            </summary>
            <param name="graph">The object to serialize.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.ISerializer.Serialize(System.IO.TextWriter,System.Object,System.Type)">
            <summary>
            Serializes the specified object.
            </summary>
            <param name="writer">The <see cref="T:System.IO.TextWriter" /> where to serialize the object.</param>
            <param name="graph">The object to serialize.</param>
            <param name="type">The static type of the object to serialize.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.ISerializer.Serialize(YamlDotNet.Core.IEmitter,System.Object)">
            <summary>
            Serializes the specified object.
            </summary>
            <param name="emitter">The <see cref="T:YamlDotNet.Core.IEmitter" /> where to serialize the object.</param>
            <param name="graph">The object to serialize.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.ISerializer.Serialize(YamlDotNet.Core.IEmitter,System.Object,System.Type)">
            <summary>
            Serializes the specified object.
            </summary>
            <param name="emitter">The <see cref="T:YamlDotNet.Core.IEmitter" /> where to serialize the object.</param>
            <param name="graph">The object to serialize.</param>
            <param name="type">The static type of the object to serialize.</param>
        </member>
        <member name="T:YamlDotNet.Serialization.ITypeInspector">
            <summary>
            Provides access to the properties of a type.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.ITypeInspector.GetProperties(System.Type,System.Object)">
            <summary>
            Gets all properties of the specified type.
            </summary>
            <param name="type">The type whose properties are to be enumerated.</param>
            <param name="container">The actual object of type <paramref name="type"/> whose properties are to be enumerated. Can be null.</param>
            <returns></returns>
        </member>
        <member name="M:YamlDotNet.Serialization.ITypeInspector.GetProperty(System.Type,System.Object,System.String,System.Boolean)">
            <summary>
            Gets the property of the type with the specified name.
            </summary>
            <param name="type">The type whose properties are to be searched.</param>
            <param name="container">The actual object of type <paramref name="type"/> whose properties are to be searched. Can be null.</param>
            <param name="name">The name of the property.</param>
            <param name="ignoreUnmatched">
            Determines if an exception or null should be returned if <paramref name="name"/> can't be
            found in <paramref name="type"/>
            </param>
            <returns></returns>
        </member>
        <member name="T:YamlDotNet.Serialization.ITypeResolver">
            <summary>
            Resolves the type of values.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.IYamlConvertible">
            <summary>
            Allows an object to customize how it is serialized and deserialized.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IYamlConvertible.Read(YamlDotNet.Core.IParser,System.Type,YamlDotNet.Serialization.ObjectDeserializer)">
            <summary>
            Reads this object's state from a YAML parser.
            </summary>
            <param name="parser">The parser where the object's state should be read from.</param>
            <param name="expectedType">The type that the deserializer is expecting.</param>
            <param name="nestedObjectDeserializer">
            A function that will use the current deserializer
            to read an object of the given type from the parser.
            </param>
        </member>
        <member name="M:YamlDotNet.Serialization.IYamlConvertible.Write(YamlDotNet.Core.IEmitter,YamlDotNet.Serialization.ObjectSerializer)">
            <summary>
            Writes this object's state to a YAML emitter.
            </summary>
            <param name="emitter">The emitter where the object's state should be written to.</param>
            <param name="nestedObjectSerializer">A function that will use the current serializer to write an object to the emitter.</param>
        </member>
        <member name="T:YamlDotNet.Serialization.ObjectDeserializer">
            <summary>
            Represents a function that is used to deserialize an object of the given type.
            </summary>
            <param name="type">The type that the deserializer should read.</param>
            <returns>Returns the object that was deserialized.</returns>
        </member>
        <member name="T:YamlDotNet.Serialization.ObjectSerializer">
            <summary>
            Represents a function that is used to serialize an object of the given type.
            </summary>
            <param name="value">The object to be serialized.</param>
            <param name="type">
            The type that should be considered when emitting the object.
            If null, the actual type of the <paramref name="value" /> is used.
            </param>
        </member>
        <member name="T:YamlDotNet.Serialization.IYamlSerializable">
            <summary>
            Allows an object to customize how it is serialized and deserialized.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IYamlSerializable.ReadYaml(YamlDotNet.Core.IParser)">
            <summary>
            Reads this object's state from a YAML parser.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IYamlSerializable.WriteYaml(YamlDotNet.Core.IEmitter)">
            <summary>
            Writes this object's state to a YAML emitter.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.IYamlTypeConverter">
            <summary>
            Allows to customize how a type is serialized and deserialized.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IYamlTypeConverter.Accepts(System.Type)">
            <summary>
            Gets a value indicating whether the current converter supports converting the specified type.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IYamlTypeConverter.ReadYaml(YamlDotNet.Core.IParser,System.Type)">
            <summary>
            Reads an object's state from a YAML parser.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.IYamlTypeConverter.WriteYaml(YamlDotNet.Core.IEmitter,System.Object,System.Type)">
            <summary>
            Writes the specified object's state to a YAML emitter.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.NamingConventions.CamelCaseNamingConvention">
            <summary>
            Convert the string with underscores (this_is_a_test) or hyphens (this-is-a-test) to 
            camel case (thisIsATest). Camel case is the same as Pascal case, except the first letter
            is lowercase.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.NamingConventions.HyphenatedNamingConvention">
            <summary>
            Convert the string from camelcase (thisIsATest) to a hyphenated (this-is-a-test) string
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.NamingConventions.LowerCaseNamingConvention">
            <summary>
            Convert the string with underscores (this_is_a_test) or hyphens (this-is-a-test) to 
            pascal case (ThisIsATest). Pascal case is the same as camel case, except the first letter
            is uppercase.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.NamingConventions.NullNamingConvention">
            <summary>
            Performs no naming conversion.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.NamingConventions.PascalCaseNamingConvention">
            <summary>
            Convert the string with underscores (this_is_a_test) or hyphens (this-is-a-test) to 
            pascal case (ThisIsATest). Pascal case is the same as camel case, except the first letter
            is uppercase.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.NamingConventions.UnderscoredNamingConvention">
            <summary>
            Convert the string from camelcase (thisIsATest) to a underscored (this_is_a_test) string
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.Nothing">
            <summary>
            An empty type for cases where a type needs to be provided but won't be used.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.ObjectFactories.DefaultObjectFactory">
            <summary>
            Creates objects using Activator.CreateInstance.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.ObjectFactories.LambdaObjectFactory">
            <summary>
            Creates objects using a Func{Type,object}"/>.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.ObjectGraphTraversalStrategies.FullObjectGraphTraversalStrategy">
            <summary>
            An implementation of <see cref="T:YamlDotNet.Serialization.IObjectGraphTraversalStrategy"/> that traverses
            readable properties, collections and dictionaries.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.ObjectGraphTraversalStrategies.RoundtripObjectGraphTraversalStrategy">
            <summary>
            An implementation of <see cref="T:YamlDotNet.Serialization.IObjectGraphTraversalStrategy"/> that traverses
            properties that are read/write, collections and dictionaries, while ensuring that
            the graph can be regenerated from the resulting document.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.ObjectGraphTraversalStrategyFactory">
            <summary>
            A factory method for creating <see cref="T:YamlDotNet.Serialization.IObjectGraphTraversalStrategy"/> instances
            </summary>
            <param name="typeInspector">The type inspector to be used by the traversal strategy.</param>
            <param name="typeResolver">The type resolver to be used by the traversal strategy.</param>
            <param name="typeConverters">The type converters to be used by the traversal strategy.</param>
            <param name="maximumRecursion">The maximum object depth to be supported by the traversal strategy.</param>
            <returns></returns>
        </member>
        <member name="T:YamlDotNet.Serialization.ObjectGraphVisitors.PreProcessingPhaseObjectGraphVisitorSkeleton">
            <summary>
            A base class that simplifies the correct implementation of <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.Serializer.#ctor">
            <summary>
            Initializes a new instance of <see cref="T:YamlDotNet.Serialization.Serializer" /> using the default configuration.
            </summary>
            <remarks>
            To customize the behavior of the serializer, use <see cref="T:YamlDotNet.Serialization.SerializerBuilder" />.
            </remarks>
        </member>
        <member name="M:YamlDotNet.Serialization.Serializer.#ctor(YamlDotNet.Serialization.IValueSerializer,YamlDotNet.Core.EmitterSettings)">
            <remarks>
            This constructor is private to discourage its use.
            To invoke it, call the <see cref="M:YamlDotNet.Serialization.Serializer.FromValueSerializer(YamlDotNet.Serialization.IValueSerializer,YamlDotNet.Core.EmitterSettings)"/> method.
            </remarks>
        </member>
        <member name="M:YamlDotNet.Serialization.Serializer.FromValueSerializer(YamlDotNet.Serialization.IValueSerializer,YamlDotNet.Core.EmitterSettings)">
            <summary>
            Creates a new <see cref="T:YamlDotNet.Serialization.Serializer" /> that uses the specified <see cref="T:YamlDotNet.Serialization.IValueSerializer" />.
            This method is available for advanced scenarios. The preferred way to customize the behavior of the
            deserializer is to use <see cref="T:YamlDotNet.Serialization.SerializerBuilder" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.Serializer.Serialize(System.IO.TextWriter,System.Object)">
            <summary>
            Serializes the specified object.
            </summary>
            <param name="writer">The <see cref="T:System.IO.TextWriter" /> where to serialize the object.</param>
            <param name="graph">The object to serialize.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.Serializer.Serialize(System.Object)">
            <summary>
            Serializes the specified object into a string.
            </summary>
            <param name="graph">The object to serialize.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.Serializer.Serialize(System.IO.TextWriter,System.Object,System.Type)">
            <summary>
            Serializes the specified object.
            </summary>
            <param name="writer">The <see cref="T:System.IO.TextWriter" /> where to serialize the object.</param>
            <param name="graph">The object to serialize.</param>
            <param name="type">The static type of the object to serialize.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.Serializer.Serialize(YamlDotNet.Core.IEmitter,System.Object)">
            <summary>
            Serializes the specified object.
            </summary>
            <param name="emitter">The <see cref="T:YamlDotNet.Core.IEmitter" /> where to serialize the object.</param>
            <param name="graph">The object to serialize.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.Serializer.Serialize(YamlDotNet.Core.IEmitter,System.Object,System.Type)">
            <summary>
            Serializes the specified object.
            </summary>
            <param name="emitter">The <see cref="T:YamlDotNet.Core.IEmitter" /> where to serialize the object.</param>
            <param name="graph">The object to serialize.</param>
            <param name="type">The static type of the object to serialize.</param>
        </member>
        <member name="T:YamlDotNet.Serialization.SerializerBuilder">
            <summary>
            Creates and configures instances of <see cref="T:YamlDotNet.Serialization.Serializer" />.
            This class is used to customize the behavior of <see cref="T:YamlDotNet.Serialization.Serializer" />. Use the relevant methods
            to apply customizations, then call <see cref="M:YamlDotNet.Serialization.SerializerBuilder.Build" /> to create an instance of the serializer
            with the desired customizations.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithMaximumRecursion(System.Int32)">
            <summary>
            Sets the maximum recursion that is allowed while traversing the object graph. The default value is 50.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithEventEmitter``1(System.Func{YamlDotNet.Serialization.IEventEmitter,``0})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IEventEmitter" /> to be used by the serializer.
            </summary>
            <param name="eventEmitterFactory">A function that instantiates the event emitter.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithEventEmitter``1(System.Func{YamlDotNet.Serialization.IEventEmitter,``0},System.Action{YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.IEventEmitter}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IEventEmitter" /> to be used by the serializer.
            </summary>
            <param name="eventEmitterFactory">A function that instantiates the event emitter.</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.IEventEmitter" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithEventEmitter``1(YamlDotNet.Serialization.WrapperFactory{YamlDotNet.Serialization.IEventEmitter,YamlDotNet.Serialization.IEventEmitter,``0},System.Action{YamlDotNet.Serialization.ITrackingRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.IEventEmitter}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IEventEmitter" /> to be used by the serializer.
            </summary>
            <param name="eventEmitterFactory">A function that instantiates the event emitter based on a previously registered <see cref="T:YamlDotNet.Serialization.IEventEmitter" />.</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.IEventEmitter" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithoutEventEmitter``1">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.IEventEmitter" /> of type <typeparam name="TEventEmitter" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithoutEventEmitter(System.Type)">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.IEventEmitter" /> of type <param name="eventEmitterType" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithTagMapping(YamlDotNet.Core.TagName,System.Type)">
            <summary>
            Registers a tag mapping.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithoutTagMapping(System.Type)">
            <summary>
            Unregisters an existing tag mapping.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.EnsureRoundtrip">
            <summary>
            Ensures that it will be possible to deserialize the serialized objects.
            This option will force the emission of tags and emit only properties with setters.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.DisableAliases">
            <summary>
            Specifies that, if the same object appears more than once in the
            serialization graph, it will be serialized each time instead of just once.
            </summary>
            <remarks>
            If the serialization graph contains circular references and this flag is set,
            a StackOverflowException will be thrown.
            If this flag is not set, there is a performance penalty because the entire
            object graph must be walked twice.
            </remarks>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.EmitDefaults">
            <summary>
            Forces every value to be serialized, even if it is the default value for that type.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.ConfigureDefaultValuesHandling(YamlDotNet.Serialization.DefaultValuesHandling)">
            <summary>
            Configures how properties with default and null values should be handled. The default value is DefaultValuesHandling.Preserve
            </summary>
            <remarks>
            If more control is needed, create a class that extends from ChainedObjectGraphVisitor and override its EnterMapping methods.
            Then register it as follows: 
            WithEmissionPhaseObjectGraphVisitor(args => new MyDefaultHandlingStrategy(args.InnerVisitor));
            </remarks>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.JsonCompatible">
            <summary>
            Ensures that the result of the serialization is valid JSON.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithPreProcessingPhaseObjectGraphVisitor``1(``0)">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> to be used by the serializer
            before emitting an object graph.
            </summary>
            <remarks>
            Registering a visitor in the pre-processing phase enables to traverse the object graph once
            before actually emitting it. This allows a visitor to collect information about the graph that
            can be used later by another visitor registered in the emission phase.
            </remarks>
            <param name="objectGraphVisitor">The type inspector.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithPreProcessingPhaseObjectGraphVisitor``1(``0,System.Action{YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.IObjectGraphVisitor{YamlDotNet.Serialization.Nothing}}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> to be used by the serializer
            before emitting an object graph.
            </summary>
            <remarks>
            Registering a visitor in the pre-processing phase enables to traverse the object graph once
            before actually emitting it. This allows a visitor to collect information about the graph that
            can be used later by another visitor registered in the emission phase.
            </remarks>
            <param name="objectGraphVisitor">The type inspector.</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithPreProcessingPhaseObjectGraphVisitor``1(YamlDotNet.Serialization.WrapperFactory{YamlDotNet.Serialization.IObjectGraphVisitor{YamlDotNet.Serialization.Nothing},``0},System.Action{YamlDotNet.Serialization.ITrackingRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.IObjectGraphVisitor{YamlDotNet.Serialization.Nothing}}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> to be used by the serializer
            before emitting an object graph.
            </summary>
            <remarks>
            Registering a visitor in the pre-processing phase enables to traverse the object graph once
            before actually emitting it. This allows a visitor to collect information about the graph that
            can be used later by another visitor registered in the emission phase.
            </remarks>
            <param name="objectGraphVisitorFactory">A factory that creates the <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> based on a previously registered <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" />.</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithoutPreProcessingPhaseObjectGraphVisitor``1">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> of type <typeparam name="TObjectGraphVisitor" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithoutPreProcessingPhaseObjectGraphVisitor(System.Type)">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> of type <param name="objectGraphVisitorType" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithObjectGraphTraversalStrategyFactory(YamlDotNet.Serialization.ObjectGraphTraversalStrategyFactory)">
            <summary>
            Registers an <see cref="T:YamlDotNet.Serialization.ObjectGraphTraversalStrategyFactory"/> to be used by the serializer
            while traversing the object graph.
            </summary>
            <param name="objectGraphTraversalStrategyFactory">A function that instantiates the traversal strategy.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithEmissionPhaseObjectGraphVisitor``1(System.Func{YamlDotNet.Serialization.EmissionPhaseObjectGraphVisitorArgs,``0})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> to be used by the serializer
            while emitting an object graph.
            </summary>
            <param name="objectGraphVisitorFactory">A function that instantiates the type inspector.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithEmissionPhaseObjectGraphVisitor``1(System.Func{YamlDotNet.Serialization.EmissionPhaseObjectGraphVisitorArgs,``0},System.Action{YamlDotNet.Serialization.IRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.IObjectGraphVisitor{YamlDotNet.Core.IEmitter}}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> to be used by the serializer
            while emitting an object graph.
            </summary>
            <param name="objectGraphVisitorFactory">A function that instantiates the type inspector.</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithEmissionPhaseObjectGraphVisitor``1(YamlDotNet.Serialization.WrapperFactory{YamlDotNet.Serialization.EmissionPhaseObjectGraphVisitorArgs,YamlDotNet.Serialization.IObjectGraphVisitor{YamlDotNet.Core.IEmitter},``0},System.Action{YamlDotNet.Serialization.ITrackingRegistrationLocationSelectionSyntax{YamlDotNet.Serialization.IObjectGraphVisitor{YamlDotNet.Core.IEmitter}}})">
            <summary>
            Registers an additional <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> to be used by the serializer
            while emitting an object graph.
            </summary>
            <param name="objectGraphVisitorFactory">A function that instantiates the type inspector based on a previously registered <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" />.</param>
            <param name="where">Configures the location where to insert the <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /></param>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithoutEmissionPhaseObjectGraphVisitor``1">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> of type <typeparam name="TObjectGraphVisitor" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithoutEmissionPhaseObjectGraphVisitor(System.Type)">
            <summary>
            Unregisters an existing <see cref="T:YamlDotNet.Serialization.IObjectGraphVisitor`1" /> of type <param name="objectGraphVisitorType" />.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.WithIndentedSequences">
            <summary>
            Creates sequences with extra indentation
            </summary>
            <example>
             list:
               - item
               - item
            </example>
            <returns></returns>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.Build">
            <summary>
            Creates a new <see cref="T:YamlDotNet.Serialization.Serializer" /> according to the current configuration.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.SerializerBuilder.BuildValueSerializer">
            <summary>
            Creates a new <see cref="T:YamlDotNet.Serialization.IValueDeserializer" /> that implements the current configuration.
            This method is available for advanced scenarios. The preferred way to customize the behavior of the
            deserializer is to use the <see cref="M:YamlDotNet.Serialization.SerializerBuilder.Build" /> method.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.StreamFragment">
            <summary>
            An object that contains part of a YAML stream.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.StreamFragment.Events">
            <summary>
            Gets or sets the events.
            </summary>
            <value>The events.</value>
        </member>
        <member name="M:YamlDotNet.Serialization.StreamFragment.YamlDotNet#Serialization#IYamlConvertible#Read(YamlDotNet.Core.IParser,System.Type,YamlDotNet.Serialization.ObjectDeserializer)">
            <summary>
            Reads this object's state from a YAML parser.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.StreamFragment.YamlDotNet#Serialization#IYamlConvertible#Write(YamlDotNet.Core.IEmitter,YamlDotNet.Serialization.ObjectSerializer)">
            <summary>
            Writes this object's state to a YAML emitter.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.TagMappings">
            <summary>
            Contains mappings between tags and types.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.TagMappings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Serialization.TagMappings"/> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.TagMappings.#ctor(System.Collections.Generic.IDictionary{System.String,System.Type})">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Serialization.TagMappings"/> class.
            </summary>
            <param name="mappings">The mappings.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.TagMappings.Add(System.String,System.Type)">
            <summary>
            Adds the specified tag.
            </summary>
            <param name="tag">The tag.</param>
            <param name="mapping">The mapping.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.TagMappings.GetMapping(System.String)">
            <summary>
            Gets the mapping.
            </summary>
            <param name="tag">The tag.</param>
            <returns></returns>
        </member>
        <member name="T:YamlDotNet.Serialization.TypeInspectors.CachedTypeInspector">
            <summary>
            Wraps another <see cref="T:YamlDotNet.Serialization.ITypeInspector"/> and applies caching.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.TypeInspectors.CompositeTypeInspector">
            <summary>
            Aggregates the results from multiple <see cref="T:YamlDotNet.Serialization.ITypeInspector" /> into a single one.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.TypeInspectors.NamingConventionTypeInspector">
            <summary>
            Wraps another <see cref="T:YamlDotNet.Serialization.ITypeInspector"/> and applies a
            naming convention to the names of the properties.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.TypeInspectors.ReadableAndWritablePropertiesTypeInspector">
            <summary>
            Returns the properties of a type that are both readable and writable.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.TypeInspectors.ReadableFieldsTypeInspector">
            <summary>
            Returns the properties and fields of a type that are readable.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.TypeInspectors.ReadablePropertiesTypeInspector">
            <summary>
            Returns the properties of a type that are readable.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.TypeResolvers.DynamicTypeResolver">
            <summary>
            The type returned will be the actual type of the value, if available.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.TypeResolvers.StaticTypeResolver">
            <summary>
            The type returned will always be the static type.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.Utilities.IPostDeserializationCallback">
            <summary>
            Indicates that a class used as deserialization state
            needs to be notified after deserialization.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.ObjectAnchorCollection.Add(System.String,System.Object)">
            <summary>
            Adds the specified anchor.
            </summary>
            <param name="anchor">The anchor.</param>
            <param name="object">The @object.</param>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.ObjectAnchorCollection.TryGetAnchor(System.Object,System.String@)">
            <summary>
            Gets the anchor for the specified object.
            </summary>
            <param name="object">The object.</param>
            <param name="anchor">The anchor.</param>
            <returns></returns>
        </member>
        <member name="P:YamlDotNet.Serialization.Utilities.ObjectAnchorCollection.Item(System.String)">
            <summary>
            Gets the <see cref="T:System.Object"/> with the specified anchor.
            </summary>
            <value></value>
        </member>
        <member name="T:YamlDotNet.Serialization.Utilities.SerializerState">
            <summary>
            A generic container that is preserved during the entire deserialization process.
            Any disposable object added to this collection will be disposed when this object is disposed.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.SerializerState.OnDeserialization">
            <summary>
            Invokes <see cref="M:YamlDotNet.Serialization.Utilities.IPostDeserializationCallback.OnDeserialization" /> on all
            objects added to this collection that implement <see cref="T:YamlDotNet.Serialization.Utilities.IPostDeserializationCallback" />.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.Utilities.StringExtensions">
            <summary>
            Various string extension methods
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.StringExtensions.ToCamelCase(System.String)">
            <summary>
            Convert the string with underscores (this_is_a_test) or hyphens (this-is-a-test) to 
            camel case (thisIsATest). Camel case is the same as Pascal case, except the first letter
            is lowercase.
            </summary>
            <param name="str">String to convert</param>
            <returns>Converted string</returns>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.StringExtensions.ToPascalCase(System.String)">
            <summary>
            Convert the string with underscores (this_is_a_test) or hyphens (this-is-a-test) to 
            pascal case (ThisIsATest). Pascal case is the same as camel case, except the first letter
            is uppercase.
            </summary>
            <param name="str">String to convert</param>
            <returns>Converted string</returns>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.StringExtensions.FromCamelCase(System.String,System.String)">
            <summary>
            Convert the string from camelcase (thisIsATest) to a hyphenated (this-is-a-test) or 
            underscored (this_is_a_test) string
            </summary>
            <param name="str">String to convert</param>
            <param name="separator">Separator to use between segments</param>
            <returns>Converted string</returns>
        </member>
        <member name="T:YamlDotNet.Serialization.Utilities.TypeConverter">
            <summary>
            Performs type conversions using every standard provided by the .NET library.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.TypeConverter.ChangeType``1(System.Object)">
            <summary>
            Converts the specified value.
            </summary>
            <typeparam name="T">The type to which the value is to be converted.</typeparam>
            <param name="value">The value to convert.</param>
            <returns></returns>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.TypeConverter.ChangeType``1(System.Object,System.IFormatProvider)">
            <summary>
            Converts the specified value.
            </summary>
            <typeparam name="T">The type to which the value is to be converted.</typeparam>
            <param name="value">The value to convert.</param>
            <param name="provider">The provider.</param>
            <returns></returns>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.TypeConverter.ChangeType``1(System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts the specified value.
            </summary>
            <typeparam name="T">The type to which the value is to be converted.</typeparam>
            <param name="value">The value to convert.</param>
            <param name="culture">The culture.</param>
            <returns></returns>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.TypeConverter.ChangeType(System.Object,System.Type)">
            <summary>
            Converts the specified value using the invariant culture.
            </summary>
            <param name="value">The value to convert.</param>
            <param name="destinationType">The type to which the value is to be converted.</param>
            <returns></returns>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.TypeConverter.ChangeType(System.Object,System.Type,System.IFormatProvider)">
            <summary>
            Converts the specified value.
            </summary>
            <param name="value">The value to convert.</param>
            <param name="destinationType">The type to which the value is to be converted.</param>
            <param name="provider">The format provider.</param>
            <returns></returns>
        </member>
        <member name="M:YamlDotNet.Serialization.Utilities.TypeConverter.ChangeType(System.Object,System.Type,System.Globalization.CultureInfo)">
            <summary>
            Converts the specified value.
            </summary>
            <param name="value">The value to convert.</param>
            <param name="destinationType">The type to which the value is to be converted.</param>
            <param name="culture">The culture.</param>
            <returns></returns>
        </member>
        <member name="T:YamlDotNet.Serialization.YamlAttributeOverrides">
            <summary>
            Define a collection of YamlAttribute Overrides for pre-defined object types.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.YamlAttributeOverrides.AttributeMapping.Matches(System.Type)">
            <summary>
            Checks whether this mapping matches the specified type, and returns a value indicating the match priority.
            </summary>
            <returns>The priority of the match. Higher values have more priority. Zero indicates no match.</returns>
        </member>
        <member name="M:YamlDotNet.Serialization.YamlAttributeOverrides.Add(System.Type,System.String,System.Attribute)">
            <summary>
            Adds a Member Attribute Override
            </summary>
            <param name="type">Type</param>
            <param name="member">Class Member</param>
            <param name="attribute">Overriding Attribute</param>
        </member>
        <member name="M:YamlDotNet.Serialization.YamlAttributeOverrides.Clone">
            <summary>
            Creates a copy of this instance.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.YamlAttributeOverrides.Add``1(System.Linq.Expressions.Expression{System.Func{``0,System.Object}},System.Attribute)">
            <summary>
            Adds a Member Attribute Override
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.YamlAttributeOverridesInspector">
            <summary>
            Applies the Yaml attribute overrides to another <see cref="T:YamlDotNet.Serialization.ITypeInspector"/>.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.YamlAttributesTypeInspector">
            <summary>
            Applies the Yaml* attributes to another <see cref="T:YamlDotNet.Serialization.ITypeInspector"/>.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.YamlIgnoreAttribute">
            <summary>
            Instructs the YamlSerializer not to serialize the public field or public read/write property value.
            </summary>
        </member>
        <member name="T:YamlDotNet.Serialization.YamlMemberAttribute">
            <summary>
            Provides special Yaml serialization instructions.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.YamlMemberAttribute.Description">
            <summary>
            Decription/Comment about this property.
            When set, a comment will be emitted when serializing this member.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.YamlMemberAttribute.SerializeAs">
            <summary>
            Specifies that this property should be serialized as the given type, rather than using the actual runtime value's type.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.YamlMemberAttribute.Order">
            <summary>
            Specifies the order priority of this property.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.YamlMemberAttribute.Alias">
            <summary>
            Instructs the <see cref="T:YamlDotNet.Serialization.Deserializer"/> to use a different field name for serialization.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.YamlMemberAttribute.ApplyNamingConventions">
            <summary>
            When false, naming conventions will not be applied to this member. Defaults to true.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.YamlMemberAttribute.ScalarStyle">
            <summary>
            Specifies the scalar style of the property when serialized. This will only affect the serialization of scalar properties.
            </summary>
        </member>
        <member name="P:YamlDotNet.Serialization.YamlMemberAttribute.DefaultValuesHandling">
            <summary>
            Overrides how null and default values should be handled for this property.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.YamlMemberAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Serialization.YamlMemberAttribute" /> class.
            </summary>
        </member>
        <member name="M:YamlDotNet.Serialization.YamlMemberAttribute.#ctor(System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:YamlDotNet.Serialization.YamlMemberAttribute" /> class.
            </summary>
            <param name="serializeAs">Specifies that this property should be serialized as the given type, rather than using the actual runtime value's type.</param>
        </member>
        <member name="M:YamlDotNet.ReflectionExtensions.HasDefaultConstructor(System.Type)">
            <summary>
            Determines whether the specified type has a default constructor.
            </summary>
            <param name="type">The type.</param>
            <returns>
                <c>true</c> if the type has a default constructor; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>
                Specifies that <see langword="null"/> is allowed as an input even if the
                corresponding type disallows it.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.AllowNullAttribute.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute"/> class.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>
                Specifies that <see langword="null"/> is disallowed as an input even if the
                corresponding type allows it.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DisallowNullAttribute.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute"/> class.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>
                Specifies that a method that will never return under any circumstance.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute"/> class.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>
                Specifies that the method will not return if the associated <see cref="T:System.Boolean"/>
                parameter is passed the specified value.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>
                Gets the condition parameter value.
                Code after the method is considered unreachable by diagnostics if the argument
                to the associated parameter matches this value.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>
                Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute"/>
                class with the specified parameter value.
            </summary>
            <param name="parameterValue">
                The condition parameter value.
                Code after the method is considered unreachable by diagnostics if the argument
                to the associated parameter matches this value.
            </param>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>
                Specifies that an output may be <see langword="null"/> even if the
                corresponding type disallows it.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullAttribute.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute"/> class.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>
                Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, 
                the parameter may be <see langword="null"/> even if the corresponding type disallows it.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>
                Gets the return value condition.
                If the method returns this value, the associated parameter may be <see langword="null"/>.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>
                 Initializes the attribute with the specified return value condition.
            </summary>
            <param name="returnValue">
                The return value condition.
                If the method returns this value, the associated parameter may be <see langword="null"/>.
            </param>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>
                Specifies that an output is not <see langword="null"/> even if the
                corresponding type allows it.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullAttribute.#ctor">
            <summary>
                Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.NotNullAttribute"/> class.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>
                Specifies that the output will be non-<see langword="null"/> if the
                named parameter is non-<see langword="null"/>.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>
                Gets the associated parameter name.
                The output will be non-<see langword="null"/> if the argument to the
                parameter specified is non-<see langword="null"/>.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>
                Initializes the attribute with the associated parameter name.
            </summary>
            <param name="parameterName">
                The associated parameter name.
                The output will be non-<see langword="null"/> if the argument to the
                parameter specified is non-<see langword="null"/>.
            </param>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>
                Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>,
                the parameter will not be <see langword="null"/> even if the corresponding type allows it.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>
                Gets the return value condition.
                If the method returns this value, the associated parameter will not be <see langword="null"/>.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>
                Initializes the attribute with the specified return value condition.
            </summary>
            <param name="returnValue">
                The return value condition.
                If the method returns this value, the associated parameter will not be <see langword="null"/>.
            </param>
        </member>
    </members>
</doc>
