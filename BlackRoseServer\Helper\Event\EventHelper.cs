﻿using CustomPlayerEffects;
using InventorySystem.Items.Firearms;
using MapGeneration;
using MEC;
using PlayerRoles;
using PlayerRoles.PlayableScps.Scp079;
using Respawning.Waves;
using System;
using System.Collections.Generic;
using System.Linq;

namespace HelpSense.Helper.Event
{
    public static class EventHelper
    {
        public static Random Random = new Random();

        public static void OnTeamRespawn(SpawnableWaveBase spawnableWaveBase, List<ReferenceHub> referenceHubs)
        {

        }
    }
}
