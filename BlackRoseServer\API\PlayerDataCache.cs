using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;

namespace BlackRoseServer.API
{
    /// <summary>
    /// 玩家数据缓存系统 - 在回合中暂存数据变更，回合结束时批量写入数据库
    /// </summary>
    public class PlayerDataCache
    {
        private static PlayerDataCache _instance;
        public static PlayerDataCache Instance => _instance ??= new PlayerDataCache();

        /// <summary>
        /// 缓存的玩家数据变更
        /// </summary>
        private readonly ConcurrentDictionary<string, CachedPlayerData> _cachedData = new();

        /// <summary>
        /// 需要在数据库中创建的新玩家
        /// </summary>
        private readonly ConcurrentHashSet<string> _newPlayers = new();

        /// <summary>
        /// 是否在回合中（回合中使用缓存，回合外直接写入数据库）
        /// </summary>
        private volatile bool _isInRound = false;

        /// <summary>
        /// 缓存的玩家数据
        /// </summary>
        public class CachedPlayerData
        {
            public string UserId { get; set; }
            public string NickName { get; set; }
            public int ExperienceDelta { get; set; } = 0; // 经验变化量
            public int? FinalExperience { get; set; } = null; // 最终经验值（如果有SetXP操作）
            public int? FinalLevel { get; set; } = null; // 最终等级（如果有升级）
            public DateTime LastUpdated { get; set; } = DateTime.Now;
            public bool IsNewPlayer { get; set; } = false;
        }

        /// <summary>
        /// 开始新回合
        /// </summary>
        public void StartRound()
        {
            _isInRound = true;
            _cachedData.Clear();
            _newPlayers.Clear();
            Logger.Info("PlayerDataCache: 回合开始，启用数据缓存模式");
        }

        /// <summary>
        /// 结束回合，批量写入数据库
        /// </summary>
        public void EndRound()
        {
            try
            {
                _isInRound = false;
                Logger.Info($"PlayerDataCache: 回合结束，开始批量写入 {_cachedData.Count} 个玩家数据");

                if (_cachedData.IsEmpty)
                {
                    Logger.Info("PlayerDataCache: 没有缓存数据需要写入");
                    return;
                }

                // 批量写入数据库
                BatchWriteToDatabase();

                // 清理缓存
                _cachedData.Clear();
                _newPlayers.Clear();

                Logger.Info("PlayerDataCache: 批量写入完成，缓存已清理");
            }
            catch (Exception ex)
            {
                Logger.Error($"PlayerDataCache: 批量写入失败: {ex.Message}");
                Logger.Debug($"PlayerDataCache详细错误: {ex}");
            }
        }

        /// <summary>
        /// 添加经验（缓存模式）
        /// </summary>
        public void AddExperience(string userId, string nickName, int amount)
        {
            if (!_isInRound)
            {
                // 回合外直接写入数据库
                var player = Player.Get(userId: userId);
                if (player != null)
                {
                    player.AddXP(amount);
                }
                return;
            }

            // 回合中使用缓存
            _cachedData.AddOrUpdate(userId, 
                new CachedPlayerData 
                { 
                    UserId = userId, 
                    NickName = nickName, 
                    ExperienceDelta = amount,
                    LastUpdated = DateTime.Now
                },
                (key, existing) => 
                {
                    existing.ExperienceDelta += amount;
                    existing.LastUpdated = DateTime.Now;
                    existing.NickName = nickName; // 更新昵称
                    return existing;
                });

            Logger.Debug($"PlayerDataCache: 缓存经验变更 - 玩家: {nickName}, 变更: +{amount}");
        }

        /// <summary>
        /// 设置经验（缓存模式）
        /// </summary>
        public void SetExperience(string userId, string nickName, int amount)
        {
            if (!_isInRound)
            {
                // 回合外直接写入数据库
                var player = Player.Get(userId: userId);
                if (player != null)
                {
                    player.SetXP(amount);
                }
                return;
            }

            // 回合中使用缓存
            _cachedData.AddOrUpdate(userId,
                new CachedPlayerData
                {
                    UserId = userId,
                    NickName = nickName,
                    FinalExperience = amount,
                    LastUpdated = DateTime.Now
                },
                (key, existing) =>
                {
                    existing.FinalExperience = amount;
                    existing.ExperienceDelta = 0; // 清除增量，使用最终值
                    existing.LastUpdated = DateTime.Now;
                    existing.NickName = nickName;
                    return existing;
                });

            Logger.Debug($"PlayerDataCache: 缓存经验设置 - 玩家: {nickName}, 设置为: {amount}");
        }

        /// <summary>
        /// 标记新玩家
        /// </summary>
        public void MarkAsNewPlayer(string userId, string nickName)
        {
            _newPlayers.Add(userId);
            
            _cachedData.AddOrUpdate(userId,
                new CachedPlayerData
                {
                    UserId = userId,
                    NickName = nickName,
                    IsNewPlayer = true,
                    LastUpdated = DateTime.Now
                },
                (key, existing) =>
                {
                    existing.IsNewPlayer = true;
                    existing.NickName = nickName;
                    return existing;
                });

            Logger.Debug($"PlayerDataCache: 标记新玩家 - {nickName}");
        }

        /// <summary>
        /// 获取缓存统计信息
        /// </summary>
        public string GetCacheStats()
        {
            return $"缓存玩家数: {_cachedData.Count}, 新玩家数: {_newPlayers.Count}, 回合状态: {(_isInRound ? "进行中" : "结束")}";
        }

        /// <summary>
        /// 批量写入数据库
        /// </summary>
        private void BatchWriteToDatabase()
        {
            var successCount = 0;
            var errorCount = 0;
            var skippedCount = 0;

            foreach (var kvp in _cachedData)
            {
                try
                {
                    var userId = kvp.Key;
                    var cachedData = kvp.Value;

                    // 获取玩家对象
                    var player = Player.Get(userId: userId);
                    if (player == null)
                    {
                        Logger.Debug($"PlayerDataCache: 玩家 {cachedData.NickName} 已离线，跳过数据写入");
                        continue;
                    }

                    // 处理新玩家 - 总是允许新玩家数据写入
                    if (cachedData.IsNewPlayer)
                    {
                        DirectSavePlayer(player);
                    }

                    // 检查回合数据写入配置
                    if (!Config.Instance.EnableRoundDataWrite)
                    {
                        // 如果禁用回合数据写入，跳过经验相关的更新
                        if (cachedData.FinalExperience.HasValue || cachedData.ExperienceDelta != 0)
                        {
                            skippedCount++;
                            Logger.Debug($"PlayerDataCache: 回合数据写入已禁用，跳过玩家 {cachedData.NickName} 的经验更新");
                            continue;
                        }
                    }

                    // 处理经验变更 - 直接调用数据库API避免递归
                    if (cachedData.FinalExperience.HasValue)
                    {
                        // 使用最终经验值
                        DirectSetExperience(player, cachedData.FinalExperience.Value);
                    }
                    else if (cachedData.ExperienceDelta != 0)
                    {
                        // 使用增量
                        DirectAddExperience(player, cachedData.ExperienceDelta);
                    }

                    successCount++;
                }
                catch (Exception ex)
                {
                    errorCount++;
                    Logger.Error($"PlayerDataCache: 写入玩家数据失败 - {kvp.Value.NickName}: {ex.Message}");
                }
            }

            var logMessage = $"PlayerDataCache: 批量写入完成 - 成功: {successCount}, 失败: {errorCount}";
            if (skippedCount > 0)
            {
                logMessage += $", 跳过回合数据: {skippedCount}";
            }
            Logger.Info(logMessage);
        }

        /// <summary>
        /// 强制刷新缓存到数据库（紧急情况使用）
        /// </summary>
        public void ForceFlush()
        {
            if (_cachedData.IsEmpty)
                return;

            Logger.Warn("PlayerDataCache: 强制刷新缓存到数据库");
            BatchWriteToDatabase();
            _cachedData.Clear();
            _newPlayers.Clear();
        }

        /// <summary>
        /// 清空缓存（不写入数据库）
        /// </summary>
        public void ClearCache()
        {
            var cachedCount = _cachedData.Count;
            var newPlayerCount = _newPlayers.Count;

            _cachedData.Clear();
            _newPlayers.Clear();

            Logger.Warn($"PlayerDataCache: 缓存已清空 - 丢弃了 {cachedCount} 个玩家数据和 {newPlayerCount} 个新玩家记录");
        }

        /// <summary>
        /// 直接保存玩家到数据库（避免递归）
        /// </summary>
        private void DirectSavePlayer(Player player)
        {
            try
            {
                // 直接调用MySQLService，避免通过扩展方法导致的递归
                if (!MySQLService.Instance.IsDatabaseEnabled())
                    return;

                // 检查玩家是否已存在
                var checkSql = "SELECT COUNT(*) FROM players WHERE UserId = @UserId";
                var checkParams = new MySql.Data.MySqlClient.MySqlParameter[]
                {
                    new MySql.Data.MySqlClient.MySqlParameter("@UserId", player.UserId)
                };

                var exists = Convert.ToInt32(MySQLService.Instance.ExecuteScalar(checkSql, checkParams)) > 0;

                if (!exists)
                {
                    // 创建新玩家记录
                    var insertSql = @"
                        INSERT INTO players (UserId, NickName, Level, Experience, PermissionName)
                        VALUES (@UserId, @NickName, 1, 1, '')";

                    var insertParams = new MySql.Data.MySqlClient.MySqlParameter[]
                    {
                        new MySql.Data.MySqlClient.MySqlParameter("@UserId", player.UserId),
                        new MySql.Data.MySqlClient.MySqlParameter("@NickName", player.Nickname ?? "Unknown")
                    };

                    MySQLService.Instance.ExecuteNonQuery(insertSql, insertParams);
                    Logger.Debug($"DirectSavePlayer: 新玩家 {player.Nickname} 已创建");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"DirectSavePlayer失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 直接添加经验到数据库（避免递归）
        /// </summary>
        private void DirectAddExperience(Player player, int amount)
        {
            try
            {
                if (!MySQLService.Instance.IsDatabaseEnabled() || amount == 0)
                    return;

                // 确保玩家存在
                DirectSavePlayer(player);

                // 获取当前经验
                var getCurrentSql = "SELECT Experience FROM players WHERE UserId = @UserId";
                var getCurrentParams = new MySql.Data.MySqlClient.MySqlParameter[]
                {
                    new MySql.Data.MySqlClient.MySqlParameter("@UserId", player.UserId)
                };

                var currentExp = Convert.ToInt32(MySQLService.Instance.ExecuteScalar(getCurrentSql, getCurrentParams) ?? 1);
                var newExp = currentExp + amount;

                // 计算新等级
                var newLevel = ExperienceCalculator.CalculateLevel(newExp);

                // 更新数据库
                var updateSql = @"
                    UPDATE players
                    SET Experience = @Experience, Level = @Level, LastUpdated = NOW()
                    WHERE UserId = @UserId";

                var updateParams = new MySql.Data.MySqlClient.MySqlParameter[]
                {
                    new MySql.Data.MySqlClient.MySqlParameter("@Experience", newExp),
                    new MySql.Data.MySqlClient.MySqlParameter("@Level", newLevel),
                    new MySql.Data.MySqlClient.MySqlParameter("@UserId", player.UserId)
                };

                MySQLService.Instance.ExecuteNonQuery(updateSql, updateParams);
                Logger.Debug($"DirectAddExperience: 玩家 {player.Nickname} 增加 {amount} 经验，总经验: {newExp}");
            }
            catch (Exception ex)
            {
                Logger.Error($"DirectAddExperience失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 直接设置经验到数据库（避免递归）
        /// </summary>
        private void DirectSetExperience(Player player, int amount)
        {
            try
            {
                if (!MySQLService.Instance.IsDatabaseEnabled())
                    return;

                // 确保玩家存在
                DirectSavePlayer(player);

                // 计算等级
                var newLevel = ExperienceCalculator.CalculateLevel(amount);

                // 更新数据库
                var updateSql = @"
                    UPDATE players
                    SET Experience = @Experience, Level = @Level, LastUpdated = NOW()
                    WHERE UserId = @UserId";

                var updateParams = new MySql.Data.MySqlClient.MySqlParameter[]
                {
                    new MySql.Data.MySqlClient.MySqlParameter("@Experience", amount),
                    new MySql.Data.MySqlClient.MySqlParameter("@Level", newLevel),
                    new MySql.Data.MySqlClient.MySqlParameter("@UserId", player.UserId)
                };

                MySQLService.Instance.ExecuteNonQuery(updateSql, updateParams);
                Logger.Debug($"DirectSetExperience: 玩家 {player.Nickname} 设置经验为 {amount}");
            }
            catch (Exception ex)
            {
                Logger.Error($"DirectSetExperience失败: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// 线程安全的HashSet
    /// </summary>
    public class ConcurrentHashSet<T> : IDisposable
    {
        private readonly HashSet<T> _hashSet = new();
        private readonly object _lock = new();

        public void Add(T item)
        {
            lock (_lock)
            {
                _hashSet.Add(item);
            }
        }

        public bool Contains(T item)
        {
            lock (_lock)
            {
                return _hashSet.Contains(item);
            }
        }

        public void Clear()
        {
            lock (_lock)
            {
                _hashSet.Clear();
            }
        }

        public int Count
        {
            get
            {
                lock (_lock)
                {
                    return _hashSet.Count;
                }
            }
        }

        public void Dispose()
        {
            lock (_lock)
            {
                _hashSet.Clear();
            }
        }
    }
}
