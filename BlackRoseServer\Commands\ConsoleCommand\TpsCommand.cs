using System;
using CommandSystem;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using RemoteAdmin;

namespace BlackRoseServer.Commands.ConsoleCommand
{
    /// <summary>
    /// TPS查看命令 - 玩家和服务端控制台可用
    /// </summary>
    [CommandHandler(typeof(ClientCommandHandler))]
    [CommandHandler(typeof(GameConsoleCommandHandler))]
    public class TpsCommand : ICommand
    {
        public string Command => "tps";
        public string[] Aliases => new[] { "tickrate", "performance" };
        public string Description => "查看服务器TPS";

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            try
            {
                // 检查是否为玩家命令
                Player player = null;
                bool isPlayerCommand = false;

                if (sender is PlayerCommandSender playerSender)
                {
                    isPlayerCommand = true;
                    player = Player.Get(playerSender.ReferenceHub);
                    if (player == null)
                    {
                        response = "无法获取玩家信息";
                        return false;
                    }
                }

                // 获取服务器TPS信息
                var currentTps = Server.Tps;
                var maxTps = Server.MaxTps;
                var tpsPercentage = maxTps > 0 ? (currentTps / maxTps) * 100 : 0;

                // 根据TPS状况确定颜色和状态
                string statusColor;
                string statusText;

                if (tpsPercentage >= 95)
                {
                    statusColor = "#00FF00"; // 绿色 - 优秀
                    statusText = "优秀";
                }
                else if (tpsPercentage >= 80)
                {
                    statusColor = "#FFFF00"; // 黄色 - 良好
                    statusText = "良好";
                }
                else if (tpsPercentage >= 60)
                {
                    statusColor = "#FFA500"; // 橙色 - 一般
                    statusText = "一般";
                }
                else
                {
                    statusColor = "#FF0000"; // 红色 - 较差
                    statusText = "较差";
                }

                // 构建响应消息
                if (isPlayerCommand)
                {
                    // 玩家端显示 - 使用颜色标签
                    response = $"<color=#FFFFFF>服务器性能状态</color>\n" +
                              $"<color=#CCCCCC>当前TPS:</color> <color={statusColor}>{currentTps:F1}</color>\n" +
                              $"<color=#CCCCCC>最大TPS:</color> <color=#FFFFFF>{maxTps:F1}</color>\n" +
                              $"<color=#CCCCCC>性能占比:</color> <color={statusColor}>{tpsPercentage:F1}%</color>\n" +
                              $"<color=#CCCCCC>状态:</color> <color={statusColor}>{statusText}</color>";

                    Logger.Debug($"玩家 {player.Nickname} 查询TPS - 当前: {currentTps:F1}, 最大: {maxTps:F1}, 占比: {tpsPercentage:F1}%");
                }
                else
                {
                    // 服务端控制台显示 - 纯文本格式
                    response = $"=== 服务器TPS状态 ===\n" +
                              $"当前TPS: {currentTps:F1}\n" +
                              $"最大TPS: {maxTps:F1}\n" +
                              $"性能占比: {tpsPercentage:F1}%\n" +
                              $"状态: {statusText}\n" +
                              $"检查时间: {DateTime.Now:HH:mm:ss}";

                    Logger.Info($"服务端控制台查询TPS - 当前: {currentTps:F1}, 最大: {maxTps:F1}, 占比: {tpsPercentage:F1}%");
                }
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取TPS信息失败: {ex.Message}");
                response = $"获取TPS信息失败: {ex.Message}";
                return false;
            }
        }
    }

    /// <summary>
    /// 详细TPS信息命令 - 管理员和服务端控制台可用
    /// </summary>
    [CommandHandler(typeof(RemoteAdminCommandHandler))]
    [CommandHandler(typeof(GameConsoleCommandHandler))]
    public class DetailedTpsCommand : ICommand
    {
        public string Command => "detailedtps";
        public string[] Aliases => new[] { "dtps", "tpsinfo" };
        public string Description => "查看详细的服务器TPS信息（管理员）";

        public bool Execute(ArraySegment<string> arguments, ICommandSender sender, out string response)
        {
            try
            {
                // 获取服务器TPS信息
                var currentTps = Server.Tps;
                var maxTps = Server.MaxTps;
                var tpsPercentage = maxTps > 0 ? (currentTps / maxTps) * 100 : 0;

                // 获取在线玩家数量
                var playerCount = Player.List.Count;

                // 计算性能等级
                string performanceGrade;
                if (tpsPercentage >= 95) performanceGrade = "A+";
                else if (tpsPercentage >= 90) performanceGrade = "A";
                else if (tpsPercentage >= 80) performanceGrade = "B";
                else if (tpsPercentage >= 70) performanceGrade = "C";
                else if (tpsPercentage >= 60) performanceGrade = "D";
                else performanceGrade = "F";

                // 性能建议
                string recommendation;
                if (tpsPercentage >= 90)
                {
                    recommendation = "服务器性能优秀，无需优化";
                }
                else if (tpsPercentage >= 70)
                {
                    recommendation = "服务器性能良好，可考虑轻微优化";
                }
                else if (tpsPercentage >= 50)
                {
                    recommendation = "服务器性能一般，建议检查插件和配置";
                }
                else
                {
                    recommendation = "服务器性能较差，需要立即优化";
                }

                response = $"=== 详细TPS报告 ===\n" +
                          $"当前TPS: {currentTps:F2}\n" +
                          $"最大TPS: {maxTps:F2}\n" +
                          $"性能占比: {tpsPercentage:F2}%\n" +
                          $"性能等级: {performanceGrade}\n" +
                          $"在线玩家: {playerCount}\n" +
                          $"建议: {recommendation}\n" +
                          $"检查时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                Logger.Info($"管理员查询详细TPS - 当前: {currentTps:F2}, 占比: {tpsPercentage:F2}%, 玩家数: {playerCount}");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取详细TPS信息失败: {ex.Message}");
                response = $"获取详细TPS信息失败: {ex.Message}";
                return false;
            }
        }
    }
}
