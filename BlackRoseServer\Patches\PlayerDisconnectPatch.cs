﻿using BlackRoseServer.Helper.Chat;
using BlackRoseServer.Helper.SCP;
using HarmonyLib;
using InventorySystem.Items.Coin;
using MEC;
using Mirror;
using PlayerRoles;
using PlayerStatsSystem;
using Logger = LabApi.Features.Console.Logger;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using UnityEngine;
using System.Collections.Concurrent;
using BlackRoseServer.Helper.Players;
using LabApi.Features.Wrappers;

namespace BlackRoseServer.Patches
{
    [HarmonyPatch(typeof(CustomNetworkManager), nameof(CustomNetworkManager.OnServerDisconnect))]
    public static class PlayerDisconnectPatch
    {
        private interface IDisconnectState
        {
            IDisconnectState Handle(DisconnectContext context);
        }

        private class InitialValidationState : IDisconnectState
        {
            public IDisconnectState Handle(DisconnectContext context)
            {

                    if (Round.Duration.TotalSeconds > 150)
                    {
                        context.SetResult(DisconnectProcessResult.RoundTimeExceeded);
                        return null;
                    }

                    if (!context.NetworkManager._disconnectDrop)
                    {
                        context.SetResult(DisconnectProcessResult.DisconnectDropDisabled);
                        return null;
                    }

                    NetworkIdentity identity = context.Connection.identity;
                    if (identity == null)
                    {
                        context.SetResult(DisconnectProcessResult.IdentityNotFound);
                        return null;
                    }

                    if (!ReferenceHub.TryGetHubNetID(identity.netId, out var hub))
                    {
                        context.SetResult(DisconnectProcessResult.HubNotFound);
                        return null;
                    }

                    if (!hub.IsAlive())
                    {
                        context.SetResult(DisconnectProcessResult.PlayerNotAlive);
                        return null;
                    }

                    context.Hub = hub;

                    return new SCPValidationState();
            }
        }

        private class SCPValidationState : IDisconnectState
        {
            public IDisconnectState Handle(DisconnectContext context)
            {
                    if (!context.Hub.IsSCP(false))
                    {
                        context.SetResult(DisconnectProcessResult.NotSCP);
                        return null;
                    }

                    Player player = Player.Get(context.Hub);
                    if (player == null)
                    {
                        context.SetResult(DisconnectProcessResult.PlayerNotFound);
                        return null;
                    }

                    context.Player = player;
                    context.UserId = player.UserId;

                    return new SCPDisconnectHandlingState();
            }
        }

        private class SCPDisconnectHandlingState : IDisconnectState
        {
            public IDisconnectState Handle(DisconnectContext context)
            {
                    var disconnectHelper = new DisconnectSCPHelper(context.Player);

                    Plugin.PlayerDataService.DisconnectSCPs.TryAdd(context.UserId, disconnectHelper);

                    Plugin.PlayerDataService.WaitingSCPs.TryAdd(context.UserId, new List<Player>());

                    Logger.Debug($"[PlayerDisconnectPatch] SCP断开连接: {context.UserId}, 角色: {context.Player.Role}");

                    string roleTranslation = ChatHelper.Instance.GetRoleTranslations().TryGetValue(context.Player.Role, out var translation) 
                        ? translation 
                        : context.Player.Role.ToString();
                    
                    Server.SendBroadcast($"<color=red>{roleTranslation}</color>掉线 输入.scp补位", 15, Broadcast.BroadcastFlags.Normal, true);

                    context.ScheduleDelayedReplacement();

                    context.Player.SetRole(RoleTypeId.Spectator, RoleChangeReason.RemoteAdmin);

                    context.SetResult(DisconnectProcessResult.Success);

                    NotifyObservers(new DisconnectEventArgs(context.Player, context.UserId, disconnectHelper));
                    
                    return null;
            }
        }

        private enum DisconnectProcessResult
        {
            NotProcessed,
            Success,
            RoundTimeExceeded,
            DisconnectDropDisabled,
            IdentityNotFound,
            HubNotFound,
            PlayerNotAlive,
            NotSCP,
            PlayerNotFound,
            Exception
        }

        private class DisconnectContext
        {
            public CustomNetworkManager NetworkManager { get; }
            public NetworkConnectionToClient Connection { get; }
            public ReferenceHub Hub { get; set; }
            public Player Player { get; set; }
            public string UserId { get; set; }

            public DisconnectProcessResult Result { get; private set; } = DisconnectProcessResult.NotProcessed;
            public Exception Exception { get; private set; }

            private CoroutineHandle _delayedReplacementHandle;

            public DisconnectContext(CustomNetworkManager networkManager, NetworkConnectionToClient connection)
            {
                NetworkManager = networkManager;
                Connection = connection;
            }

            public void SetResult(DisconnectProcessResult result, Exception exception = null)
            {
                Result = result;
                Exception = exception;

                if (result != DisconnectProcessResult.Success && result != DisconnectProcessResult.NotProcessed)
                {
                    Logger.Debug($"[PlayerDisconnectPatch] 断开连接处理结果: {result}");
                }
            }

            public void ScheduleDelayedReplacement()
            {
                _delayedReplacementHandle = Timing.CallDelayed(15f, () =>
                {
                        if (Plugin.PlayerDataService.DisconnectSCPs.TryGetValue(UserId, out var newSCP) &&
                            Plugin.PlayerDataService.WaitingSCPs.TryGetValue(UserId, out var waitingList) &&
                            waitingList.Count > 0)
                        {
                            Player targetPlayer = waitingList.RandomItem();
                            if (newSCP != null && targetPlayer != null)
                            {
                                newSCP.Reborn(targetPlayer);

                                waitingList.Remove(targetPlayer);
 
                                if (Plugin.PlayerDataService.DisconnectSCPs.IsEmpty)
                                {
                                    Plugin.PlayerDataService.WaitingSCPs.Clear();
                                }

                                NotifyReplacementObservers(new ReplacementEventArgs(targetPlayer, UserId, newSCP));
                            }
                        }
                });
            }

            public void CancelDelayedReplacement()
            {
                if (_delayedReplacementHandle.IsRunning)
                {
                    Timing.KillCoroutines(_delayedReplacementHandle);
                }
            }
        }

        private static class DisconnectHandlerFactory
        {
            public static DisconnectHandler Create(CustomNetworkManager networkManager, NetworkConnectionToClient connection)
            {
                return new DisconnectHandler(networkManager, connection);
            }
        }

        private class DisconnectHandler
        {
            private readonly DisconnectContext _context;
            private IDisconnectState _currentState;

            public DisconnectHandler(CustomNetworkManager networkManager, NetworkConnectionToClient connection)
            {
                _context = new DisconnectContext(networkManager, connection);
                _currentState = new InitialValidationState();
            }

            public DisconnectProcessResult Process()
            {
                while (_currentState != null)
                {
                    _currentState = _currentState.Handle(_context);
                }

                return _context.Result;
            }
        }

        public class DisconnectEventArgs : EventArgs
        {
            public Player Player { get; }
            public string UserId { get; }
            public DisconnectSCPHelper DisconnectHelper { get; }

            public DisconnectEventArgs(Player player, string userId, DisconnectSCPHelper disconnectHelper)
            {
                Player = player;
                UserId = userId;
                DisconnectHelper = disconnectHelper;
            }
        }

        public class ReplacementEventArgs : EventArgs
        {
            public Player TargetPlayer { get; }
            public string OriginalUserId { get; }
            public DisconnectSCPHelper DisconnectHelper { get; }

            public ReplacementEventArgs(Player targetPlayer, string originalUserId, DisconnectSCPHelper disconnectHelper)
            {
                TargetPlayer = targetPlayer;
                OriginalUserId = originalUserId;
                DisconnectHelper = disconnectHelper;
            }
        }

        public static event EventHandler<DisconnectEventArgs> OnSCPDisconnect;

        public static event EventHandler<ReplacementEventArgs> OnSCPReplacement;

        private static void NotifyObservers(DisconnectEventArgs args)
        {
            OnSCPDisconnect?.Invoke(null, args);
        }

        private static void NotifyReplacementObservers(ReplacementEventArgs args)
        {
            OnSCPReplacement?.Invoke(null, args);
        }

        public static bool Prefix(CustomNetworkManager __instance, NetworkConnectionToClient conn)
        {
            try
            {
                var handler = DisconnectHandlerFactory.Create(__instance, conn);

                var result = handler.Process();

                if (result == DisconnectProcessResult.Success)
                {
                    Logger.Debug("[PlayerDisconnectPatch] SCP断开连接处理成功");
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"[PlayerDisconnectPatch] 处理断开连接时发生异常: {ex.Message}");
                Logger.Debug(ex.StackTrace);
            }

            return true;
        }
    }
}
