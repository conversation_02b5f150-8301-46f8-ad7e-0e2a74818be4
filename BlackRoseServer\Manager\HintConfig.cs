using System;
using HintServiceMeow.Core.Enum;

namespace BlackRoseServer.Manager
{
    /// <summary>
    /// Hint类型枚举
    /// </summary>
    public enum HintType
    {
        /// <summary>
        /// 升级显示
        /// </summary>
        LevelUp,
        
        /// <summary>
        /// SCP-914启动信息
        /// </summary>
        SCP914Info,
        
        /// <summary>
        /// 电梯交互
        /// </summary>
        ElevatorInteraction,
        
        /// <summary>
        /// 保安下班提示
        /// </summary>
        SecurityOffDuty,
        
        /// <summary>
        /// 计时器显示
        /// </summary>
        Timer,
        
        /// <summary>
        /// 经验进度条
        /// </summary>
        ExperienceBar,
        
        /// <summary>
        /// 聊天信息
        /// </summary>
        Chat,
        
        /// <summary>
        /// 排行榜信息
        /// </summary>
        Leaderboard,
        
        /// <summary>
        /// 观察者信息
        /// </summary>
        Spectator,
        
        /// <summary>
        /// 通用通知
        /// </summary>
        Notification,
        
        /// <summary>
        /// 警告信息
        /// </summary>
        Warning,
        
        /// <summary>
        /// 错误信息
        /// </summary>
        Error,
        
        /// <summary>
        /// 成功信息
        /// </summary>
        Success,
        
        /// <summary>
        /// 自定义
        /// </summary>
        Custom,

        /// <summary>
        /// 简单经验显示
        /// </summary>
        SimpleExperience,

        /// <summary>
        /// SCP队友信息
        /// </summary>
        SCPTeammate,

        /// <summary>
        /// SCP-079信息
        /// </summary>
        SCP079Info
    }

    /// <summary>
    /// Hint优先级枚举
    /// </summary>
    public enum HintPriority
    {
        /// <summary>
        /// 低优先级
        /// </summary>
        Low = 0,
        
        /// <summary>
        /// 普通优先级
        /// </summary>
        Normal = 1,
        
        /// <summary>
        /// 高优先级
        /// </summary>
        High = 2,
        
        /// <summary>
        /// 紧急优先级
        /// </summary>
        Critical = 3
    }

    /// <summary>
    /// Hint生命周期类型
    /// </summary>
    public enum HintLifecycleType
    {
        /// <summary>
        /// 持久化显示，需要手动移除
        /// </summary>
        Persistent,
        
        /// <summary>
        /// 临时显示，自动移除
        /// </summary>
        Temporary,
        
        /// <summary>
        /// 自动文本更新
        /// </summary>
        AutoUpdate
    }

    /// <summary>
    /// Hint配置类
    /// </summary>
    public class HintConfig
    {
        /// <summary>
        /// Hint类型
        /// </summary>
        public HintType Type { get; set; } = HintType.Custom;
        
        /// <summary>
        /// 显示文本
        /// </summary>
        public string Text { get; set; } = string.Empty;
        
        /// <summary>
        /// 对齐方式
        /// </summary>
        public HintAlignment Alignment { get; set; } = HintAlignment.Center;
        
        /// <summary>
        /// X坐标（水平位置）
        /// </summary>
        public float XCoordinate { get; set; } = 0f;

        /// <summary>
        /// Y坐标
        /// </summary>
        public int YCoordinate { get; set; } = 500;
        
        /// <summary>
        /// 字体大小
        /// </summary>
        public int FontSize { get; set; } = 20;
        
        /// <summary>
        /// 行高
        /// </summary>
        public int LineHeight { get; set; } = 5;
        
        /// <summary>
        /// 同步速度
        /// </summary>
        public HintSyncSpeed SyncSpeed { get; set; } = HintSyncSpeed.Normal;
        
        /// <summary>
        /// 优先级
        /// </summary>
        public HintPriority Priority { get; set; } = HintPriority.Normal;
        
        /// <summary>
        /// 生命周期类型
        /// </summary>
        public HintLifecycleType LifecycleType { get; set; } = HintLifecycleType.Temporary;
        
        /// <summary>
        /// 显示持续时间（秒），仅对Temporary类型有效
        /// </summary>
        public float Duration { get; set; } = 5f;
        
        /// <summary>
        /// 延迟显示时间（秒）
        /// </summary>
        public float DelayTime { get; set; } = 0f;
        
        /// <summary>
        /// 是否允许重叠显示
        /// </summary>
        public bool AllowOverlap { get; set; } = false;
        
        /// <summary>
        /// 自定义标识符，用于管理和移除
        /// </summary>
        public string CustomId { get; set; } = string.Empty;
        
        /// <summary>
        /// 自动文本更新函数（仅对AutoUpdate类型有效）
        /// </summary>
        public Func<string> AutoTextFunction { get; set; }
        
        /// <summary>
        /// 创建默认配置
        /// </summary>
        public static HintConfig Default => new HintConfig();
        
        /// <summary>
        /// 克隆配置
        /// </summary>
        public HintConfig Clone()
        {
            return new HintConfig
            {
                Type = this.Type,
                Text = this.Text,
                Alignment = this.Alignment,
                XCoordinate = this.XCoordinate,
                YCoordinate = this.YCoordinate,
                FontSize = this.FontSize,
                LineHeight = this.LineHeight,
                SyncSpeed = this.SyncSpeed,
                Priority = this.Priority,
                LifecycleType = this.LifecycleType,
                Duration = this.Duration,
                DelayTime = this.DelayTime,
                AllowOverlap = this.AllowOverlap,
                CustomId = this.CustomId,
                AutoTextFunction = this.AutoTextFunction
            };
        }
    }

    /// <summary>
    /// Hint层级定义
    /// </summary>
    public static class HintLayers
    {
        /// <summary>
        /// 顶层 - 紧急信息
        /// </summary>
        public const int Critical = 50;

        /// <summary>
        /// SCP信息层 - SCP队友和079信息
        /// </summary>
        public const int SCPInfo = 100;

        /// <summary>
        /// 计时器层
        /// </summary>
        public const int Timer = 105;
        
        /// <summary>
        /// 升级显示层
        /// </summary>
        public const int LevelUp = 200;
        
        /// <summary>
        /// 聊天信息层
        /// </summary>
        public const int Chat = 250;
        
        /// <summary>
        /// 排行榜信息层
        /// </summary>
        public const int Leaderboard = 300;
        
        /// <summary>
        /// 通知层
        /// </summary>
        public const int Notification = 400;
        
        /// <summary>
        /// 交互信息层
        /// </summary>
        public const int Interaction = 800;

        /// <summary>
        /// 经验增加提示层
        /// </summary>
        public const int ExperienceGain = 1000;

        /// <summary>
        /// 经验进度条层
        /// </summary>
        public const int ExperienceBar = 1100;
        
        /// <summary>
        /// 观察者信息层
        /// </summary>
        public const int Spectator = 20;
    }
}
