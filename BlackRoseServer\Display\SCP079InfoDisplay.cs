using System;
using System.Collections.Generic;
using System.Linq;
using LabApi.Features.Wrappers;
using LabApi.Features.Console;
using BlackRoseServer.Manager;
using PlayerRoles;
using UnityEngine;
using Logger = LabApi.Features.Console.Logger;

namespace BlackRoseServer.Display
{
    /// <summary>
    /// SCP-079专用信息显示组件
    /// </summary>
    public class SCP079InfoDisplay
    {
        /// <summary>
        /// 激活的电板信息
        /// </summary>
        public class ActivatedGenerator
        {
            public Vector3 Position { get; set; }
            public string LocationName { get; set; }
            public DateTime ActivatedTime { get; set; }
            public bool IsActive { get; set; } = true;
        }

        private readonly Dictionary<int, ActivatedGenerator> _activatedGenerators;
        private readonly object _syncLock = new object();

        /// <summary>
        /// 构造函数
        /// </summary>
        public SCP079InfoDisplay()
        {
            _activatedGenerators = new Dictionary<int, ActivatedGenerator>();
        }

        /// <summary>
        /// 显示SCP-079信息
        /// </summary>
        /// <param name="player">SCP-079玩家</param>
        public void ShowSCP079Info(Player player)
        {
            if (player == null || player.Role != RoleTypeId.Scp079)
                return;

            try
            {
                var infoText = BuildSCP079InfoText(player);
                
                var config = new HintConfig
                {
                    Type = HintType.Custom,
                    Text = infoText,
                    YCoordinate = HintLayers.SCPInfo, // 使用SCP信息层级
                    FontSize = 18,
                    Alignment = HintServiceMeow.Core.Enum.HintAlignment.Right,
                    LifecycleType = HintLifecycleType.Persistent,
                    Priority = HintPriority.Normal,
                    CustomId = $"{player.UserId}_scp079_info",
                    AllowOverlap = true // 允许重叠显示
                };

                // 直接显示信息，不移除旧的
                HintManager.Instance.ShowHint(player, config);
            }
            catch (Exception ex)
            {
                Logger.Error($"显示SCP-079信息失败: {ex.Message}");
                Logger.Debug($"ShowSCP079Info详细错误: {ex}");
            }
        }

        /// <summary>
        /// 构建SCP-079信息文本
        /// </summary>
        /// <param name="player">SCP-079玩家</param>
        /// <returns>信息文本</returns>
        private string BuildSCP079InfoText(Player player)
        {
            try
            {
                var infoLines = new List<string>();

                // 获取SCP-079基本信息
                var energy = GetSCP079Energy(player);
                var level = GetSCP079Level(player);
                var activeGenerators = GetActiveGeneratorCount();

                // 基本信息
                infoLines.Add($"<color=#FF6600><b>SCP-079 信息</b></color>");
                infoLines.Add($"<color=#00FFFF>电量: {energy:F0}%</color>");
                infoLines.Add($"<color=#FFFF00>等级: {level}</color>");
                infoLines.Add($"<color=#00FF00>激活电板: {activeGenerators}</color>");

                // 激活的电板位置信息
                lock (_syncLock)
                {
                    var activeGens = _activatedGenerators.Values.Where(g => g.IsActive).ToList();
                    if (activeGens.Count > 0)
                    {
                        infoLines.Add($"<color=#90EE90>电板位置:</color>");
                        foreach (var gen in activeGens.Take(5)) // 最多显示5个
                        {
                            var timeActive = DateTime.Now - gen.ActivatedTime;
                            var timeStr = timeActive.TotalMinutes < 1 
                                ? $"{timeActive.Seconds}秒前" 
                                : $"{(int)timeActive.TotalMinutes}分钟前";
                            
                            infoLines.Add($"  <color=#87CEEB>{gen.LocationName}</color> <color=#D3D3D3>({timeStr})</color>");
                        }
                        
                        if (activeGens.Count > 5)
                        {
                            infoLines.Add($"  <color=#D3D3D3>...还有{activeGens.Count - 5}个</color>");
                        }
                    }
                }

                return string.Join("\n", infoLines);
            }
            catch (Exception ex)
            {
                Logger.Error($"构建SCP-079信息文本失败: {ex.Message}");
                return "<color=#FF0000>信息获取失败</color>";
            }
        }

        /// <summary>
        /// 获取SCP-079电量百分比
        /// </summary>
        /// <param name="player">SCP-079玩家</param>
        /// <returns>电量百分比</returns>
        private float GetSCP079Energy(Player player)
        {
            try
            {
                // 尝试获取SCP-079的电量信息
                // 这里需要根据实际的API来获取电量
                // 暂时返回模拟数据
                if (player.ReferenceHub?.roleManager?.CurrentRole is PlayerRoles.PlayableScps.Scp079.Scp079Role scp079Role)
                {
                    // 获取实际的电量信息
                    var currentEnergy = scp079Role.SubroutineModule.TryGetSubroutine<PlayerRoles.PlayableScps.Scp079.Scp079AuxManager>(out var auxManager) 
                        ? auxManager.CurrentAux 
                        : 0f;
                    var maxEnergy = auxManager?.MaxAux ?? 100f;
                    
                    return (currentEnergy / maxEnergy) * 100f;
                }
                
                return 0f;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取SCP-079电量失败: {ex.Message}");
                return 0f;
            }
        }

        /// <summary>
        /// 获取SCP-079等级
        /// </summary>
        /// <param name="player">SCP-079玩家</param>
        /// <returns>等级</returns>
        private int GetSCP079Level(Player player)
        {
            try
            {
                // 尝试获取SCP-079的等级信息
                if (player.ReferenceHub?.roleManager?.CurrentRole is PlayerRoles.PlayableScps.Scp079.Scp079Role scp079Role)
                {
                    // 获取实际的等级信息
                    if (scp079Role.SubroutineModule.TryGetSubroutine<PlayerRoles.PlayableScps.Scp079.Scp079TierManager>(out var tierManager))
                    {
                        return tierManager.AccessTierLevel;
                    }
                }
                
                return 1;
            }
            catch (Exception ex)
            {
                Logger.Error($"获取SCP-079等级失败: {ex.Message}");
                return 1;
            }
        }

        /// <summary>
        /// 获取激活的电板数量
        /// </summary>
        /// <returns>激活的电板数量</returns>
        private int GetActiveGeneratorCount()
        {
            try
            {
                lock (_syncLock)
                {
                    return _activatedGenerators.Values.Count(g => g.IsActive);
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取激活电板数量失败: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// 记录电板激活
        /// </summary>
        /// <param name="generatorId">电板ID</param>
        /// <param name="position">电板位置</param>
        public void RecordGeneratorActivated(int generatorId, Vector3 position)
        {
            try
            {
                var locationName = GetLocationName(position);
                
                lock (_syncLock)
                {
                    _activatedGenerators[generatorId] = new ActivatedGenerator
                    {
                        Position = position,
                        LocationName = locationName,
                        ActivatedTime = DateTime.Now,
                        IsActive = true
                    };
                }

                Logger.Debug($"记录电板激活 - ID: {generatorId}, 位置: {locationName}");
            }
            catch (Exception ex)
            {
                Logger.Error($"记录电板激活失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录电板取消激活
        /// </summary>
        /// <param name="generatorId">电板ID</param>
        public void RecordGeneratorDeactivated(int generatorId)
        {
            try
            {
                lock (_syncLock)
                {
                    if (_activatedGenerators.TryGetValue(generatorId, out var generator))
                    {
                        generator.IsActive = false;
                        // 保留位置信息，但标记为非激活状态
                        Logger.Debug($"记录电板取消激活 - ID: {generatorId}, 位置: {generator.LocationName}");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"记录电板取消激活失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据位置获取位置名称
        /// </summary>
        /// <param name="position">位置坐标</param>
        /// <returns>位置名称</returns>
        private string GetLocationName(Vector3 position)
        {
            try
            {
                var zone = ZoneDetector.GetZoneByPosition(position);
                var zoneName = ZoneDetector.GetZoneName(zone);
                
                // 可以根据具体位置进一步细化位置名称
                // 这里简化为区域名称
                return $"{zoneName}区域";
            }
            catch (Exception ex)
            {
                Logger.Error($"获取位置名称失败: {ex.Message}");
                return "未知位置";
            }
        }

        /// <summary>
        /// 清理过期的电板记录
        /// </summary>
        public void CleanupExpiredRecords()
        {
            try
            {
                var cutoffTime = DateTime.Now.AddMinutes(-30); // 30分钟前的记录
                
                lock (_syncLock)
                {
                    var expiredIds = _activatedGenerators
                        .Where(kvp => !kvp.Value.IsActive && kvp.Value.ActivatedTime < cutoffTime)
                        .Select(kvp => kvp.Key)
                        .ToList();

                    foreach (var id in expiredIds)
                    {
                        _activatedGenerators.Remove(id);
                    }

                    if (expiredIds.Count > 0)
                    {
                        Logger.Debug($"清理了 {expiredIds.Count} 个过期的电板记录");
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"清理过期电板记录失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取所有激活的电板信息
        /// </summary>
        /// <returns>激活的电板信息列表</returns>
        public List<ActivatedGenerator> GetActivatedGenerators()
        {
            try
            {
                lock (_syncLock)
                {
                    return _activatedGenerators.Values.Where(g => g.IsActive).ToList();
                }
            }
            catch (Exception ex)
            {
                Logger.Error($"获取激活电板信息失败: {ex.Message}");
                return new List<ActivatedGenerator>();
            }
        }

        /// <summary>
        /// 清理所有记录
        /// </summary>
        public void ClearAllRecords()
        {
            try
            {
                lock (_syncLock)
                {
                    _activatedGenerators.Clear();
                }
                Logger.Debug("已清理所有电板记录");
            }
            catch (Exception ex)
            {
                Logger.Error($"清理所有电板记录失败: {ex.Message}");
            }
        }
    }
}
